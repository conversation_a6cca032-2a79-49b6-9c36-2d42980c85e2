<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 26.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 311 201" style="enable-background:new 0 0 311 201;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;stroke:#422063;stroke-width:1.5;stroke-miterlimit:10;}
	.st1{fill:none;stroke:#422063;stroke-width:1.5;stroke-linecap:round;stroke-miterlimit:10;}
	.st2{fill:#E8E8E8;stroke:#7F7175;stroke-miterlimit:10;}
	.st3{fill:#DFF1F0;stroke:#7F7175;stroke-miterlimit:10;}
	.st4{fill:#D8D3E1;stroke:#7F7175;stroke-miterlimit:10;}
	.st5{fill:#DCE8EF;stroke:#7F7F7F;stroke-miterlimit:10;}
	.st6{fill:#FFFFFF;}
	.st7{fill:#DCEDDD;stroke:#7F7F7F;stroke-miterlimit:10;}
	.st8{fill:none;stroke:#FFFFFF;stroke-width:6;stroke-miterlimit:10;}
	.st9{fill:none;stroke:#FFFFFF;stroke-width:6;stroke-linecap:round;stroke-miterlimit:10;}
	.st10{fill:none;stroke:#FFFFFF;stroke-width:6;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st11{fill:none;stroke:#FFFFFF;stroke-width:5;stroke-miterlimit:10;}
	.st12{fill:none;stroke:#FFFFFF;stroke-width:5;stroke-linecap:round;stroke-miterlimit:10;}
</style>
<symbol  id="Plus_ICon" viewBox="-13.25 -13.25 26.5 26.5">
	<circle class="st0" cx="0" cy="0" r="12.5"/>
	<line class="st1" x1="0" y1="7.35" x2="0" y2="-7.35"/>
	<line class="st1" x1="-7.35" y1="0" x2="7.35" y2="0"/>
</symbol>
<rect x="55.5" y="-54.5" transform="matrix(6.123234e-17 -1 1 6.123234e-17 55 256)" class="st7" width="200" height="310"/>
<use xlink:href="#Plus_ICon"  width="26.5" height="26.5" x="-13.25" y="-13.25" transform="matrix(1 0 0 -1 286.0633 179.8956)" style="overflow:visible;"/>
<g>
	<path class="st8" d="M124.33,150.85c29.43,0,45.54-4.9,49.04-20.08c3.5-15.18,0.46-43.52-5.37-62.12
		c-6.07-19.38-25.65-30.83-45.26-30.83S83.54,49.25,77.47,68.64c-5.83,18.61-8.87,46.94-5.37,62.12s19.62,20.08,49.04,20.08H124.33z
		"/>
	<path class="st8" d="M210.12,161.82c0,0,8.17,0.23,12.38,0c4.2-0.23,9.11-3.74,12.14-8.64s3.27-21.49-0.23-31.76
		c-3.5-10.28-12.61-18.92-24.29-18.92c-11.68,0-20.79,8.64-24.29,18.92c-3.5,10.28-3.27,26.86-0.23,31.76
		c3.04,4.9,7.94,8.41,12.14,8.64S210.12,161.82,210.12,161.82z"/>
	<line class="st9" x1="70.23" y1="177" x2="244.45" y2="177"/>
	<line class="st8" x1="122.78" y1="176.77" x2="122.78" y2="150.85"/>
	<line class="st8" x1="210.82" y1="177" x2="210.82" y2="161.82"/>
	<path class="st9" d="M195.41,133.56c2.1,11.44,26.62,10.98,28.49,0"/>
	<path class="st10" d="M86.11,111.86c1.86,14.29,23.57,13.71,25.22,0c1.86,14.29,23.57,13.71,25.22,0
		c1.86,14.29,23.57,13.71,25.22,0"/>
	<path class="st10" d="M94.52,78.68c2.1,14.29,26.74,14,28.61,0.29c2.1,14.29,26.74,14,28.61,0.29"/>
</g>
</svg>
