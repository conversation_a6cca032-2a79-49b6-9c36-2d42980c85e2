<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 20.1 14" style="enable-background:new 0 0 20.1 14;" xml:space="preserve">
<style type="text/css">
	.st0{filter:url(#Adobe_OpacityMaskFilter);}
	.st1{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st2{mask:url(#mask-2_1_);fill-rule:evenodd;clip-rule:evenodd;fill:#2C1851;}
	.st3{filter:url(#Adobe_OpacityMaskFilter_1_);}
	.st4{mask:url(#mask-4_1_);fill-rule:evenodd;clip-rule:evenodd;fill:#2C1851;}
	.st5{fill:#B4B4B4;}
	.st6{fill:#A2A2A2;}
</style>
<g id="Icon-_x2F_-hamburger_2_" transform="translate(53.000000, 3.000000)">
	<g id="Line-4_2_">
		<path class="st5" d="M-33.9-1H-52c-0.6,0-1-0.4-1-1s0.4-1,1-1h18.1c0.6,0,1,0.4,1,1S-33.3-1-33.9-1z"/>
	</g>
	<g id="Line-4-Copy_2_">
		<path class="st5" d="M-33.9,5H-52c-0.6,0-1-0.4-1-1s0.4-1,1-1h18.1c0.6,0,1,0.4,1,1S-33.3,5-33.9,5z"/>
	</g>
	<g id="Line-4-Copy-2_2_">
		<path class="st5" d="M-33.9,11H-52c-0.6,0-1-0.4-1-1s0.4-1,1-1h18.1c0.6,0,1,0.4,1,1S-33.3,11-33.9,11z"/>
	</g>
</g>
</svg>
