import React, { Component } from "react";
import { Switch, Route } from "react-router-dom";
import Logged_In_Header from "../components/Logged_In_header/Logged_In_header";
import SEARCH_BAR from "../components/Logged_In_header/advanced_search";

import EditProfile from "../components/company_wizard/edit_profile";
import EditOfficeShot from "../components/our_office/our_office";
import ProfilePage from "../components/profile_page/profile_container";
import Footer from "../components/Footer/Footer";

function Home(props) {
  return (
    <>
      {/* <Logged_In_Header path={props.location.pathname} /> */}
      <SEARCH_BAR path={props.location.pathname} />
      <Switch>
        <Route
          exact
          path={`${props.match.path}/edit_profile`}
          component={EditProfile}
        />
        <Route
          exact
          path={`${props.match.path}/edit_office`}
          component={EditOfficeShot}
        />
        <Route
          exact
          path={`${props.match.path}/profile`}
          component={ProfilePage}
        />
      </Switch>
      <Footer />
    </>
  );
}

export default Home;
