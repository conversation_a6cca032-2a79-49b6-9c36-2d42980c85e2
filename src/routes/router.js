import React from "react";
import { <PERSON>rowser<PERSON>outer, Route, Switch, Redirect } from "react-router-dom";
// import { useRouter<PERSON>istory } from "react-router";

import { useSelector } from "react-redux";
import { get } from "lodash";

import Default_Landing from "../pages/layouts/Default_Langing";
import Home from "./home";
import { Loading } from "../components/common";
import AppRouting from "./app-routing";
import PrivateRoute from "./private_route";
import Gamification from "../components/gamification/gamification";
import upgradeLevel from "../components/modals/upgardeLevel";
import MessagePing from "../components/modals/message_ping";
import {
  BASEURL,
  SPACK_DEV_URL,
  SPACK_QA_URL,
  SPACK_STAGE_URL,
} from "../constants";
import NewJoinModal from "../components/modals/new_joinModal";
import NewCompanyModal from "../components/modals/new_companyModal";
import CompanyAlready from "../components/modals/companyAlready";
import ProfilePage from "../components/profile_page/profile_container";
import Logged_In_Header from "../components/Logged_In_header/Logged_In_header";
import LoggedInHeader from "../components/Logged_In_header/landingHeader";
import CurrentlyLoggedUser from "../components/modals/currentlyLoggedUser";
import NewMember from "../components/company_wizard/businessCard_steps/new_member";
import public_view_modal from "../components/modals/public_view_modal";
import new_company_confirm_modal from "../components/modals/new_company_confirm_modal";
import LandingLoading from "../components/common/landing_loading";
import DefaultRedirect from "../pages/layouts/DefaultRedirect";
import GlobalSearch from "../pages/global_search/GlobalSearch";

export default function Router(props) {
  const loading = useSelector((state) => state.Api.loading);
  const horseLoading = useSelector((state) => state.Api.horseLoading);

  return (
    <BrowserRouter basename={process.env.PUBLIC_URL}>
      <Loading loading={loading} />
      <LandingLoading loading={horseLoading} />
      <LoggedInHeader />
      <Switch>
        <PrivateRoute
          exact
          path="/"
          component={(props) => {
            let host = get(window.location, "host", "");
            if (host) {
              if (host.includes("localhost")) {
                host = "localhost:3000";
                window.location.href = `http://${host}/`;
              } else {
                if (host.includes("-dev-")) {
                  window.location.href = `https://${SPACK_DEV_URL}/`;
                }
                if (host.includes("-qa-")) {
                  window.location.href = `https://${SPACK_QA_URL}/`;
                }
                if (host.includes("-stage-")) {
                  window.location.href = `https://${SPACK_STAGE_URL}/`;
                } else {
                  window.location.href = `${BASEURL.URL}`;
                }
              }
            }
            return null;
          }}
        />
        <PrivateRoute exact path="/cpack/:token" component={AppRouting} />
        <PrivateRoute
          exact
          path="/cpack/emailauth/:token"
          component={AppRouting}
        />
        <PrivateRoute exact path="/emailauth/:token" component={AppRouting} />
        <Route exact path="/" component={Default_Landing} />
        <Route
          exact
          path="/redirect/:token/:companyId"
          component={DefaultRedirect}
        />
        <PrivateRoute exact path="/wizard" component={Default_Landing} />
        <PrivateRoute exact path="/global-search" component={GlobalSearch} />
        <PrivateRoute
          exact
          path="/company/public/:username"
          component={ProfilePage}
        />
        <PrivateRoute
          exact
          path="/company/public-display/:username"
          component={ProfilePage}
        />
        <PrivateRoute
          path="/twc"
          state={{ from: props.location }}
          component={Home}
        />
        <PrivateRoute
          path="/gamification"
          state={{ from: props.location }}
          component={Gamification}
        />
        <PrivateRoute
          path="/CurrentlyLoggedUser"
          state={{ from: props.location }}
          component={CurrentlyLoggedUser}
        />
        <PrivateRoute
          path="/upgradeLevel"
          state={{ from: props.location }}
          component={upgradeLevel}
        />
        {/* for ui testing remove if functionality is done */}
        <PrivateRoute
          path="/NewJoinModal"
          state={{ from: props.location }}
          component={NewJoinModal}
        />
        <PrivateRoute
          path="/NewCompanyModal"
          state={{ from: props.location }}
          component={NewCompanyModal}
        />
        <PrivateRoute
          path="/CompanyAlready"
          state={{ from: props.location }}
          component={CompanyAlready}
        />
        <PrivateRoute
          path="/thankMail/:email/:token"
          state={{ from: props.location }}
          component={MessagePing}
        />
        <PrivateRoute
          path="/newMember"
          state={{ from: props.location }}
          component={NewMember}
        />
        <PrivateRoute
          path="/publicModal"
          state={{ from: props.location }}
          component={public_view_modal}
        />
        <PrivateRoute
          path="/companyConfirm"
          state={{ from: props.location }}
          component={new_company_confirm_modal}
        />
        <Redirect from="*" to="/" />
      </Switch>
    </BrowserRouter>
  );
}
