import _ from "lodash";
import React, { Component } from "react";
import { Redirect, Route } from "react-router-dom";

class PrivateRoute extends Component {
  render() {
    const isLoggedIn = localStorage.getItem("tradeworks_user_token");
    const { component: Component, ...rest } = this.props;
    return (
      <Route
        {...rest}
        render={(props) => {
          if (isLoggedIn) {
            return <Component {...props} />;
          } else {
            if (
              props.location.pathname === "/" ||
              props.match.path === "/cpack/:token" ||
              props.match.path === "/cpack/emailauth/:token" ||
              props.match.path === "/emailauth/:token" ||
              props.match.path === "/company/public/:username"
            ) {
              return <Component {...props} />;
            } else {
              //If not loggedin redirect to initial screen
              return (
                <Redirect
                  to={{
                    pathname: "/",
                    state: { from: props.location },
                  }}
                />
              );
            }
          }
        }}
      />
    );
  }
}

export default PrivateRoute;
