import React, { useEffect } from "react";
import { get } from "lodash";
import { styled } from "@material-ui/core/styles";
import { useHistory } from "react-router-dom";

const Loading = ({ loading, match, location }) => {
  let history = useHistory();

  useEffect(() => {
    localStorage.setItem("tradeworks_user_token", match.params.token);
    const userDetails = JSON.parse(localStorage.getItem("profile"));
    const userProfile = get(userDetails, "user");
    const hasProfile = get(userProfile, "hasCpackProfile", false);
    if (hasProfile) {
      history.push("/twc/profile");
    } else {
      history.push("/wizard");
    }
  }, []);
  return (
    <LoadingWheel>
      <img src="assets/images/loading.gif" />
    </LoadingWheel>
  );
};

const LoadingWheel = styled("div")({
  position: "fixed",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  width: "100%",
  height: "100%",
  opacity: 0.8,
  zIndex: 2000,
});

export default Loading;
