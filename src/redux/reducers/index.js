import { combineReducers } from "redux";
import ApiReducer from "./api.reducer";
import LoginReducer from "./login_reducer";
import ProfileReducer from "./profile_reducer";
import ConfigReducer from "./configs_reducer";
import { LoginTypes } from "../actionTypes";
const appReducer = combineReducers({
  Api: ApiReducer,
  Login: LoginReducer,
  Profile: ProfileReducer,
  Configs: ConfigReducer,
});
const RootReducer = (state, action) => {
  if (action.type === LoginTypes.LOGOUT) {
    localStorage.clear();
    state = undefined;
  }
  return appReducer(state, action);
};

export default RootReducer;
