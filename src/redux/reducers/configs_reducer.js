import { ConfigTypes } from "../actionTypes";

const initialState = {
  config: null,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case ConfigTypes.GET_CONFIGS: {
      return {
        ...state,
        config: action.config,
      };
    }
    case ConfigTypes.FETCHED_COUNTRIES: {
      return {
        ...state,
        countries: action.data,
        defaultCountry: action.defaultSelectedCountry,
      };
    }
    case ConfigTypes.GET_ALL_COUNTRIES: {
      return {
        ...state,
        countries_flags: [...action.payload.countryList],
      };
    }
    default:
      return state;
  }
};
