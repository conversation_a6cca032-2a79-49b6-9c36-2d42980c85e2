import _ from "lodash";

import { ProfileTypes } from "../actionTypes";
import { ProfileActions } from "../actions";
const initialState = {
  config: null,
  // businesscard: {},
};

export default (state = initialState, action) => {
  switch (action.type) {
    case ProfileTypes.FETCH_ALL_TRADES: {
      return {
        ...state,
        trades: [...action.payload],
      };
    }
    case ProfileTypes.GET_PROFILE_DATA: {
      return {
        ...state,
        [_.toLower(action.typeOf)]: action.payload,
      };
    }
    case ProfileTypes.GET_PROFILE_DATA_INFO: {
      return {
        ...state,
        profileUserData: action.payload,
      };
    }

    case ProfileTypes.FETCH_ACCOLADES: {
      return {
        ...state,
        accolades: action.payload,
      };
    }

    case ProfileTypes.GET_COLLEGE_ALLEGIANCES: {
      return {
        ...state,
        collegeAllegiances: action.payload,
      };
    }

    case ProfileTypes.GET_COMPANY_VIBES: {
      return {
        ...state,
        companyVibes: action.payload,
      };
    }

    case ProfileTypes.GET_CLIENTELE_DATA: {
      return {
        ...state,
        clientele: action.payload,
      };
    }

    case ProfileTypes.GET_PROCLIVITIES_DATA: {
      return {
        ...state,
        proclivities: action.payload,
      };
    }

    case ProfileTypes.UPDATE_BUSINESSCARD_DATA: {
      const headQuarterObj = _.find(action.payload.businessCard, {
        isHeadOffice: true,
      });
      if (headQuarterObj) {
        localStorage.setItem("companyId", headQuarterObj.companyId);
        localStorage.setItem("newCompanyId", headQuarterObj.companyId);
        localStorage.setItem(
          "companyName",
          headQuarterObj.companyName || headQuarterObj.name
        );
      }
      return {
        ...state,
        refBusinessCard: action.payload,
      };
    }

    case ProfileTypes.FETCH_FEEDS: {
      return {
        ...state,
        [action.typeOf]: action.payload,
      };
    }

    case ProfileTypes.GET_VENUES: {
      return {
        ...state,
        venues: action.payload,
      };
    }

    case ProfileTypes.COMPANY_MEMBERS: {
      return {
        ...state,
        companyMembersList: action.payload,
      };
    }

    case ProfileTypes.COMPANY_DATA_BY_USER: {
      return {
        ...state,
        CompanyListByUser: [...action.payload],
      };
    }

    case ProfileTypes.GET_OPEN_POSITIONS: {
      return {
        ...state,
        openPositions: [...action.payload],
      };
    }

    case ProfileTypes.COMPANY_LIST: {
      return {
        ...state,
        companiesList: [...action.payload],
      };
    }

    case ProfileTypes.COMPANY_BASIC_LIST: {
      return {
        ...state,
        companyBasicList: [...action.payload],
      };
    }

    case ProfileTypes.GET_JOB_DATA: {
      return {
        ...state,
        jobInfoData: action.payload,
      };
    }

    case ProfileTypes.JOB_LISTING: {
      return {
        ...state,
        jobListing: action.data.data,
      };
    }

    case ProfileTypes.TOP_LOCATIONS: {
      return {
        ...state,
        topLocations: action.payload,
      };
    }

    case ProfileTypes.INTERVIEW_INFO: {
      return {
        ...state,
        interviewInfo: action.payload,
      };
    }
    case ProfileTypes.LOGOUT_IMPERSONATE: {
      return {
        ...state,
        impersonateDetails: action.payload,
      };
    }
    case ProfileTypes.INTERVIEW_BANNER_DATA: {
      return {
        ...state,
        interviewBannersData: action.payload,
      };
    }
    case ProfileTypes.SEARCH_IN_ALL: {
      return {
        ...state,
        searchInAll: { ...action.payload },
      };
    }

    default:
      return state;
  }
};
