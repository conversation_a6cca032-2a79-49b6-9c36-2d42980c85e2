import _ from "lodash";

import Api, { profileStepperApi } from "../api";
import { ProfileTypes, ConfigTypes } from "../actionTypes";
import { ApiActions } from ".";
import { URI, PROFILE_TYPES } from "../../constants";

export default {
  fetchAllTrades: () => {
    return async (dispatch) => {
      try {
        const res = await profileStepperApi.getAllTrades();
        const {
          data: { data },
        } = res;
        dispatch({
          type: ProfileTypes.FETCH_ALL_TRADES,
          payload: data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchAccolades: () => {
    return async (dispatch) => {
      try {
        const res = await profileStepperApi.fetchAccolades();
        const {
          data: { data },
        } = res;
        dispatch({
          type: ProfileTypes.FETCH_ACCOLADES,
          payload: data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchCompanyVibe: () => {
    return async (dispatch) => {
      try {
        const res = await profileStepperApi.fetchCompanyVibe();
        const {
          data: { data },
        } = res;
        dispatch({
          type: ProfileTypes.GET_COMPANY_VIBES,
          payload: data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchCollegeAllegiances: (name) => {
    return async (dispatch) => {
      try {
        const res = await profileStepperApi.fetchCollegeAllegiances(name);
        const {
          data: { data },
        } = res;
        dispatch({
          type: ProfileTypes.GET_COLLEGE_ALLEGIANCES,
          payload: data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchClienteleData: () => {
    return async (dispatch) => {
      try {
        const res = await profileStepperApi.fetchClienteleData();
        const {
          data: { data },
        } = res;
        dispatch({
          type: ProfileTypes.GET_CLIENTELE_DATA,
          payload: data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchProclivitiesData: () => {
    return async (dispatch) => {
      try {
        const res = await profileStepperApi.fetchProclivitiesData();
        const {
          data: { data },
        } = res;
        dispatch({
          type: ProfileTypes.GET_PROCLIVITIES_DATA,
          payload: data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getProfileDataOf: (types) => {
    types = _.isArray(types) ? types : [types];
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        for (let i = 0; i < types.length; i++) {
          const type = types[i];
          const res = await profileStepperApi.getProfileDataOf(type);
          dispatch({
            type: ProfileTypes.GET_PROFILE_DATA,
            typeOf: type,
            payload: res.data.data,
          });
        }
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getProfileDataOfInfo: (types) => {
    types = _.isArray(types) ? types : [types];
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        for (let i = 0; i < types.length; i++) {
          const type = types[i];
          const res = await profileStepperApi.getProfileDataOf(type);
          dispatch({
            type: ProfileTypes.GET_PROFILE_DATA_INFO,
            payload: res.data.data,
          });
        }
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getCompanyProfileData: (types) => {
    types = _.isArray(types) ? types : [types];
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        for (let i = 0; i < types.length; i++) {
          const type = types[i];
          const companyId = localStorage.getItem("companyId");
          const res = await profileStepperApi.getCompanyProfileData(
            type,
            companyId
          );
          if (!res.data.error.error) {
            dispatch({
              type: ProfileTypes.GET_PROFILE_DATA,
              typeOf: type,
              payload: res.data.data,
            });
          }
        }
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  updateCompanyPitch: (data, callBack) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        let companyId = localStorage.getItem("companyId");
        // for (let i = 0; i < data.length; i++) {
        if (data.coverImage && data.coverImage.imageFile) {
          const formData = new FormData();
          formData.append("files", data.coverImage.imageFile);
          const res = await Api.post(URI.UPLOAD + "/" + companyId, formData);
          data.coverImage = _.get(res, "data.data[0]");
        } else {
          data.coverImage = _.get(data.coverImage, "image");
        }
        if (data.topFeatureImage1 && data.topFeatureImage1.imageFile) {
          const formData = new FormData();
          formData.append("files", data.topFeatureImage1.imageFile);
          const res = await Api.post(URI.UPLOAD + "/" + companyId, formData);
          data.topFeatureImage1 = _.get(res, "data.data[0]");
        } else {
          data.topFeatureImage1 = _.get(data.topFeatureImage1, "image");
        }
        if (data.topFeatureImage2 && data.topFeatureImage2.imageFile) {
          const formData = new FormData();
          formData.append("files", data.topFeatureImage2.imageFile);
          const res = await Api.post(URI.UPLOAD + "/" + companyId, formData);
          data.topFeatureImage2 = _.get(res, "data.data[0]");
        } else {
          data.topFeatureImage2 = _.get(data.topFeatureImage2, "image");
        }
        if (data.companyLogo && data.companyLogo.imageFile) {
          const formData = new FormData();
          formData.append("files", data.companyLogo.imageFile);
          const res = await Api.post(URI.UPLOAD + "/" + companyId, formData);
          data.companyLogo = _.get(res, "data.data[0]");
        } else {
          data.companyLogo = _.get(data.companyLogo, "image");
        }
        // }
        data.companyId = companyId;
        const res = await profileStepperApi.updateCompanyPitch(data);
        data = res.data.data;
        dispatch({
          type: ProfileTypes.GET_PROFILE_DATA,
          typeOf: PROFILE_TYPES.COMPANY_PITCH,
          payload: res.data.data,
        });
        callBack && callBack(res.data.error);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  updateWaterCooler: (data, callBack) => {
    return async (dispatch) => {
      let companyId = localStorage.getItem("companyId");
      ApiActions.request(dispatch);
      try {
        let companyId = localStorage.getItem("companyId");
        for (let i = 0; i < data.length; i++) {
          if (data[i].imageFile) {
            const formData = new FormData();
            formData.append("files", data[i].imageFile);
            const res = await Api.post(URI.UPLOAD + "/" + companyId, formData);
            data[i].headShotLink = _.get(res, "data.data[0]");
            delete data[i].imageFile;
          }
        }
        const res = await profileStepperApi.updateWaterCooler(data);
        data = res.data.data;
        dispatch({
          type: ProfileTypes.GET_PROFILE_DATA,
          typeOf: PROFILE_TYPES.WATER_COOLER,
          payload: res.data.data,
        });
        callBack && callBack(res.data.error);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  updateOfficeShots: (data, callBack) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        let companyId = localStorage.getItem("companyId");
        for (let addressIndex = 0; addressIndex < data.length; addressIndex++) {
          //LOOPING EACH OFFICE ADDRESS WHICH HAS MULTIPLE IMAGES IN ARRY(OFFICEURL)
          if (data[addressIndex].officeUrl) {
            //IF ADDRESS HAS OFFICEURL
            const imageUrls = data[addressIndex].officeUrl;
            for (let urlIndex = 0; urlIndex < imageUrls.length; urlIndex++) {
              //LOOPING EACH IMAGE FILE OBJECT AND TRIGGERING AZURE BLOB API URL
              if (_.get(imageUrls[urlIndex], "imageFile", "")) {
                const formData = new FormData();
                formData.append("files", imageUrls[urlIndex].imageFile);
                const res = await Api.post(
                  URI.UPLOAD + "/" + companyId,
                  formData
                );
                const azureImageUrl = _.get(res, "data.data[0]", "");
                //REASSIGNING IMAGE FILE OBJECT WITH RESPONSE AZURE URL
                data[addressIndex].officeUrl[urlIndex] = azureImageUrl;
              } else {
                //IF AZURE URL EXISTS REPLACING IMAGE FILE OBJECT WITH AZURE URL
                data[addressIndex].officeUrl[urlIndex] = _.get(
                  imageUrls[urlIndex],
                  "image",
                  ""
                );
              }
            }
          }
          data[addressIndex].id = "";
        }
        const res = await profileStepperApi.updateOfficeShots(data);
        data = res.data.data;
        dispatch({
          type: ProfileTypes.GET_PROFILE_DATA,
          typeOf: PROFILE_TYPES.OFFICE_SHOTS,
          payload: res.data.data,
        });
        callBack && callBack(res.data.error);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  updateCompanyHighlights: (data, callBack) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        let companyId = localStorage.getItem("companyId");
        for (let layoutIndex = 0; layoutIndex < data.length; layoutIndex++) {
          if (data[layoutIndex].url) {
            const imageUrls = data[layoutIndex].url;
            for (let urlIndex = 0; urlIndex < imageUrls.length; urlIndex++) {
              if (imageUrls[urlIndex].imageFile) {
                const formData = new FormData();
                formData.append("files", imageUrls[urlIndex].imageFile);
                const res = await Api.post(
                  URI.UPLOAD + "/" + companyId,
                  formData
                );
                const azureImageUrl = _.get(res, "data.data[0]", "");
                data[layoutIndex].url[urlIndex] = azureImageUrl;
              } else {
                data[layoutIndex].url[urlIndex] = imageUrls[urlIndex].image;
              }
            }
          }
          data[layoutIndex].id = "";
          delete data[layoutIndex]._id;
        }
        const res = await profileStepperApi.updateCompanyHighlights(data);
        data = res.data.data;
        // dispatch({
        //   type: ProfileTypes.GET_PROFILE_DATA,
        //   typeOf: PROFILE_TYPES.COMPANY_HIGHLIGHTS,
        //   payload: res.data.data,
        // });
        callBack && callBack(res.data.error);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchAllCompanies: () => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.fetchAllCompanies();
        if (!res.data.error.error) {
          dispatch({
            type: ProfileTypes.GET_PROFILE_DATA,
            typeOf: "companies",
            payload: res.data.data,
          });
        }
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchBasicCompaniesInfo: () => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.fetchBasicCompaniesInfo();
        if (!res.data.error.error) {
          dispatch({
            type: ProfileTypes.COMPANY_BASIC_LIST,
            payload: res.data.data,
          });
        }
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchAllCountries: () => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.fetchAllCountries();
        dispatch({
          type: ConfigTypes.GET_ALL_COUNTRIES,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  updateBusinessCard: (data, callBack) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        let companyId = localStorage.getItem("companyId");
        if (_.get(data, "aboutMe.profileUrl.imageFile", false)) {
          const formData = new FormData();
          formData.append("files", data.aboutMe.profileUrl.imageFile);
          const res = await Api.post(URI.UPLOAD + "/" + companyId, formData);
          data.aboutMe.profileUrl = _.get(res, "data.data[0]");
        } else {
          data.aboutMe.profileUrl = _.get(data, "aboutMe.profileUrl", "");
        }
        const res = await profileStepperApi.updateBusinessCard(data);
        // dispatch({
        //   type: ProfileTypes.GET_PROFILE_DATA,
        //   typeOf: PROFILE_TYPES.BUSINESS_CARD,
        //   payload: res.data.data,
        // });
        callBack && callBack(res.data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchFeeds: (type) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.getFeeds(type);
        dispatch({
          type: ProfileTypes.FETCH_FEEDS,
          typeOf: `${type}Feeds`,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchVenue: () => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.getVenues();
        dispatch({
          type: ProfileTypes.GET_VENUES,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  pingAdminPublic: (data, cb) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.pingAdminPublic(data);
        cb && cb(res);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  pingAdminTeamMember: (data, cb) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.pingAdminTeamMember(data);
        cb && cb(res);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getGamificationData: () => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.getGamificationData(
          localStorage.getItem("companyId")
        );
        dispatch({
          type: ProfileTypes.GET_PROFILE_DATA,
          typeOf: PROFILE_TYPES.GAMIFICATION,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getJobData: (jobId) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.getFullJobDetails(jobId);
        dispatch({
          type: ProfileTypes.GET_JOB_DATA,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getCompanyBanners: (companyId, cb) => {
    return async (dispatch) => {
      try {
        ApiActions.request(dispatch);
        const res = await profileStepperApi.getInterviewBanners(companyId);
        if (!res.data.error.error) {
          dispatch({
            type: ProfileTypes.INTERVIEW_BANNER_DATA,
            payload: res.data.data,
          });
        }
        cb && cb(res.data.error.error);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchJobListings: () => {
    let companyId = localStorage.getItem("companyId");
    return async (dispatch) => {
      try {
        const res = await profileStepperApi.jobListing(companyId);
        const { data } = res;
        dispatch({
          type: ProfileTypes.JOB_LISTING,
          data,
        });
        ApiActions.success(dispatch);
        return data;
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  fetchTopLocations: (KEY) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.fetchAllLocations(KEY);
        const { data } = res;
        dispatch({
          type: ProfileTypes.TOP_LOCATIONS,
          payload: data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
  logoutImpersonate: (KEY, cb) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.logoutImpersonate(KEY);
        const { data } = res;
        dispatch({
          type: ProfileTypes.LOGOUT_IMPERSONATE,
          payload: data.data,
        });
        cb && cb(data.data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
  companyStatusUpdate: (id, status, cb) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.companyStatusUpdate(id, status);
        const { data } = res;
        cb && cb(data.data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
  searchInAll: (data, callback) => {
    return async (dispatch) => {
      try {
        ApiActions.request(dispatch);
        const res = await profileStepperApi.searchInAll(data);
        callback && callback(res.data);
        dispatch({
          type: ProfileTypes.SEARCH_IN_ALL,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
  postFeedback: (data) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.postFeedback(data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
  getInterviewInfo: (callback) => {
    return async (dispatch) => {
      try {
        ApiActions.request(dispatch);
        const res = await profileStepperApi.interviewInfo();
        callback && callback(res.data);
        dispatch({
          type: ProfileTypes.INTERVIEW_INFO,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
  getOpenPositionData: (data) => {
    const publicPage =
      window.location.pathname.includes("/public/") ||
      window.location.pathname.includes("/public-display/");
    let companyIdentifier = "";
    if (publicPage) {
      companyIdentifier =
        window.location.pathname.split("/public/")[1] ||
        window.location.pathname.split("/public-display/")[1];
    }
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.getOpenPositions(
          publicPage === true
            ? companyIdentifier
            : localStorage.getItem("companyId"),
          data
        );
        dispatch({
          type: ProfileTypes.GET_OPEN_POSITIONS,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  sendThanksEmail: (data, cb) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.sendThanksEmailNotification(data);
        cb && cb(res);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getInstagramVerificationCode: (data, cb) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.instagramVerificationCode(data);
        cb && cb(res.data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getInstaFeeds: () => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.getInstagramFeeds();
        dispatch({
          type: ProfileTypes.GET_PROFILE_DATA,
          typeOf: PROFILE_TYPES.INSTAGRAM_FEEDS,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  resendEmailTM: (data) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.triggerEmailTM(data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  createAzureBlob: (file, callBack) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        let companyId = localStorage.getItem("companyId");
        const formData = new FormData();
        formData.append("files", file);
        const res = await Api.post(URI.UPLOAD + "/" + companyId, formData);
        callBack && callBack(res.data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  sendEmailFranklinReport: (params, callBack) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await Api.post(URI.EMAIL_FR_ADMIN, params);
        callBack && callBack(res.data.error);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getCompaniesListByUser: () => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.getCompaniesByUser();
        dispatch({
          type: ProfileTypes.COMPANY_DATA_BY_USER,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
        setTimeout(() => {
          ApiActions.horseSuccess(dispatch);
        }, 5000);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  companiesList: () => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.companyList();
        dispatch({
          type: ProfileTypes.COMPANY_LIST,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  getCompanyMembersById: () => {
    const publicPage =
      window.location.pathname.includes("/public/") ||
      window.location.pathname.includes("/public-display/");
    let companyIdentifier = "";
    if (publicPage) {
      companyIdentifier =
        window.location.pathname.split("/public/")[1] ||
        window.location.pathname.split("/public-display/")[1];
    }
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.companyMembersById(
          publicPage === true
            ? companyIdentifier
            : localStorage.getItem("companyId")
        );
        dispatch({
          type: ProfileTypes.COMPANY_MEMBERS,
          payload: res.data.data,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  sendCompanyNameChangeRequest: (data, cb) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await profileStepperApi.sendCompanyNameChangeRequest(data);
        cb && cb(res.data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },

  addMeToCompany: (data, cb) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        if (_.get(data, "aboutMe.profileUrl.imageFile", false)) {
          const formData = new FormData();
          const companyId = localStorage.getItem("companyId");
          formData.append("files", data.aboutMe.profileUrl.imageFile);
          const res = await Api.post(URI.UPLOAD + "/" + companyId, formData);
          data.aboutMe.profileUrl = _.get(res, "data.data[0]");
        } else {
          data.aboutMe.profileUrl = _.get(data, "aboutMe.profileUrl.image", "");
        }
        const res = await profileStepperApi.addMeToCompany(data);
        cb && cb(res.data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
};
