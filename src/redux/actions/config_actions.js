import _ from "lodash";
import { ConfigApi } from "../api";
import { ConfigTypes } from "../actionTypes";
import { MONTHS } from "../../constants";
import { ApiActions } from ".";

export default {
  getConfigs: () => {
    return async (dispatch, getState) => {
      try {
        const res = await ConfigApi.getConfigs();
        const { data } = res;
        const config = data.data;
        const currentYear = new Date().getFullYear();
        const { fromYear } = config;
        config.years = _.reverse(_.range(fromYear, currentYear + config.range));
        config.months = MONTHS;
        config.yearsFrom = _.reverse(_.range(1900, currentYear + 1));
        dispatch({
          type: ConfigTypes.GET_CONFIGS,
          config,
        });
      } catch (err) {
        console.log(err);
      }
    };
  },
  contactUs: (data, callback) => {
    return async (dispatch) => {
      try {
        ApiActions.request(dispatch);
        await ConfigApi.contactUs(data);
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
  fetchAllCountries: (obj) => {
    return async (dispatch) => {
      ApiActions.request(dispatch);
      try {
        const res = await ConfigApi.fetchAllCountries(obj);
        const { data } = res.data;
        let defaultSelectedCountry = _.find(data.countryList, ["code", "US"]);
        dispatch({
          type: ConfigTypes.FETCHED_COUNTRIES,
          data,
          defaultSelectedCountry,
        });
        ApiActions.success(dispatch);
      } catch (err) {
        ApiActions.failure(dispatch, err);
      }
    };
  },
};
