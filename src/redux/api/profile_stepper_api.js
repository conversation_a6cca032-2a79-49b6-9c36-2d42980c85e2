import api, { jpackURL, lookupApiURL, CATSURL } from ".";
import { URI, JPACK_URI, LOOKUP_URI, CATS_URI } from "../../constants";

export default {
  getAllTrades: () => {
    return api.get(URI.TRADES);
  },

  getProfileDataOf: (type) => {
    return api.get(`${URI.COMPANY_PROFILE_DATA}/${type}`);
  },

  getCompanyProfileData: (type, companyId) => {
    return api.get(`${URI.COMPANY_PROFILE}/${companyId}/${type}`);
  },

  updateBusinessCard: (data) => {
    return api.post(`${URI.UPDATE_BUSINESS_CARD}`, data);
  },

  updateCompanyPitch: (data) => {
    return api.post(`${URI.UPDATE_ADD_COMPANY_PITCH}`, data);
  },

  updateWaterCooler: (data) => {
    let postData = {
      companyId: localStorage.getItem("companyId"),
      waterCollers: data,
    };
    return api.post(`${URI.UPDATE_WATER_COOLER}`, postData);
  },

  fetchAccolades: () => {
    return api.get(URI.FETCH_ACCOLADES);
  },

  fetchCompanyVibe: () => {
    return api.get(URI.GET_COMPANY_VIBES);
  },

  fetchCollegeAllegiances: (data) => {
    return api.post(URI.GET_COLLEGE_ALLEGIANCES, data);
  },

  fetchClienteleData: () => {
    return api.get(URI.GET_CLIENTELE_DATA);
  },

  fetchProclivitiesData: () => {
    return api.get(URI.GET_PROCLIVITIES_DATA);
  },

  updateOfficeShots: (data) => {
    let postData = {
      companyId: localStorage.getItem("companyId"),
      officeShots: data,
    };
    return api.post(URI.ADD_UPDATE_OFFICE_SHOTS, postData);
  },

  updateCompanyHighlights: (data) => {
    let postData = {
      companyId: localStorage.getItem("companyId"),
      highlights: data,
    };
    return api.post(URI.ADD_UPDATE_COMPANY_HIGHLIGHTS, postData);
  },

  fetchAllCompanies: () => {
    return api.post(URI.FETCH_ALL_COMPANIES);
  },

  fetchBasicCompaniesInfo: () => {
    return api.get(URI.BASIC_COMPANIES_INFO);
  },

  fetchAllCountries: () => {
    return api.post(URI.FETCH_ALL_COUNTRIES);
  },

  getFeeds: (type) => {
    return api.get(`${URI.FETCH_FEEDS}/get-${type}-feeds`);
  },

  getVenues: () => {
    return api.get(`${URI.FETCH_FEEDS}/get-venue`);
  },

  pingAdminPublic: (data) => {
    return api.post(URI.PING_ADMIN_PUBLIC, data);
  },

  pingAdminTeamMember: (data) => {
    return api.post(URI.PING_ADMIN_TM, data);
  },

  getGamificationData: (companyId) => {
    return api.get(`${URI.GET_GAMIFICATION}/${companyId}`);
  },

  getOpenPositions: (companyId, data) => {
    return api.post(`${URI.OPEN_POSITIONS}/${companyId}`, data);
  },

  sendThanksEmailNotification: (data) => {
    return api.post(URI.SEND_THANKS, data);
  },

  instagramVerificationCode: (data) => {
    return api.post(URI.INSTA_VERIFICATION_CODE, data);
  },

  getInstagramFeeds: () => {
    return api.get(URI.GET_INSTA_FEEDS);
  },

  triggerEmailTM: (data) => {
    return api.post(URI.TRIGGER_EMAIL_TM, data);
  },

  getCompaniesByUser: () => {
    return api.get(URI.COMPANY_BY_USER);
  },

  sendCompanyNameChangeRequest: (data) => {
    return api.post(URI.COMPANY_NAME_CHANGE, data);
  },

  addMeToCompany: (data) => {
    return api.post(URI.ADD_ME_TO_COMPANY, data);
  },

  companyList: () => {
    return api.post(URI.COMPANY_LIST);
  },

  getFullJobDetails: (jobId) => {
    return jpackURL.get(`${JPACK_URI.JOB_DATA}/${jobId}`);
  },

  getInterviewBanners: (companyId) => {
    return CATSURL.get(`${CATS_URI.COMPANY_INTERVIEW_BANNERS}/${companyId}`);
  },

  jobListing: (id) => lookupApiURL.post(`${LOOKUP_URI.JOB_LISTING}${id}`),
  companyMembersById: (id) => api.get(`${URI.COMPANY_MEMBERS_BY_ID}/${id}`),
  fetchAllLocations: (key) => lookupApiURL.post(LOOKUP_URI.LOCATIONS_DATA, key),
  postFeedback: (data) => lookupApiURL.post(LOOKUP_URI.POST_FEEDBACK, data),
  logoutImpersonate: (data) =>
    lookupApiURL.post(LOOKUP_URI.LOGOUT_IMPERSONATE, data),
  companyStatusUpdate: (id, data) => {
    return lookupApiURL.post(`${LOOKUP_URI.COMPANY_STATUS_UPDATE}/${id}`, data);
  },
  searchInAll: (data) => lookupApiURL.post(`${LOOKUP_URI.SEARCH_IN_ALL}`, data),
  interviewInfo: () => {
    return lookupApiURL.get(LOOKUP_URI.GET_INTERVIEW_INFO);
  },
};
