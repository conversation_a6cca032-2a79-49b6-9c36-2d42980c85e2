import { TOKEN_KEY } from "../../constants";

const RequestInterceptor = (reqConfig) => {
  try {
    let token = localStorage.getItem(TOKEN_KEY);
    const publicPage = localStorage.getItem("publicPage");
    const tradeworksUrl = localStorage.getItem("tradeworksurl");
    if (publicPage === "true") {
      reqConfig.headers = {
        ...reqConfig.headers,
        "trade-work-url": tradeworksUrl,
        "x-auth-token": (token !== null && (reqConfig.url === "/api/v1/companies-by-user" || reqConfig.url.includes('companyInfo'))) ? token : "",
      };
    } else {
      reqConfig.headers = {
        ...reqConfig.headers,
        "x-auth-token": token,
      };
    }

    return reqConfig;
  } catch (error) {
    return Promise.reject(error);
  }
};

export { RequestInterceptor };
