import axios from "axios";
import { default as <PERSON><PERSON><PERSON><PERSON> } from "./login_api";
import { default as Config<PERSON><PERSON> } from "./config_api";
import { default as <PERSON><PERSON><PERSON><PERSON> } from "./joinUs_api";
import { default as profileStepper<PERSON><PERSON> } from "./profile_stepper_api";

import {
  SERVER_URL,
  SERVER_URL_JPACK,
  SERVER_URL_CATS,
  SERVER_URL_LOOKUP,
} from "../../constants";
import { RequestInterceptor } from "./request_interceptor";
import { ResponseInterceptor } from "./response_interceptor";
const api = axios.create({
  baseURL: SERVER_URL,
});

const jpackURL = axios.create({
  baseURL: SERVER_URL_JPACK,
});

const CATSURL = axios.create({
  baseURL: SERVER_URL_CATS,
});

const lookupApiURL = axios.create({
  baseURL: SERVER_URL_LOOKUP,
});

api.interceptors.request.use(RequestInterceptor);
api.interceptors.response.use(null, ResponseInterceptor);

jpackURL.interceptors.request.use(RequestInterceptor);
jpackURL.interceptors.response.use(null, ResponseInterceptor);

CATSURL.interceptors.request.use(RequestInterceptor);
CATSURL.interceptors.response.use(null, ResponseInterceptor);

lookupApiURL.interceptors.request.use(RequestInterceptor);
lookupApiURL.interceptors.response.use(null, ResponseInterceptor);

export default api;
export {
  LoginApi,
  ConfigApi,
  profileStepperApi,
  JoinusApi,
  jpackURL,
  CATSURL,
  lookupApiURL,
};
