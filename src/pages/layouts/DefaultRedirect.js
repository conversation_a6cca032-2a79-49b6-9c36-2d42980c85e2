import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useHistory, useParams } from "react-router-dom";
import { find, get } from "lodash";

import HorizontalLabelPositionBelowStepper from "../../pages/ProfileStepper/profileStepperNew";
import { PROFILE_TYPES } from "../../constants";
import { ProfileActions, ConfigActions, ApiActions } from "../../redux/actions";
import { ApiActionTypes } from "../../redux/actionTypes";

/**
 *  Default component for landing page which is used to display images to the user before he sign in/ join in
 */

function DefaultRedirect(props) {
  const history = useHistory();
  const { token, companyId } = useParams();
  const CompanyListByUser = useSelector(
    (state) => state.Profile.CompanyListByUser
  );
  const fetchData = () => {
    dispatch(
      ProfileActions.getCompanyProfileData([PROFILE_TYPES.BUSINESS_CARD])
    );
    dispatch(ConfigActions.getConfigs());
    dispatch(ProfileActions.getCompaniesListByUser());
  };
  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    history.push("/twc/profile");
    localStorage.setItem("tradeworks_user_token", token);
    localStorage.setItem("companyId", companyId);
    dispatch({ type: ApiActionTypes.HORSESUCCESS });
  }, []);

  useEffect(() => {
    if (!CompanyListByUser) {
      return;
    }
    const foundRecord = find(CompanyListByUser, {
      _id: companyId,
    });
    localStorage.setItem("companyName", foundRecord.name);
    localStorage.setItem("companyLogo", foundRecord.companyLogo);
    localStorage.setItem("active", "company");
  }, [CompanyListByUser]);

  const dispatch = useDispatch();

  return (
    <>
      <HorizontalLabelPositionBelowStepper {...props} />
    </>
  );
}

export default DefaultRedirect;
