import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";
import { get } from "lodash";

import HorizontalLabelPositionBelowStepper from "../../pages/ProfileStepper/profileStepperNew";
import { PROFILE_TYPES } from "../../constants";
import { ProfileActions, ConfigActions, ApiActions } from "../../redux/actions";
import { ApiActionTypes } from "../../redux/actionTypes";

/**
 *  Default component for landing page which is used to display images to the user before he sign in/ join in
 */

function Default_Landing(props) {
  const history = useHistory();
  const businesscard = useSelector((state) => state.Profile.businesscard);
  const CompanyListByUser = useSelector(
    (state) => state.Profile.CompanyListByUser
  );
  const fetchData = () => {
    dispatch(
      ProfileActions.getCompanyProfileData([PROFILE_TYPES.BUSINESS_CARD])
    );
    dispatch(ConfigActions.getConfigs());
    dispatch(ProfileActions.getCompaniesListByUser());
  };
  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    // if (localStorage.getItem("companyId")) {
    //   return;
    // }
    if (businesscard && localStorage.getItem("isSetNewCompany") !== "true") {
      const cards = get(businesscard, "businessCard.length", 0);
      if (cards > 0) {
        ApiActions.request(dispatch);
        localStorage.removeItem("isSetNewCompany");
        const edit = get(props, "location.state.edit", false);
        if (!edit) {
          history.push("/twc/profile");
          dispatch({ type: ApiActionTypes.HORSESUCCESS });
        }
      }
    } else if (localStorage.getItem("isInpersonate") === "true") {
      history.push("/twc/profile");
      dispatch({ type: ApiActionTypes.HORSESUCCESS });
    } else if (
      localStorage.getItem("companyId") &&
      localStorage.getItem("isSetNewCompany") === "true"
    ) {
      localStorage.removeItem("companyId");
      localStorage.removeItem("companyName");
    }
  }, [businesscard]);

  const dispatch = useDispatch();

  return (
    <>
      <HorizontalLabelPositionBelowStepper {...props} />
    </>
  );
}

export default Default_Landing;
