import React, { useEffect, useState } from "react";

import NetworkStyles from "./GlobalSearchStyles";
import { Row, Text } from "../../components/common/ui_kit";
import strings from "../../utilities/strings";
import { Grid } from "@material-ui/core";
import CustomButton from "../../components/navigations/custom_buttons";
import { ProfileActions } from "../../redux/actions";
import { useDispatch, useSelector } from "react-redux";
import { get, map } from "lodash";
import { BASEURL, PROFILE_TYPES } from "../../constants";
import { color } from "../../utilities/themes";

function Network(props) {
  const { classes } = props;
  const dispatch = useDispatch();
  const [values, setValues] = useState({
    pendingNetworkList: [],
    confirmedNetworkList: [],
  });
  const { pendingNetworkList, confirmedNetworkList } = values;
  const globalSearchResults = useSelector((state) => state.Profile.searchInAll);

  const getPublicProfileLink = (companyInfo) => {
    if (!companyInfo) {
      return;
    }
    let host = get(window.location, "host", "");
    if (host) {
      if (host.includes("twwstage")) {
        return `${BASEURL.URL}cpack/company/public-display/${companyInfo.tradeWorkUrl}`;
      } else {
        return `${BASEURL.URL}cpack/company/public-display/${companyInfo.tradeWorkUrl}`;
      }
    }
    return null;
  };

  const redirectToPublic = (companyInfo) => () => {
    if (!companyInfo) {
      return;
    }
    window.open(
      `${BASEURL.URL}cpack/company/public/${companyInfo.tradeWorkUrl}`,
      "_blank" // <- This is what makes it open in a new window.
    );
  };

  const redirectToProfilePublic = (each) => () => {
    let hostUrl = "";
    hostUrl = `${BASEURL.URL}lpack/profile/public`;
    // hostUrl = host.includes("twwstage")
    //   ? "http://twwstage.franklinreport.com/lpack/profile/public"
    //   : "https://beta.thetrade.works/lpack/profile/public";
    const tradeworksURL = `${hostUrl}/${get(each, "profile.tradeWorkUrl", "")}`;
    window.open(
      tradeworksURL,
      "_blank" // <- This is what makes it open in a new window.
    );
  };

  const redirectToJobPublic = (each) => () => {
    let hostUrl = "";
    hostUrl = `${BASEURL.URL}jpack/publicFullJob`;
    // hostUrl = host.includes("twwstage")
    //   ? "http://twwstage.franklinreport.com/lpack/profile/public"
    //   : "https://beta.thetrade.works/lpack/profile/public";
    const tradeworksURL = `${hostUrl}/${get(each, "basicDetails.jobId", "")}`;
    window.open(
      tradeworksURL,
      "_blank" // <- This is what makes it open in a new window.
    );
  };

  return (
    <div style={{ padding: "30px 70px" }}>
      <Row>
        <Text
          size={30}
          color={color.primary_palette.franklin_purple}
          family="OptimaLT"
          style={{ paddingBottom: "18.5px" }}
        >
          SEARCH RESULTS{" "}
        </Text>
        <Text
          size={14}
          color={color.secondary_palette.grays.shadow_gray}
          family="gillsans_sb"
          style={{ marginTop: "14px", marginLeft: "16px" }}
        >
          {get(globalSearchResults, "openjobs.length", 0) +
            get(globalSearchResults, "companies.length", 0) +
            get(globalSearchResults, "users.length", 0)}{" "}
          Results
        </Text>
      </Row>
      {get(globalSearchResults, "openjobs.length", 0) === 0 &&
        get(globalSearchResults, "companies.length", 0) === 0 &&
        get(globalSearchResults, "users.length", 0) === 0 && (
          <div>
            <Text family="gillsans_r">No Results.</Text>
            <Text
              family="gillsans_r"
              color={color.primary_palette.franklin_purple}
            >
              Please widen your search
            </Text>
          </div>
        )}
      {get(globalSearchResults, "openjobs.length") > 0 && (
        <Text
          size={18}
          color={color.primary_palette.white}
          family="gillsans_sb"
          className={classes.companiesTxtStyle}
          style={{ backgroundColor: "#422163" }}
        >
          OPEN JOBS
        </Text>
      )}
      <Row className={classes.cardsFlex}>
        {globalSearchResults &&
          globalSearchResults.openjobs.map((each) => {
            return (
              <div
                className={classes.alignStyle}
                onClick={redirectToJobPublic(each)}
              >
                <Row>
                  <div>
                    <img
                      alt=""
                      src={
                        get(each, "primaryPoints.companyLogo") ||
                        "assets/images/company_thumbnail.png"
                      }
                      className={classes.iconStyle}
                    />
                  </div>
                  <div>
                    <Text
                      size={18}
                      color={color.primary_palette.franklin_purple}
                      family="gillsans_sb"
                    >
                      {get(each, "basicDetails.jobTitle")}
                    </Text>
                    <Text
                      size={16}
                      color={color.unrelated_mousy}
                      family="gillsans_sb"
                    >
                      {get(each, "basicDetails.tradeName")}
                    </Text>
                    <Row>
                      <Text
                        size={16}
                        color={color.unrelated_mousy}
                        family="gillsans_sb"
                      >
                        {get(each, "primaryPoints.companyName")}
                      </Text>
                      ,&nbsp;
                      <Text
                        size={16}
                        color={color.unrelated_mousy}
                        family="gillsans_sb"
                      >
                        {get(each, "basicDetails.location.cityAndState")}
                      </Text>
                    </Row>
                  </div>
                </Row>
              </div>
            );
          })}
      </Row>
      {get(globalSearchResults, "companies.length") > 0 && (
        <Text
          size={18}
          color={color.primary_palette.white}
          family="gillsans_sb"
          className={classes.companiesTxtStyle}
          style={{ backgroundColor: "#3e51a3" }}
        >
          COMPANIES
        </Text>
      )}
      <Row className={classes.cardsFlex}>
        {globalSearchResults &&
          globalSearchResults.companies.map((each) => {
            return (
              <div
                className={classes.alignStyle}
                onClick={redirectToPublic(each)}
              >
                <Row>
                  <div>
                    <img
                      alt=""
                      src={
                        each.companyLogo ||
                        "assets/images/company_thumbnail.png"
                      }
                      className={classes.iconStyle}
                    />
                  </div>
                  <div>
                    <Text
                      size={18}
                      color={color.primary_palette.franklin_purple}
                      family="gillsans_sb"
                    >
                      {get(each, "name")}
                    </Text>
                    <Text
                      size={16}
                      color={color.unrelated_mousy}
                      family="gillsans_sb"
                    >
                      {get(each, "address.city")}, {get(each, "address.state")}
                    </Text>
                    <Text style={{ marginTop: "10px" }}>
                      {get(each, "trades") &&
                        get(each, "trades").length > 0 &&
                        get(each, "trades").map((jobTrade, idx) => {
                          return (
                            <span key={idx} className={classes.tradeAlign}>
                              {(idx === 0 ? " " : " • ") + jobTrade.name}
                            </span>
                          );
                        })}
                    </Text>
                  </div>
                </Row>
              </div>
            );
          })}
      </Row>
      {get(globalSearchResults, "users.length") > 0 && (
        <Text
          size={18}
          color={color.primary_palette.white}
          family="gillsans_sb"
          className={classes.companiesTxtStyle}
          style={{ backgroundColor: "#c9ddf0" }}
        >
          PEOPLE
        </Text>
      )}
      <Row className={classes.cardsFlex}>
        {globalSearchResults &&
          globalSearchResults.users.map((each) => {
            return (
              <div
                className={classes.alignStyle}
                onClick={redirectToProfilePublic(each)}
              >
                <Row>
                  <div>
                    <img
                      alt=""
                      src={
                        get(each, "profileImg.square") ||
                        "assets/images/company_thumbnail.png"
                      }
                      className={classes.iconStyle}
                    />
                  </div>
                  <div>
                    <Text
                      size={18}
                      color={color.primary_palette.franklin_purple}
                      family="gillsans_sb"
                    >
                      {get(each, "firstName")} {get(each, "lastName")}
                    </Text>
                    <Text
                      size={16}
                      color={color.unrelated_mousy}
                      family="gillsans_sb"
                    >
                      {get(each, "profile.location.name")}
                    </Text>
                    <Text style={{ marginTop: "10px" }}>
                      {get(each, "profile.trades") &&
                        get(each, "profile.trades").length > 0 &&
                        get(each, "profile.trades").map((jobTrade, idx) => {
                          return (
                            <span key={idx} className={classes.tradeAlign}>
                              {(idx === 0 ? " " : " • ") + jobTrade.name}
                            </span>
                          );
                        })}
                    </Text>
                  </div>
                </Row>
              </div>
            );
          })}
      </Row>
    </div>
  );
}
export default NetworkStyles(Network);
