import { withStyles } from "@material-ui/core/styles";
import { color, pxToRem } from "../../utilities/themes";
// import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  headerWrap: {
    width: "90%",
    margin: "0 auto",
  },
  hfooter: {
    justifyContent: "space-between",
  },
  rightheadWrap: {
    textAlign: "center",
    width: pxToRem(150),
  },
  banner: {
    width: pxToRem(482),
  },
  bannerWrap: {
    position: "relative",
    width: pxToRem(482),
    textAlign: "center",
    margin: "0 auto",
  },
  jobJourney: {
    position: "absolute",
    top: pxToRem(17),
    left: pxToRem(120),
  },
  coin_img: {
    width: pxToRem(27),
    position: "relative",
    top: pxToRem(5),
  },
  companiesTxtStyle: {
    backgroundColor: "#5e94e0",
    padding: "10px",
    marginTop: pxToRem(10),
  },
  iconStyle: {
    width: "69px",
    height: "68px",
    marginRight: "8px",
    border: "1px solid #000",
  },
  tradeAlign: {
    fontSize: "16px",
    color: color.primary_palette.black,
  },
  alignStyle: {
    width: "367px",
    border: "1px solid #000",
    padding: "8px",
    cursor: "pointer",
    marginTop: "18.5px",
  },
  cardsFlex: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
});

export default styles;
