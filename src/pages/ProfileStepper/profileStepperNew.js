import React, { useEffect, useState } from "react";
import { get } from "lodash";
import { makeStyles } from "@material-ui/core/styles";
import { StepButton } from "@material-ui/core";
import Stepper from "@material-ui/core/Stepper";
import Step from "@material-ui/core/Step";
import StepLabel from "@material-ui/core/StepLabel";
import Typography from "@material-ui/core/Typography";

import CompanyWizard from "../../components/company_wizard/company_wizard_step2";
import Officeshot from "../../components/company_wizard/office_shot";
import CompanyHighlight from "../../components/company_wizard/Company_highlight";
import WaterCooler from "../../components/company_wizard/water_cooler_step3";
import tradework_theme, { pxToRem, color } from "../../utilities/themes";
import BusinessCard from "../../components/company_wizard/BusinessCard";
import ChequeredFlag_Icon from "../../components/data_display/icons/ChequeredFlag";

const useStyles = makeStyles((theme) => ({
  upperText: {
    "& .MuiStepLabel-label.MuiStepLabel-alternativeLabel": {
      marginTop: `${pxToRem(-38)} !important`,
    },
  },
  lowerText: {
    "& .MuiStepLabel-label.MuiStepLabel-alternativeLabel": {
      marginTop: `${pxToRem(7)} !important`,
    },
  },
  companyStepper: {
    position: "relative",
    marginTop: pxToRem(20),
    "& .MuiPaper-elevation0": {
      width: "65%",
      margin: "0 auto",
    },
    "& .MuiStepper-root": {
      paddingBottom: `${pxToRem(0)} !important`,
    },
    "& .MuiStepConnector-alternativeLabel": {
      left: "calc(-50% + 6px)",
      width: "96.5%",
    },
    "& .MuiStepper-alternativeLabel": {
      minHeight: pxToRem(50),
    },
    "&  .MuiStep-alternativeLabel": {
      cursor: "default",
    },

    "& .MuiStepConnector-root": {
      cursor: "default",
    },

    "& .MuiStepButton-root": {
      cursor: "default",
    },

    "& .MuiStepLabel-root": {
      cursor: "default",
    },
  },
  revisit_step: {
    "& .MuiStepIcon-active": {
      color: `${color.primary_palette.white} !important`,
      maxHeight: `${pxToRem(31)} !important`,
      backgroundColor: `${color.primary_palette.white} !important`,
      borderRadius: "0 !important",
      // border: `solid ${pxToRem(0.5)} ${color.primary_palette.black}`,
      // backgroundImage: ' url("assets/icons/pencil.svg")',
      width: pxToRem(25),
      height: pxToRem(25),
      backgroundRepeat: "no-repeat",
      backgroundColor: "white !important",
      backgroundSize: "auto",
      backgroundPosition: pxToRem(3),
      backgroundSize: pxToRem(20),
      position: "relative",
      marginTop: pxToRem(-8),
      zIndex: 9,
      "& path": {
        display: "none !important",
      },
      "& circle": {
        display: "none",
      },
    },
  },
  lastStep: {
    "& .MuiStepIcon-root": {
      fontSize: `${pxToRem(7)} !important`,
      position: "relative !important",
      color: `${color.primary_palette.franklin_purple} !important`,
      border: `${pxToRem(0)} !important`,
      left: `${pxToRem(-5)} !important`,
      top: `${pxToRem(4)} !important`,
    },
    "& .MuiStepLabel-labelContainer": {
      position: "relative",
      top: pxToRem(-28),
      left: pxToRem(15),
      opacity: 0.2,
    },
  },
  main_step: {
    "& .MuiStepIcon-text": {
      display: "none",
    },
    "& .Mui-disabled": {
      "& .MuiStepIcon-root": {
        color: color.primary_palette.white,
        border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
        borderRadius: "100%",
        // "&:hover": {
        //   border: `solid ${pxToRem(2)} ${
        //     color.primary_palette.franklin_purple
        //   }`,
        // },
      },
    },
    "& .MuiStepLabel-label": {
      color: color.primary_palette.franklin_purple,
      ...tradework_theme.typography.styles.gillsans_r,
      fontSize: pxToRem(12),
    },
    "& .MuiStepIcon-root": {
      fontSize: pxToRem(12),
      color: color.primary_palette.franklin_purple,
    },
    "& .MuiStepLabel-iconContainer": {
      paddingRight: `${pxToRem(0)} !important`,
      marginTop: pxToRem(6),
      maxHeight: pxToRem(14),
      minHeight: pxToRem(14),
    },
    "& .MuiButtonBase-root.Mui-disabled": {
      cursor: "default !important",
      pointerEvents: "unset !important",
    },
    "& .MuiStepIcon-active": {
      color: color.primary_palette.franklin_purple,
      maxHeight: pxToRem(14),
      backgroundColor: color.primary_palette.franklin_purple,
      borderRadius: "50%",
      // "&:hover": {
      //   border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
      //   borderRadius: "100%",
      // },
    },
    "& .MuiStepConnector-line": {
      width: "96%",
      background: color.primary_palette.black,
      height: pxToRem(1),
    },
    "& .MuiStepIcon-completed": {
      color: color.primary_palette.franklin_purple,
      backgroundColor: color.primary_palette.franklin_purple,
      borderRadius: "50%",
      // "&:hover": {
      //   border: `solid ${pxToRem(0.5)} ${color.primary_palette.black}`,
      //   backgroundImage: ' url("assets/icons/pencil.svg")',
      //   width: pxToRem(25),
      //   height: pxToRem(25),
      //   backgroundRepeat: "no-repeat",
      //   backgroundColor: "white",
      //   backgroundSize: "auto",
      //   maxHeight: pxToRem(31),
      //   backgroundPosition: pxToRem(3),
      //   backgroundSize: pxToRem(20),
      //   borderRadius: 0,
      //   position: "relative",
      //   marginTop: pxToRem(-8),
      //   zIndex: 9,
      //   "& path": {
      //     display: "none !important",
      //   },
      // },
    },
  },
}));

function getSteps() {
  return [
    "Step 1: Business Card",
    "Step 2: Company Pitch",
    "Step 3: Our People",
    "Step 4: Office Shots",
    "Step 5: Company Culture",
    "",
  ];
}

function getStepContent(stepIndex, handleNext, handleBack, edit) {
  switch (stepIndex) {
    case 0:
      return (
        <BusinessCard
          edit={edit}
          handleNext={handleNext}
          handleBack={handleBack}
        />
      );
    case 1:
      return (
        <CompanyWizard
          edit={edit}
          handleNext={handleNext}
          handleBack={handleBack}
        />
      );
    case 2:
      return (
        <WaterCooler
          edit={edit}
          handleNext={handleNext}
          handleBack={handleBack}
        />
      );
    case 3:
      return (
        <Officeshot
          edit={edit}
          handleNext={handleNext}
          handleBack={handleBack}
        />
      );
    case 4:
      return (
        <CompanyHighlight
          edit={edit}
          handleNext={handleNext}
          handleBack={handleBack}
        />
      );
    default:
      return "Unknown stepIndex";
  }
}

export default function HorizontalLabelPositionBelowStepper(props) {
  const [values, setValues] = useState({
    edit: false,
    step: null,
  });
  const { edit } = values;

  useEffect(() => {
    // IF USER CLICKS ON EDIT SECTION WILL BE REDIRECTED HERE TO STEPPER WITH STEP NUMBER
    const edit = get(props, "location.state.edit", false);
    const step = get(props, "location.state.step", null);
    const queryString = window.location.href;
    let isPublic = queryString.includes("public");
    if (!isPublic) {
      localStorage.removeItem("publicPage");
    }
    if (edit) {
      setValues({ ...values, edit, step });
      // REDIRECTING TO SELECTED STEP IN PROFILE
      setActiveStep(step - 1);
    }
  }, []);

  const classes = useStyles();
  const [activeStep, setActiveStep] = React.useState(0);
  const steps = getSteps();

  const handleNext = () => {
    if (!edit) {
      setValues({ ...values, revisit: false });
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  const handleBack = () => {
    !edit && setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const onStepClick = (index) => () => {
    if (activeStep > index && !edit) {
      setActiveStep(index);
      setValues({ ...values, revisit: true });
    }
  };

  return (
    <div className={classes.companyStepper}>
      {!edit && (
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label, index) => (
            <Step
              key={label}
              className={`${classes.main_step} ${
                values.revisit && classes.revisit_step
              }`}
            >
              <StepButton
              // onClick={onStepClick(index)}
              >
                <StepLabel
                  className={`${
                    index == steps.length - 1 && classes.lastStep
                  } ${index % 2 === 0 ? classes.upperText : classes.lowerText}`}
                  completed={false}
                >
                  {label}
                  {index == steps.length - 1 && (
                    <ChequeredFlag_Icon
                      className={classes.flag}
                      style={{ height: pxToRem(38), width: pxToRem(33) }}
                    />
                  )}
                </StepLabel>
              </StepButton>
            </Step>
          ))}
        </Stepper>
      )}

      <div>
        <Typography className={classes.instructions}>
          {getStepContent(activeStep, handleNext, handleBack, edit)}
        </Typography>
      </div>
    </div>
  );
}
