import React from "react";
import { map } from "lodash";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import OurOfficeStyles from "./styles/our_office_styles";
import strings from "../../utilities/strings";
import OfficeLayoutOne from "./layouts/office_layout1";
import Chevron_Down_Icon from "../data_display/icons/Arrow_Chevron_Down";
import CustomButton from "../navigations/custom_buttons";
import Lil_Plus_filled from "../data_display/icons/Lil_Plus_filled";
import { color } from "../../utilities/themes";

function OurOffice(props) {
  const { classes } = props;

  const [values, setValues] = React.useState({
    offices: [
      {
        layoutData: [],
        businessCardData: [],
      },
    ],
    showReorderContainer: false,
  });
  const { offices, showReorderContainer } = values;

  const addOfficeLayout = () => {
    setValues({
      ...values,
      offices: [
        ...values.offices,
        {
          layoutData: [],
          businessCardData: [],
        },
      ],
    });
  };

  return (
    <>
      <Row className={classes.edit_office}>
        <Row style={{ width: "80%", margin: "0 auto" }}>
          <Row className={classes.textEdit}>
            <Text className={classes.edit_officeText}>
              {strings.edit_office.titles.edit_office}
            </Text>
          </Row>
          <Row className={classes.reorder}>
            <Text size={15} color={color.secondary_palette.blues.action_blue}>
              {strings.edit_office.titles.edit_officeText}
            </Text>

            <Chevron_Down_Icon />
          </Row>
          {showReorderContainer && (
            <div>{/* container for reorder boxes */}</div>
          )}
        </Row>
        <Row className={classes.country_comp}>
          {map(offices, (office, index) => {
            return <OfficeLayoutOne layoutData={office} idx={index} />;
          })}
        </Row>
        <Row className={classes.checkbox_section2}>
          <CustomButton className={classes.Addoffice} onClick={addOfficeLayout}>
            <Lil_Plus_filled /> Add Office
          </CustomButton>
          <Row style={{ justifyContent: "center" }}>
            <CustomButton className={classes.cancelBtn}>CANCEL</CustomButton>
            <CustomButton className={classes.cancelBtn}>
              Save & Continue
            </CustomButton>
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default OurOfficeStyles(OurOffice);
