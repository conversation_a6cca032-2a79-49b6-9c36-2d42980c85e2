import React from "react";
import {
  FormControl,
  MenuItem,
} from "@material-ui/core";
import Select from "@material-ui/core/Select";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import OurOfficeStyles from "../styles/our_office_styles";
import Chevron_Down_Icon from "../../data_display/icons/Arrow_Chevron_Down";
import { color } from "../../../utilities/themes";
import CustomCheckbox from "../../inputs/custom_checkbox";
import AddImageLogo from "../../company_wizard/add_image_logo";

function OfficeLayoutOne(props) {
  const { classes, layoutData, idx } = props;
  // INTEGRATION SHOULD BE DONE, SO COMMENTED SOME CODE
  const returnLayout = (type, data) => {
    switch (type) {
      case 1:
        return (
          <>
            <Row className={classes.block_relative}>
              <Row>
                <Row className={classes.office_img1}>
                  <AddImageLogo
                    defaultImage="assets/images/Office_03.png"
                    index={0}
                    deleteIcon
                    id={data.id}
                    type="officeShots"
                    plus
                    //deleteImage={deleteImage}
                    refreshIcon
                    className={classes.imag_tag}
                    //layoutImage={data.officeUrl}
                    ////imageUpload={imageUpload}
                  />
                </Row>
                <Row className={classes.margin_9_block}>
                  <Row className={classes.office_img2}>
                    <AddImageLogo
                      defaultImage="assets/images/Office_01.png"
                      index={0}
                      deleteIcon
                      id={data.id}
                      type="officeShots"
                      plus
                      //deleteImage={deleteImage}
                      refreshIcon
                      className={classes.imag_tag}
                      //layoutImage={data.officeUrl}
                      ////imageUpload={imageUpload}
                    />
                  </Row>
                  <Row className={classes.office_img3}>
                    <AddImageLogo
                      defaultImage="assets/images/Office_02.png"
                      index={0}
                      deleteIcon
                      id={data.id}
                      type="officeShots"
                      plus
                      //deleteImage={deleteImage}
                      refreshIcon
                      className={classes.imag_tag}
                      //layoutImage={data.officeUrl}
                      ////imageUpload={imageUpload}
                    />
                  </Row>
                </Row>
              </Row>
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  "Error Message"
                </Text>{" "}
              </Row>
            </Row>
          </>
        );
      case 2:
        return (
          <>
            <Row className={classes.block_relative}>
              <Row>
                <Row className={classes.country_comp}>
                  <Row className={classes.office_img2}>
                    <AddImageLogo
                      defaultImage="assets/images/Office_01.png"
                      index={0}
                      deleteIcon
                      id={data.id}
                      type="officeShots"
                      plus
                      //deleteImage={deleteImage}
                      refreshIcon
                      className={classes.imag_tag}
                      //layoutImage={data.officeUrl}
                      ////imageUpload={imageUpload}
                    />
                  </Row>
                  <Row className={classes.office_img3}>
                    <AddImageLogo
                      defaultImage="assets/images/Office_02.png"
                      index={0}
                      deleteIcon
                      id={data.id}
                      type="officeShots"
                      plus
                      //deleteImage={deleteImage}
                      refreshIcon
                      className={classes.imag_tag}
                      //layoutImage={data.officeUrl}
                      ////imageUpload={imageUpload}
                    />
                  </Row>
                </Row>
                <Row className={classes.office_img1}>
                  <AddImageLogo
                    defaultImage="assets/images/Office_03.png"
                    index={0}
                    deleteIcon
                    id={data.id}
                    type="officeShots"
                    plus
                    //deleteImage={deleteImage}
                    refreshIcon
                    className={classes.imag_tag}
                    marginLeft_9={classes.marginLeft_9}
                    //layoutImage={data.officeUrl}
                    ////imageUpload={imageUpload}
                  />
                </Row>
              </Row>
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  "Error Message"
                </Text>{" "}
              </Row>
            </Row>
          </>
        );
      case 3:
        return (
          <>
            <Row className={classes.block_relative}>
              <Row>
                <Row className={classes.office_img3_block}>
                  <AddImageLogo
                    defaultImage="assets/images/Office_03.png"
                    index={0}
                    deleteIcon
                    id={data.id}
                    type="officeShots"
                    plus
                    //deleteImage={deleteImage}
                    refreshIcon
                    className={classes.imag_tag}
                    marginLeft_9={`${classes.marginLeft_9} ${classes.height_100}`}
                    //layoutImage={data.officeUrl}
                    ////imageUpload={imageUpload}
                  />
                </Row>
                <Row className={classes.office_img3_block}>
                  <AddImageLogo
                    defaultImage="assets/images/Office_03.png"
                    index={0}
                    deleteIcon
                    id={data.id}
                    type="officeShots"
                    plus
                    //deleteImage={deleteImage}
                    refreshIcon
                    className={classes.imag_tag}
                    marginLeft_9={`${classes.marginLeft_9} ${classes.height_100}`}
                    //layoutImage={data.officeUrl}
                    ////imageUpload={imageUpload}
                  />
                </Row>
                <Row className={classes.office_img3_block}>
                  <AddImageLogo
                    defaultImage="assets/images/Office_03.png"
                    index={0}
                    deleteIcon
                    id={data.id}
                    type="officeShots"
                    plus
                    //deleteImage={deleteImage}
                    refreshIcon
                    className={classes.imag_tag}
                    marginLeft_9={`${classes.marginLeft_9} ${classes.height_100}`}
                    //layoutImage={data.officeUrl}
                    ////imageUpload={imageUpload}
                  />
                </Row>
              </Row>
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  "Error Message"
                </Text>{" "}
              </Row>
            </Row>
          </>
        );
      case 4:
        return (
          <>
            <Row className={classes.block_relative}>
              <Row>
                <Row className={classes.office_img4_block}>
                  <AddImageLogo
                    defaultImage="assets/images/Office_03.png"
                    index={0}
                    deleteIcon
                    id={data.id}
                    type="officeShots"
                    plus
                    //deleteImage={deleteImage}
                    refreshIcon
                    className={classes.imag_tag}
                    marginLeft_9={`${classes.marginLeft_9} ${classes.height_100}`}
                    //layoutImage={data.officeUrl}
                    ////imageUpload={imageUpload}
                  />
                </Row>
                <Row className={classes.office_img4_block}>
                  <AddImageLogo
                    defaultImage="assets/images/Office_03.png"
                    index={0}
                    deleteIcon
                    id={data.id}
                    type="officeShots"
                    plus
                    //deleteImage={deleteImage}
                    refreshIcon
                    className={classes.imag_tag}
                    marginLeft_9={`${classes.marginLeft_9} ${classes.height_100}`}
                    //layoutImage={data.officeUrl}
                    ////imageUpload={imageUpload}
                  />
                </Row>
                <Row className={classes.country_comp}>
                  <Row className={classes.office_img2_4}>
                    <AddImageLogo
                      defaultImage="assets/images/Office_01.png"
                      index={0}
                      deleteIcon
                      id={data.id}
                      type="officeShots"
                      plus
                      //deleteImage={deleteImage}
                      refreshIcon
                      className={classes.imag_tag}
                      marginLeft_9={classes.marginLeft_9}
                      //layoutImage={data.officeUrl}
                      ////imageUpload={imageUpload}
                    />
                  </Row>
                  <Row className={classes.office_img2_4}>
                    <AddImageLogo
                      defaultImage="assets/images/Office_02.png"
                      index={0}
                      deleteIcon
                      id={data.id}
                      type="officeShots"
                      plus
                      //deleteImage={deleteImage}
                      refreshIcon
                      className={classes.imag_tag}
                      marginLeft_9={classes.marginLeft_9}
                      //layoutImage={data.officeUrl}
                      ////imageUpload={imageUpload}
                    />
                  </Row>
                </Row>
              </Row>
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  "Error Message"
                </Text>{" "}
              </Row>
            </Row>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Text className={classes.countryName}>New York City</Text>
      <Row className={classes.main_block}>
        <Row className={classes.card_blk}>
          <Row className={classes.main_heading}>
            <Text className={classes.mainText}>Ames | Adams Architects</Text>
            <Row className={classes.dropdown_align}>
              <FormControl className={classes.country_dropdown}>
                <Select IconComponent={Chevron_Down_Icon}>
                  <MenuItem value={10}>Ten</MenuItem>
                  <MenuItem value={20}>Twenty</MenuItem>
                  <MenuItem value={30}>Thirty</MenuItem>
                </Select>
              </FormControl>
            </Row>
          </Row>
          <Row>
            <Row className={classes.address}>
              <Text className={classes.address_text}>5 West 5th Ave</Text>
              <Text className={classes.address_text}>Suite 17S</Text>
              <Text className={classes.address_text}>New York, NY 10001 </Text>
            </Row>
            <Row className={classes.address}>
              <Text className={classes.address_text}><EMAIL></Text>
              <Text className={classes.address_text}>AARchitects.com</Text>
              <Text className={classes.address_text}>212.555.1234</Text>
            </Row>
          </Row>
        </Row>
        {returnLayout(idx + 1, {})}
      </Row>
      <Row className={classes.checkbox_section}>
        <CustomCheckbox label="HQ?" className={classes.checkbox} />
      </Row>
    </>
  );
}

export default OurOfficeStyles(OfficeLayoutOne);
