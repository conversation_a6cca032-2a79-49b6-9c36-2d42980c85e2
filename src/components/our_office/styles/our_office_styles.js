import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  edit_office: {
    paddingRight: pxToRem(165),
    paddingLeft: pxToRem(165),
    paddingTop: pxToRem(45),
    display: "block",
    // paddingRight: pxToRem(165)
  },
  textEdit: {
    flexGrow: 1,
  },
  reorder: {
    flexGrow: 1,
    justifyContent: "flex-end",
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(10),
      paddingLeft: pxToRem(5)
    }
  },
  options: {
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(10),
      top: pxToRem(11),
    },
    "& .MuiSelect-select": {
      paddingRight: `${pxToRem(15)} !important`,
      "&:focus": {
        backgroundColor: "transparent",
      },
    },
    "& .MuiInput-underline": {
      // display: "none"
      "&:before": {
        borderBottom: 0,
        display: "none",
      },
      "&:after": {
        borderBottom: 0,
        display: "none",
      },
    },
  },
  countryName: {
    fontSize: pxToRem(25),
    ...tradework_theme.typography.styles.avenir_light,
    color: color.greyish_brown,
    width: "80%",
    margin: "0 auto",
    paddingTop: pxToRem(20),
  },
  checkbox: {
    "& .MuiTypography-root": {
      fontSize: pxToRem(14),
      color: color.primary_palette.franklin_purple,
      ...tradework_theme.typography.styles.gillsans_sb,
    },
  },
  country_comp: {
    display: "block",
  },
  margin_9_block: {
    marginLeft: pxToRem(9),
    display: "block",
  },
  checkbox_section: {
    width: "80%",
    borderBottom: `solid ${pxToRem(1)} ${
      color.secondary_palette.grays.background_gray
    }`,
    margin: "0 auto",
    paddingBottom: pxToRem(25),
  },
  edit_officeText: {
    color: color.primary_palette.franklin_purple,
    fontSize: pxToRem(20),
    ...tradework_theme.typography.styles.avenir_bold,
  },
  card_blk: {
    width: pxToRem(416),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    paddingRight: pxToRem(17),
    paddingLeft: pxToRem(17),
    display: "block",
    marginRight: pxToRem(21),
  },
  mainText: {
    fontSize: pxToRem(30),
    color: color.primary_palette.black,
  },
  main_heading: {
    display: "block",
    margin: "0 auto",
    borderBottom: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
    width: "100%",
    textAlign: "center",
    paddingTop: pxToRem(25),
    paddingBottom: pxToRem(3),
  },
  dropdown_align: {
    justifyContent: "flex-end",
    paddingTop: pxToRem(7),
  },
  country_dropdown: {
    fontSize: pxToRem(10),
    "& .MuiInput-underline": {
      "&:before": {
        display: "none !important",
      },
      "&:after": {
        display: "none !important",
      },
    },
    "& .MuiSelect-select": {
      fontSize: pxToRem(10),
      paddingTop: 0,
      paddingBottom: 0,
      color: "#5c5c5c",
    },
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(11),
      top: pxToRem(2),
      "& path": {
        stroke: "#5f5f5f",
      },
    },
  },
  address: {
    flexGrow: 1,
    display: "block",
    textAlign: "center",
    paddingTop: pxToRem(26),
    paddingBottom: pxToRem(19),
  },
  address_text: {
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_light,
  },
  office_img1: {
    height: pxToRem(208),
    width: pxToRem(313),
  },
  imag_tag: {
    width: "100%",
    height: "100%",
  },
  office_img2: {
    height: pxToRem(105),
    width: pxToRem(209),
    marginBottom: pxToRem(6),
  },
  office_img3: {
    height: pxToRem(98),
    width: pxToRem(210),
  },
  office_img3_block: {
    height: pxToRem(208),
    width: pxToRem(178),
    display: "block",
  },
  marginRight_9: {
    marginRight: pxToRem(9),
  },
  marginLeft_9: {
    marginLeft: pxToRem(9),
  },
  height_100: {
    height: "100%",
  },
  // layout2
  main_heading2: {
    display: "block",
    margin: "0 auto",
    borderBottom: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
    width: "100%",
    textAlign: "center",
    paddingTop: pxToRem(11),
    paddingBottom: pxToRem(3),
  },
  dropdown_align2: {
    justifyContent: "flex-end",
  },
  address_text2: {
    fontSize: pxToRem(14),
    ...tradework_theme.typography.styles.avenir_light,
    marginBottom: pxToRem(6),
  },
  address_text3: {
    fontSize: pxToRem(10.5),
    ...tradework_theme.typography.styles.avenir_light,
    marginBottom: pxToRem(6),
  },
  address2: {
    flexGrow: 1,
    display: "block",
    textAlign: "center",
    paddingTop: pxToRem(14),
    paddingBottom: pxToRem(9),
  },
  card_blk2: {
    width: pxToRem(435),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    paddingRight: pxToRem(17),
    paddingLeft: pxToRem(17),
    display: "block",
    marginRight: pxToRem(21),
  },
  office_imgL2_1: {
    height: pxToRem(209),
    width: pxToRem(303),
  },
  office_imgL2_2: {
    height: pxToRem(105),
    width: pxToRem(204),
    marginBottom: pxToRem(6),
  },
  office_imgL2_3: {
    height: pxToRem(97),
    width: pxToRem(204),
  },
  checkbox_section2: {
    width: "80%",
    // borderBottom: `solid ${pxToRem(1)} ${color.secondary_palette.grays.background_gray}`,
    margin: "0 auto",
    paddingBottom: pxToRem(25),
    display: "block",
  },
  cancelBtn: {
    fontSize: pxToRem(15),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.avenir_bold,
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    padding: `${pxToRem(3)} ${pxToRem(18)} `,
    marginRight: pxToRem(75),
  },
  Addoffice: {
    fontSize: pxToRem(20.6),
    color: `${color.primary_palette.franklin_purple} !important`,
    ...tradework_theme.typography.styles.gillsans_sb,
    marginBottom: pxToRem(100),
    display: "block",
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(22),
      verticalAlignment: "middle",
      "& path:nth-child(1)": {
        fill: color.secondary_palette.grays.background_gray,
      },
    },
  },

  // layout3
  dropdown_align3: {
    justifyContent: "center",
    paddingTop: pxToRem(7),
  },
  mainText3: {
    fontSize: pxToRem(30),
    color: "#a0a0a0",
    paddingRight: pxToRem(80),
    paddingLeft: pxToRem(80),
  },
  address_text_L3_1: {
    color: "#a3a3a3",
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_light,
  },
  card_blk3: {
    width: pxToRem(486),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    paddingRight: pxToRem(17),
    paddingLeft: pxToRem(17),
    display: "block",
    marginRight: pxToRem(21),
  },
  main_heading3: {
    display: "block",
    margin: "0 auto",
    borderBottom: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
    width: "100%",
    textAlign: "center",
    paddingTop: pxToRem(33),
    paddingBottom: pxToRem(18),
  },
  office_imgL3_1: {
    width: pxToRem(273),
    height: pxToRem(236),
  },
  office_imgL3_2: {
    width: pxToRem(183),
    height: pxToRem(118),
    marginBottom: pxToRem(6),
  },
  office_imgL3_3: {
    width: pxToRem(183),
    height: pxToRem(110),
  },
  office_img4_block: {
    width: pxToRem(210),
    height: pxToRem(208),
  },
  office_img2_4: {
    width: pxToRem(118),
    height: pxToRem(101),
    marginBottom: pxToRem(6),
  },
  errorStyles: {
    position: "absolute",
    left: "50%",
    paddingTop: pxToRem(20),
  },
  main_block: {
    width: "80%",
    margin: "0 auto",
    justifyContent: "center",
  },
  block_relative: {
    display: "block",
    position: "relative",
  },
});

export default styles;
