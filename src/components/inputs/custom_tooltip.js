import React from "react";
import { withStyles } from "@material-ui/core/styles";
import Tooltip from "@material-ui/core/Tooltip";

const styles = {
  tooltip: {
    width: (props) => {
      return props.width;
    },
    height: (props) => {
      return props.height;
    },
    color: "#000",
    // fontWeight: "600",
    fontFamily: "gillsans_sb",
    // height: "36px",
    // borderRadius: "18px",
    // boxShadow: "0 20px 80px 0",
    // backgroundColor: "red",
  },
};

const CustomTooltip = withStyles(styles)(Tooltip);

function MyCustomTooltip(props) {
  return (
    <CustomTooltip
      title={props.title}
      width={props.width}
      height={props.height}
      {...props}
    >
      {props.children}
    </CustomTooltip>
  );
}

export default MyCustomTooltip;
