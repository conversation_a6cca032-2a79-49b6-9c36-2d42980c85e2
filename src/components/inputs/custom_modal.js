import React from "react";

import Modal from "@material-ui/core/Modal";
import { makeStyles } from "@material-ui/core";

const useStyles = makeStyles({
  custom_modal: {
    background: "rgba(190, 190, 190, 0.95)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    outline: "none",
    "& div[aria-hidden='true']": {
      backgroundColor: "rgba(255, 255, 255, 0.1) !important",
    },
    "& div": {
      "&:nth-child(3)": {
        borderRadius: "18px",
      },
    },
    //  "& div": {
    //       "&:nth-child(1)": {
    //           backgroundColor: "transparent !important",
    //         }
    //   }
  },
});

function CustomModal({ className, children, ...props }) {
  const classes = useStyles();
  return (
    <>
      <Modal
        className={`${classes.custom_modal} ${className}`}
        // BackdropProps	={
        //   classes: {root: classes.root}
        // }
        {...props}
      >
        {children}
      </Modal>
    </>
  );
}

export default CustomModal;
