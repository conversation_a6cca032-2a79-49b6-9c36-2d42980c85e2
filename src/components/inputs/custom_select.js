import React, { useState, useEffect, useRef } from "react";
import _ from "lodash";
import {
  ClickAwayListener,
  Popper,
  Paper,
  makeStyles,
} from "@material-ui/core";
import tradework_theme, { color, pxToRem } from "../../utilities/themes";

const useStyles = makeStyles({
  flex: {
    width: "100%",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "white",
    height: pxToRem(23),
    padding: `${pxToRem(0)} ${pxToRem(5)}`,
    border: `${pxToRem(1)} solid rgba(0, 0, 0, 0.30)`,
    cursor: "pointer",
    "&:hover": {
      borderColor: color.primary_palette.black,
    },
    "&:focus": {
      borderColor: color.primary_palette.black,
    },
  },
  disabled: {
    cursor: "default",
    "&:hover": {
      borderColor: "rgba(0, 0, 0, 0.30)",
    },
    color: "rgba(0, 0, 0, 0.38)",
  },
  paper: {
    paddingBottom: "0px !important",
    boxShadow:
      "0px 5px 5px -3px rgba(0,0,0,0.2), 0px 8px 10px 1px rgba(0,0,0,0.14), 0px 3px 14px 2px rgba(0,0,0,0.12)",
  },
});

function CustomSelect({
  value,
  IconComponent,
  className,
  style,
  renderValue,
  disabled,
  onBlur,
  onFocus,
  children,
  ...props
}) {
  const anchorRef = useRef(null);
  const classes = useStyles();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    toggleDropdown(false);
  }, [value]);

  const toggleDropdown = (flag) => {
    flag && onFocus && onFocus();
    // if dropdown open and trying to close dropdown then call onBlur if exist
    open && !flag && onBlur && onBlur();
    setOpen(flag);
  };

  return (
    <>
      <div
        ref={anchorRef}
        onClick={() => {
          !disabled && toggleDropdown(!open);
        }}
        className={`${classes.flex} ${
          disabled && classes.disabled
        } ${className}`}
        style={style}
        // {...props}
      >
        <p>{(renderValue && renderValue(value)) || value}</p>
        {IconComponent && <IconComponent />}
      </div>
      <Popper open={open} anchorEl={anchorRef.current} style={{ zIndex: 1500 }}>
        <ClickAwayListener
          onClickAway={(evt) => {
            toggleDropdown(!open);
          }}
        >
          <Paper
            elevation={3}
            square
            style={{
              width: _.get(anchorRef, "current.offsetWidth"),
            }}
            className={classes.paper}
          >
            {children}
          </Paper>
        </ClickAwayListener>
      </Popper>
    </>
  );
}

export default CustomSelect;
