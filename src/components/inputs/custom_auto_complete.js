import React from "react";
import Autocomplete from "@material-ui/lab/Autocomplete";
import { TextField, CircularProgress } from "@material-ui/core";
import parse from "autosuggest-highlight/parse";
import match from "autosuggest-highlight/match";
import { makeStyles } from "@material-ui/styles";

import { color, pxToRem } from "../../utilities/themes";
import Text from "../common/ui_kit/text";

/**
 * Auto Suggest Component with pre populated options on text input
 * @param {*} props
 */

const useStyles = makeStyles({
  MuiAutocomplete: {
    "& input:first-child": {
      marginLeft: "6%",
    },
    "&:after": {
      borderBottom: "transparent !important",
    },
    "&:before": {
      borderBottom: "transparent !important",
    },
    "& .MuiFormControl-root": {
      "&:after": {
        borderBottom: "transparent !important",
      },
      "&:before": {
        borderBottom: "transparent !important",
      },
      "&:focus": {
        border: "0px !important",
        color: "transparant",
      },
      "&:hover": {
        border: "1px solid #C6DDFF !important",
        "&:not(.Mui-disabled)": {
          "&:after": {
            borderBottom: "transparent !important",
          },
          "&:before": {
            borderBottom: "transparent !important",
          },
        },
      },
      "&:active": {
        border: `solid  ${pxToRem(1)} #5E94E0 !important`,
        "&:not(.Mui-disabled)": {
          "&:after": {
            borderBottom: "transparent !important",
          },
          "&:before": {
            borderBottom: "transparent !important",
          },
        },
      },
    },
  },
  error: {
    "& .MuiFormControl-root": {
      border: `solid ${pxToRem(1)} ${
        color.primary_palette.christmas_red
      } !important`,
      margin: 0,
    },
  },
  text_info: {
    display: "inline-block",
    verticalAlign: "middle",
  },
  text_sub: {
    display: "block",
    fontSize: pxToRem(17),
  },
});

const CustomAutocomplete = (props) => {
  const classes = useStyles();
  return (
    <>
      <Autocomplete
        {...props}
        className={`${props.className} ${
          props.hasLogo || props.professionalSummary
            ? classes.MuiAutocomplete
            : ""
        } ${props.error && classes.error}`}
        style={{
          ...(props.error && {
            borderColor: color.primary_palette.christmas_red,
          }),
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            margin="normal"
            fullWidth
            placeholder={props.placeholder}
            onChange={props.onTextChange}
            onBlur={props.onBlur}
            InputProps={{
              ...params.InputProps,
              endAdornment: (
                <React.Fragment>
                  {props.loading ? (
                    <CircularProgress color="inherit" size={20} />
                  ) : null}
                  {props.endAdornment || params.InputProps.endAdornment}
                </React.Fragment>
              ),
            }}
          />
        )}
        renderOption={(option, { inputValue }) => {
          const matches = match(option.name, inputValue);
          const parts = parse(option.name, matches);

          return (
            <div>
              {props.hasLogo && (
                <img
                  style={{
                    height: pxToRem(13),
                    width: pxToRem(20),
                    margin: `${pxToRem(0)} ${pxToRem(5)} ${pxToRem(
                      0
                    )} ${pxToRem(0)}`,
                  }}
                  src={
                    option.logo
                      ? option.logo
                      : "assets/images/blank-school-icon.svg"
                  }
                />
              )}
              {props.businessCard ? (
                <div>
                  <img
                    style={{
                      height: pxToRem(41),
                      width: pxToRem(41),
                      verticalAlign: "middle",
                      padding: `${pxToRem(10)} ${pxToRem(15)} ${pxToRem(
                        10
                      )} ${pxToRem(5)}`,
                    }}
                    className={classes.logo_width}
                    src={option.logo || "assets/images/accolade_star.jpg"}
                  />
                  <div className={classes.text_info}>
                    <Text
                      size={22}
                      color={color.primary_palette.black}
                      family="gillsans_sb"
                      fontWeight={600}
                    >
                      {option.name}
                    </Text>
                    <Text
                      size={14}
                      color={color.greyish_brown}
                      family="gillsans_sb"
                      fontWeight={300}
                    >
                      {option.primaryTrade} {option.primaryTrade && "-"}{" "}
                      {option.secondaryTrade} {option.secondaryTrade && "-"}{" "}
                      {option.address.city}, {option.address.state}
                    </Text>
                  </div>
                </div>
              ) : (
                parts.map((part, index) => (
                  <span
                    key={index}
                    style={{
                      color: part.highlight
                        ? props.highlightColor
                          ? props.highlightColor
                          : "#000"
                        : "#000",
                      fontWeight: part.highlight ? 700 : 400,
                    }}
                  >
                    {part.text}
                  </span>
                ))
              )}
            </div>
          );
        }}
      />
      {/* {props.error && (
        <p style={{ color: color.primary_palette.christmas_red, margin: 0 }}>
          {props.error}
        </p>
      )} */}
    </>
  );
};

export default CustomAutocomplete;
