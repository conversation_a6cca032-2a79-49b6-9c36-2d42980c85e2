import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { pxToRem, color } from "../../../utilities/themes";

const styles = withStyles({
  requestModalSuccess_width: {
    width: pxToRem(441),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  job_listing_dropdown: {
    width: pxToRem(237),
    marginLeft: pxToRem(18),
    height: pxToRem(30),
    borderRadius: pxToRem(3),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(10),
    },
    "& p": {
      fontSize: pxToRem(14),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.black,
      paddingLeft: pxToRem(5),
    },
  },
  job_listing_dropdown2: {
    width: pxToRem(237),
    marginLeft: pxToRem(18),
    height: pxToRem(30),
    borderRadius: pxToRem(3),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(10),
    },
    "& p": {
      fontSize: pxToRem(14),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.black,
      paddingLeft: pxToRem(5),
    },
  },
  requestModal_width: {
    width: pxToRem(619),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  searchModal_width: {
    width: pxToRem(475),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  companyListModal_width: {
    width: pxToRem(450),
    border: `solid ${pxToRem(3)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  talentModal_width: {
    width: pxToRem(482),
    border: `solid ${pxToRem(3)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  request_company_width: {
    width: pxToRem(400),
    height: pxToRem(300),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  displayModal_width: {
    width: pxToRem(619),
    height: pxToRem(800),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  displayModal_width_resume: {
    width: pxToRem(619),
    height: pxToRem(630),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  displayModal_width_2: {
    width: pxToRem(730),
    height: pxToRem(650),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  btnRight: {
    textAlign: "right",
  },
  flying_envelop: {
    width: pxToRem(300),
  },
  crossBtn: {
    minWidth: pxToRem(30),
    padding: pxToRem(15),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
      width: pxToRem(15),
      height: pxToRem(15),
    },
  },
  crossBtn2: {
    minWidth: pxToRem(30),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
      width: pxToRem(15),
      height: pxToRem(15),
    },
  },
  recomTxt: {
    width: pxToRem(400),
    textTransform: "uppercase",
    margin: "0 auto",
    textAlign: "center",
    marginBottom: pxToRem(15),
  },
  recomendation_success: {
    margin: `${pxToRem(0)} ${pxToRem(80)}`,
    textAlign: "center",
    // fontWeight:'bold'
  },
  textCenter: {
    textAlign: "center",
  },
  success_invite_envelop: {
    width: pxToRem(80),
    marginTop: pxToRem(32),
    marginBottom: pxToRem(30),
  },
  invite_envelop: {
    width: pxToRem(89),
    height: pxToRem(101),
  },
  accolades_image: {
    width: pxToRem(69),
    height: pxToRem(69),
  },
  elememt_required: {
    fontSize: pxToRem(10),
    // verticalAlign: "super",
    paddingLeft: pxToRem(3),
  },
  success_spacing_from: {
    padding: `${pxToRem(0)} ${pxToRem(0)}`,
  },
  spacing_from: {
    padding: `${pxToRem(0)} ${pxToRem(25)}`,
  },
  input_field_Name: {
    width: "30%",
    paddingRight: pxToRem(49),
  },
  text_field_Name: {
    width: "92%",
    // paddingRight: pxToRem(49),
  },
  input_field_email: {
    width: "40%",
    "& .MuiFormControl-root": {
      width: "100%",
    },
  },
  field_spacing: {
    paddingTop: pxToRem(24),
  },
  field_spacing_top: {
    paddingTop: pxToRem(12),
  },
  textArea: {
    resize: "none",
    width: "97%",
    height: `${pxToRem(190)} !important`,
    overflow: "auto !important",
    padding: `${pxToRem(18)} ${pxToRem(7)}`,
    ...tradework_theme.typography.styles.gillsans_r,
    fontSize: pxToRem(15),
  },
  padding_top_35: {
    paddingTop: pxToRem(35),
  },
  padding_top_15: {
    paddingTop: pxToRem(15),
  },
  reqBtn: {
    width: pxToRem(170),
    height: pxToRem(25),
    borderRadius: pxToRem(13.5),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    backgroundColor: color.primary_palette.white,
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.gillsans_sb,
    color: `${color.primary_palette.pine_green} !important`,
    padding: 0,
    marginTop: pxToRem(39),
    marginBottom: pxToRem(28),
  },
  printBtn: {
    width: pxToRem(135),
    height: pxToRem(25),
    marginLeft: pxToRem(27),
    borderRadius: pxToRem(13.5),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.gillsans_sb,
    color: `${color.primary_palette.franklin_purple} !important`,
    padding: 0,
    marginTop: pxToRem(39),
    marginBottom: pxToRem(28),
  },
  terrificBtn: {
    width: pxToRem(129),
    height: pxToRem(25),
    borderRadius: pxToRem(13.5),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.gillsans_sb,
    color: `${color.primary_palette.franklin_purple} !important`,
    padding: 0,
    marginTop: pxToRem(39),
    marginBottom: pxToRem(28),
  },

  // RecommendationTQ styles
  RecommendationTQ_width: {
    width: pxToRem(495),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
  },
  padding_top_24: {
    paddingTop: pxToRem(24),
  },
  padding_top_5_22: {
    paddingTop: pxToRem(5),
    paddingBottom: pxToRem(22),
  },
  goBtn: {
    width: pxToRem(170),
    height: pxToRem(25),
    borderRadius: pxToRem(13.5),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.gillsans_sb,
    color: `${color.primary_palette.franklin_purple} !important`,
    padding: 0,
    marginTop: pxToRem(33),
    marginBottom: pxToRem(36),
    "&:hover": {
      backgroundColor: `${color.btn_hover} !important`,
    },
  },

  // recommendation slider
  recommendation_width: {
    width: pxToRem(551),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
  },
  upperText: {
    textTransform: "uppercase",
  },
  slider_content: {
    width: pxToRem(423),
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    backgroundColor: color.primary_palette.white,
    margin: "0 auto",
    padding: `${pxToRem(10)} ${pxToRem(25)}`,
  },
  actual_msg: {
    padding: `${pxToRem(40)} ${pxToRem(0)}`,
  },
  padding_bottom_8: {
    paddingBottom: pxToRem(8),
    wordBreak: "break-word",
  },
  textRight: {
    textAlign: "right",
    paddingBottom: pxToRem(60),
  },
  marginRight40: {
    marginRight: pxToRem(40),
  },
  greatBtn: {
    width: pxToRem(108),
    height: pxToRem(25),
    borderRadius: pxToRem(30),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.gillsans_sb,
    color: `${color.primary_palette.franklin_purple} !important`,
    margin: `${pxToRem(25)} ${pxToRem(0)}`,
    "&:hover": {
      backgroundColor: `${color.btn_hover} !important`,
    },
  },
  recommendation_img: {
    paddingTop: pxToRem(15),
  },
  star_icon: {
    width: pxToRem(21),
    height: pxToRem(20),
  },
  arrow: {
    width: pxToRem(55),
    height: pxToRem(21),
    cursor: "pointer",
  },
  leftArrow: {
    transform: "rotate(90deg)",
  },
  rightArrow: {
    transform: "rotate(-90deg)",
  },
  input_field: {
    "& .MuiInputBase-input": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.NeutraText,
      padding: 0,
    },
  },
  search_input_field: {
    marginTop: "20px",
    "& .MuiInputBase-input": {
      width: "400px",
      border: "1px solid #979797",
      height: "20px",
      padding: "10px",
      borderRadius: "4px",
      fontSize: pxToRem(20),
      textTransform: "uppercase",
      ...tradework_theme.typography.styles.NeutraText_b,
      "&::placeholder": {
        ...tradework_theme.typography.styles.NeutraText,
      },
    },
  },
  RecommendationDelete_width: {
    width: pxToRem(551),
    border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `solid ${pxToRem(1.5)} ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
  },
  padding_space: {
    paddingTop: pxToRem(24),
    paddingBottom: pxToRem(33),
    paddingLeft: pxToRem(20),
    paddingRight: pxToRem(20),
  },
  sureBtn: {
    width: pxToRem(131),
    height: pxToRem(25),
    borderRadius: pxToRem(30),
    border: `solid ${pxToRem(1.5)} ${color.form_colors.blueberry_purple}`,
    backgroundColor: color.primary_palette.white,
    color: `${color.form_colors.royal_purple_1} !important`,
    fontSize: pxToRem(15),
    padding: 0,
    marginBottom: pxToRem(28),
    ...tradework_theme.typography.styles.gillsans_sb,
  },
  imageBlockAlign: {
    display: "inline-block",
    width: pxToRem(90),
  },
  textBlockAlign: {
    display: "inline-block",
    verticalAlign: "top",
  },
  tw_icon: {
    width: "50px",
    height: "59px",
    marginLeft: "27px",
  },
  marginRight4: {
    marginRight: pxToRem(4),
  },
  margin20: {
    margin: "20px 0",
  },
  "MuiTableCell-root": {
    padding: "8px",
  },
  contactUsModal: {
    width: "390px",
    border: `1.5px solid ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      border: `1.5px solid ${color.primary_palette.franklin_purple}`,
      outline: "none",
    },
    textAlign: "center",
  },
  formLabel: {
    textTransform: "uppercase",
    fontFamily: tradework_theme.typography.styles.gillsans_sb.fontFamily,
    marginBottom: "3px",
  },
  contactInput: {
    width: "334px",
    height: "30px",
    border: `1px solid ${color.secondary_palette.grays.shadow_gray}`,
    marginBottom: "14px",
    padding: "0 10px",
    boxSizing: "border-box",
    fontFamily: "NeutraText",
    borderRadius: "2px",
  },
  contactInputText: {
    width: "334px",
    height: "30px",
    border: `1px solid ${color.secondary_palette.grays.shadow_gray}`,
    marginBottom: "14px",
    padding: "0 10px",
    boxSizing: "border-box",
    fontFamily: "NeutraText",
    borderRadius: "2px",
  },
  formInputsDiv: {
    textAlign: "left",
    padding: "14px 28px",
    position: "relative",
  },
  eraseInputValBtn: {
    position: "absolute",
    top: "35px",
    right: "20px",
    color: color.secondary_palette.grays.shadow_gray,
    cursor: "pointer",
  },
  sendBtn: {
    width: "111px",
    height: "27px",
    border: `1px solid ${color.primary_palette.pine_green}`,
    textTransform: "uppercase",
    fontSize: "15px",
    ...tradework_theme.typography.styles.gillsans_sb,
    color: color.primary_palette.pine_green,
    margin: "5px 0 10px 0",
    "&:hover": {
      color: `${color.primary_palette.pine_green} !important`,
      // backgroundColor: `${color.primary_palette.white} !important`,
    },
  },
  eraseMsg: {
    position: "absolute",
    top: "5px",
    right: "12px",
    color: color.secondary_palette.grays.shadow_gray,
    cursor: "pointer",
  },
  error_icon: {
    color: color.primary_palette.christmas_red,
    fontSize: pxToRem(10),
    paddingLeft: pxToRem(3),
  },
  textArea2: {
    width: "100%",
    // height: "3.0625em !important",
    height: "76px !important",
    resize: "none",
    outline: "none",
    // padding: "0 15px",
    fontFamily: "NeutraText",
    // fontSize: font_sizes.font_16,
    marginTop: "5px",
    paddingRight: pxToRem(15),
    fontSize: pxToRem(16),
    paddingLeft: pxToRem(11),
  },
  doc: {
    flexGrow: 1,
    // width: "25%",
  },
  doc_list: {
    // width: "25%",
    textAlign: "center",
    paddingTop: pxToRem(14),
    padding: pxToRem(20),
  },
  your_location: {
    width: "244px",
    marginLeft: "22px",
    marginTop: "-10px",
    textAlign: "center",
    "& .MuiInputBase-input": {
      padding: "0 !important",
      "&::placeholder": {
        fontSize: pxToRem(14),
        ...tradework_theme.typography.styles.gillsans_sb,
        color: color.primary_palette.black,
        opacity: 1,
      },
    },
    "& svg": {
      width: "16px",
      height: "16px",
      position: "absolute",
      top: "0px !important",
      left: "-11px",
    },
  },
});

export default styles;
