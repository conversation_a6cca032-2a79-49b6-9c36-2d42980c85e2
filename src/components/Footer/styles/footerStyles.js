import { withStyles } from "@material-ui/core/styles";

import { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  footerRow: {
    borderTop: `2px solid ${color.secondary_palette.grays.shadow_gray}`,
    padding: `25px 0`,
    marginTop: pxToRem(25),
  },
  footerGrid: {
    borderRight: `1px solid ${color.primary_palette.franklin_purple}`,
  },
  logoGrid: {
    textAlign: "center",
  },
  textGrid: {
    paddingLeft: "52px",
  },
  contactGrid: {
    borderRight: "none",
  },
  socialMediaIcons: {
    marginRight: "24px",
    fontSize: "2.2em",
  },
  email: {
    color: color.clammy_pearl,
    fontSize: pxToRem(16),
    textDecoration: "none",
    "&:hover": {
      color: color.primary_palette.pine_green,
      textDecoration: "underline",
    },
  },
});

export default styles;
