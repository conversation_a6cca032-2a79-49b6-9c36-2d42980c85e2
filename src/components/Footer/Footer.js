import React from "react";
import { Grid } from "@material-ui/core";
import { connect, useDispatch } from "react-redux";
import { useState } from "react";
import _, { get } from "lodash";

import { Row, Text } from "../common/ui_kit";
import footerStyles from "../Footer/styles/footerStyles";
import strings from "../../utilities/strings";
import { color, pxToRem } from "../../utilities/themes";
import {
  FOOTER_DATA,
  ATS_URLS,
  TOKEN_KEY,
  TDW_URL,
  SPACK_DEV_URL,
  SPACK_QA_URL,
  SPACK_STAGE_URL,
  FRANKLIN_REPORT_URL,
  BASEURL,
} from "../../constants";
// import FaceBook_Outline_Icon from "../data_display/icons/Facebook_Outline";
// import Twitter_Outline_Icon from "../data_display/icons/Twitter_Outline";
// import Instagram_Outline_Icon from "../data_display/icons/Instagram_Outline";
import CustomModal from "../inputs/custom_modal";
import SearchJobModal from "../modals/feedbacks/search_job_modal";
import ContactUsModal from "../Footer/ContactUs";
import TalentSearch from "../Logged_In_header/talent_search";
import VisitCommunity from "../Logged_In_header/visit_community";
import { LoginActions } from "../../redux/actions";

const Footer = ({
  classes,
  userinfo,
  jobListing,
  trades,
  topLocations,
  profileUserData,
}) => {
  const [showSearchJobModal, setShowSJModal] = useState(false);
  const [showTalentSrchModal, setTalentSrchModal] = useState(false);
  const [showCommunityModal, setCommunityModal] = useState(false);
  const [contactListModal, setContactListModal] = useState(false);
  const dispatch = useDispatch();

  // To show/hide the search job modal
  const toggleSearchJobModal = () => {
    setShowSJModal((showSearchJobModal) => !showSearchJobModal);
  };
  // To show/hide the talent search modal
  const toggleTalentSearchModal = () => {
    setTalentSrchModal((showTalentSrchModal) => !showTalentSrchModal);
  };
  const toggleVisitCommunityModal = () => {
    setCommunityModal((showCommunityModal) => !showCommunityModal);
  };
  // To redirect to LPack
  const redirectToLpack = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    let token = localStorage.getItem("tradeworks_user_token");
    if (host.includes("localhost")) {
      host = "localhost:3001";
      window.location.href = `http://${host}/lpack/profile/${token}`;
    } else {
      host = BASEURL.URL;
      window.location.href = `${host}lpack/lpack/profile/${token}`;
    }
  };
  // To redirect to Ats
  const redirectToAts = () => {
    let host = get(window, "location.host", "");
    const token = localStorage.getItem(TOKEN_KEY);
    localStorage.removeItem("companyId");
    if (!token) {
      return;
    }
    if (host) {
      if (host.includes("localhost")) {
        host = ATS_URLS.local;
        window.location.href = `http://${host}/auth/${token}`;
      } else {
        if (host.includes("-dev-")) {
          host = ATS_URLS.dev;
          window.location.href = `https://${host}/auth/${token}`;
        } else if (host.includes("-qa-")) {
          host = ATS_URLS.qa;
          window.location.href = `https://${host}/auth/${token}`;
        } else if (host.includes("-stage-")) {
          host = ATS_URLS.stage;
          window.location.href = `https://${host}/auth/${token}`;
        } else {
          host = `${BASEURL.URL}ats`;
          // host = "twwstage.franklinreport.com/ats";
          window.location.href = `${host}/auth/${token}`;
        }
      }
    }
  };
  // To redirect to WPack
  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/wpack";
      // window.location.href = `http://${host}/${token}/${companyId}`;
      if (host.includes("localhost")) {
        host = "localhost:3003";
        window.location.href = `http://${host}/${token}/${companyId}`;
      } else {
        if (host.includes("-dev-")) {
          host = TDW_URL.WPACK.DEV;
        }
        if (host.includes("-qa-")) {
          host = TDW_URL.WPACK.QA;
        }
        if (host.includes("-stage-")) {
          host = TDW_URL.WPACK.STAGE;
        }
        if (host.includes("-twwstage-")) {
          host = `${BASEURL.URL}wpack`;
          // window.location.href = `http://${host}/${token}/${companyId}`;
        } else {
          host = `${BASEURL.URL}wpack`;
        }
        window.location.href = `${host}/${token}/${companyId}`;
      }
    }
  };
  const toggleContactListModal = () => {
    setContactListModal((contactListModal) => !contactListModal);
  };
  // To redirect to cats
  const redirectToCats = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/cats";
      host = `${BASEURL.URL}cats`;
      window.location.href = `${host}/${token}/${companyId}`;
    }
  };
  // To Logout
  const logOut = () => {
    dispatch(LoginActions.logOut());
    let host = get(window, "location.host", "");
    if (host) {
      if (host.includes("localhost")) {
        host = "localhost:3000";
        window.location.href = `http://${host}/logout`;
      } else {
        if (host.includes("-dev-")) {
          host = SPACK_DEV_URL;
        }
        if (host.includes("-qa-")) {
          host = SPACK_QA_URL;
        }
        if (host.includes("-stage-")) {
          host = SPACK_STAGE_URL;
        } else {
          host = BASEURL.URL;
          // host = "http://twwstage.franklinreport.com";
        }
        window.location.href = `${host}logout`;
      }
    }
  };
  // To Redirect to Franklink report url on click of Visit Franklin Report text
  const goToFranklinReport = () => {
    window.open(FRANKLIN_REPORT_URL, "_blank").focus();
    // window.location.href = FRANKLIN_REPORT_URL;
  };
  //   To redirect to settings
  const redirectToSettings = () => {
    const token = localStorage.getItem(TOKEN_KEY);
    let host = get(window, "location.host", "");
    let route = "";
    if (host) {
      route = `${BASEURL.URL}settings`;
    }
    window.location.href = `${route}/${token}`;
  };
  // Click functionality of For Job Seekers list items
  const onClickForJobSeekers = (key) => {
    switch (key) {
      case "searchJobs":
        toggleSearchJobModal();
        break;
      case "userProfile":
        redirectToLpack();
        break;
      case "redirectAts":
        redirectToAts();
        break;
      default:
        break;
    }
  };
  // Click functionality of For Employers list items
  const OnClickForEmployers = (key) => {
    switch (key) {
      case "postJobs":
        redirectToWpack();
        break;
      case "disCandidates":
        toggleTalentSearchModal();
        break;
      case "redirectCats":
        redirectToCats();
        break;
      case "redirectSettings":
        redirectToSettings();
        break;
      default:
        break;
    }
  };
  // Click functionality of My Account list items
  const OnClickMyAccount = (key) => {
    switch (key) {
      case "logOut":
        logOut();
        break;
      case "redirectSettings":
        redirectToSettings();
        break;
      default:
        break;
    }
  };

  return (
    <div>
      <Row className={classes.footerRow}>
        <Grid xs={3} className={`${classes.footerGrid} ${classes.logoGrid}`}>
          <img
            src="assets/images/trade-works.png"
            alt={strings.landing_page.titles.tradeWorks}
          />
          <div>
            <Text
              size={14}
              color={color.secondary_palette.grays.shadow_gray}
              style={{
                marginTop: "18px",
              }}
              family="NeutraText"
            >
              {strings.footer.copy_right}
            </Text>
          </div>
        </Grid>
        <Grid xs={2} className={`${classes.footerGrid} ${classes.textGrid}`}>
          <Text
            size={18}
            color={color.primary_palette.franklin_purple}
            style={{
              fontWeight: "600",
            }}
          >
            {strings.footer.forJobSeekers}
          </Text>
          {FOOTER_DATA.FOR_JOB_SEEKERS.map((item) => (
            <Text
              size={16}
              onClick={() => onClickForJobSeekers(item.key)}
              style={{
                padding: "5px 0",
                cursor: "pointer",
                lineHeight: "20px",
              }}
            >
              {item.title}
            </Text>
          ))}
        </Grid>
        <Grid xs={2} className={`${classes.footerGrid} ${classes.textGrid}`}>
          <Text
            size={18}
            color={color.primary_palette.franklin_purple}
            style={{
              fontWeight: "600",
            }}
          >
            {strings.footer.forEmployers}
          </Text>
          {FOOTER_DATA.FOR_EMPLOYERS.map((item) => (
            <Text
              size={16}
              onClick={() => OnClickForEmployers(item.key)}
              style={{
                padding: "5px 0",
                cursor: "pointer",
                lineHeight: "20px",
              }}
            >
              {item.title}
            </Text>
          ))}
        </Grid>
        <Grid xs={2} className={`${classes.footerGrid} ${classes.textGrid}`}>
          <Text
            size={18}
            color={color.primary_palette.franklin_purple}
            style={{
              fontWeight: "600",
            }}
          >
            {strings.footer.myAccount}
          </Text>
          {FOOTER_DATA.MY_ACCOUNT.map((item) => (
            <Text
              size={16}
              onClick={() => OnClickMyAccount(item.key)}
              style={{
                padding: "5px 0",
                cursor: "pointer",
                lineHeight: "20px",
              }}
            >
              {item.title}
            </Text>
          ))}
          <Text
            size={18}
            color={color.primary_palette.franklin_purple}
            style={{
              fontWeight: "600",
              marginTop: "40px",
              cursor: "pointer",
            }}
            onClick={goToFranklinReport}
          >
            {strings.footer.visitFranklinReport}
          </Text>
        </Grid>
        <Grid
          xs={2}
          className={`${classes.footerGrid} ${classes.textGrid} ${classes.contactGrid}`}
        >
          <Text
            size={18}
            color={color.primary_palette.franklin_purple}
            style={{
              fontWeight: "600",
              cursor: "pointer",
            }}
            onClick={toggleContactListModal}
          >
            {strings.footer.contactUs}
          </Text>
          <div>
            {FOOTER_DATA.CONTACT_US.map((item) => (
              <Text
                size={16}
                color={color.clammy_pearl}
                style={{
                  padding: "5px 0px 0px 0px",
                  lineHeight: "20px",
                  width: pxToRem(200),
                }}
              >
                {item}
              </Text>
            ))}
            <div
              className={classes.email}
              onClick={() => toggleContactListModal()}
              style={{ cursor: "pointer" }}
            >
              <EMAIL>
            </div>
          </div>
          <Text
            size={18}
            color={color.primary_palette.franklin_purple}
            style={{
              fontWeight: "600",
              margin: "12px 0 17px 0",
            }}
          >
            {strings.footer.privacyPolicy}
          </Text>
          {/* <div>
            <FaceBook_Outline_Icon
              color={color.primary_palette.franklin_purple}
              className={classes.socialMediaIcons}
            />
            <Instagram_Outline_Icon
              color={color.primary_palette.franklin_purple}
              className={classes.socialMediaIcons}
            />
            <Twitter_Outline_Icon
              color={color.primary_palette.franklin_purple}
              className={classes.socialMediaIcons}
            />
          </div> */}
        </Grid>
      </Row>
      {showSearchJobModal && (
        <CustomModal open className={classes.modal_popup}>
          <SearchJobModal user={userinfo} onClose={toggleSearchJobModal} />
        </CustomModal>
      )}
      {showTalentSrchModal && (
        <CustomModal open className={classes.modal_popup}>
          <TalentSearch
            user={userinfo}
            onClose={toggleTalentSearchModal}
            jobsListOptions={jobListing}
          />
        </CustomModal>
      )}
      {showCommunityModal && (
        <CustomModal open className={classes.modal_popup}>
          <VisitCommunity
            user={userinfo}
            onClose={toggleVisitCommunityModal}
            trades={trades}
            topLocations={topLocations}
          />
        </CustomModal>
      )}
      {contactListModal && (
        <CustomModal open className={classes.modal_popup}>
          <ContactUsModal
            onClose={() => toggleContactListModal()}
            userInfo={profileUserData}
          />
        </CustomModal>
      )}
    </div>
  );
};
function mapStateToProps(state) {
  return {
    userinfo: state?.LookupReducer?.userinfo,
    jobListing: state.Profile.jobListing,
    profileUserData: state.Profile.profileUserData,
    trades: state.Profile.trades,
    topLocations: state.Profile?.topLocations,
  };
}
export default connect(mapStateToProps)(footerStyles(Footer));
