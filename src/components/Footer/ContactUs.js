import React from "react";
import RequestStyles from "../Footer/styles/ContactUsStyles";
import CustomButton from "../navigations/custom_buttons";
import CloseIcon from "../data_display/icons/Close";
import strings from "../../utilities/strings";
import { Text } from "../common/ui_kit";
import { color } from "../../utilities/themes";
import { ConfigActions } from "../../redux/actions";
import { useDispatch } from "react-redux";

const ContactUsModal = ({ classes, onClose, userInfo }) => {
  const msgRef = React.createRef();
  const dispatch = useDispatch();
  // On click send button
  const send = () => {
    let userInput = {
      name: `${userInfo.firstName} ${userInfo.lastName}`,
      email: userInfo.email,
      message: msgRef.current.value,
    };
    dispatch(
      ConfigActions.contactUs(userInput, (resp) => {
        console.log("resp", resp);
      })
    );
    onClose();
  };

  // On click message clear icon
  const eraseMsg = () => {
    msgRef.current.value = "";
  };

  return (
    <div className={classes.contactUsModal}>
      <div className={classes.btnRight}>
        <CustomButton className={classes.crossBtn}>
          <CloseIcon onClick={onClose} />
        </CustomButton>
      </div>
      <div>
        <img
          src="assets/icons/trade_works_icon_small.svg"
          alt="TW"
          width="64"
        />
        <Text
          size={25}
          color={color.primary_palette.franklin_purple}
          style={{
            marginTop: "10px",
            textTransform: "uppercase",
          }}
        >
          {strings.footer.contactUs}
        </Text>
      </div>
      <div className={classes.formInputsDiv}>
        <Text
          size={12}
          color={color.primary_palette.franklin_purple}
          className={classes.formLabel}
        >
          {strings.footer.Name}
        </Text>
        <input
          type="text"
          className={classes.contactInput}
          defaultValue={`${userInfo.firstName} ${userInfo.lastName}`}
        />
        <Text
          size={12}
          color={color.primary_palette.franklin_purple}
          className={classes.formLabel}
        >
          {strings.recommendation.titles.email}
        </Text>
        <input
          type="email"
          className={classes.contactInput}
          defaultValue={userInfo.email}
        />
        <Text
          size={12}
          color={color.primary_palette.franklin_purple}
          className={classes.formLabel}
        >
          {strings.share_Tw.titles.message}
        </Text>
        <div style={{ position: "relative" }}>
          <textarea
            className={classes.contactInputText}
            style={{ height: "72px", resize: "none" }}
            ref={msgRef}
          ></textarea>
          <span className={classes.eraseMsg} onClick={eraseMsg}>
            x
          </span>
        </div>
        <div style={{ textAlign: "center" }}>
          <CustomButton className={classes.sendBtn} onClick={send}>
            {strings.share_Tw.titles.send}
          </CustomButton>
        </div>
      </div>
    </div>
  );
};

export default RequestStyles(ContactUsModal);
