import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  scrollbar: {
    minHeight: pxToRem(528),
  },
  gamification_width: {
    width: pxToRem(1016),
    height: pxToRem(650),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    position: "relative",
    padding: pxToRem(5),
    "&:focus": {
      outline: "none !important",
    },
  },
  peral_img: {
    backgroundImage: `url("assets/images/Cream Pearl.png")`,
    backgroundSize: "cover",
    textAlign: "center",
    width: pxToRem(23),
    height: pxToRem(23),
    margin: "0 auto",
    ...tradework_theme.typography.styles.Helvetica,
    fontWeight: "bold",
    fontSize: pxToRem(10.8),
    lineHeight: pxToRem(22),
  },
  scroll_img: {
    backgroundImage: `url("assets/images/Scroll.png")`,
    backgroundSize: "cover",
    textAlign: "center",
    width: pxToRem(100),
    height: pxToRem(127),
  },
  scroll_bg: {
    flexGrow: 1,
    width: "23%",
  },
  padding_top: {
    paddingTop: pxToRem(15),
  },
  scroll_text: {
    padding: `${pxToRem(44)} ${pxToRem(0)}`,
    display: "block",
    marginTop: pxToRem(-18),
  },
  block: {
    display: "block",
    padding: `${pxToRem(7)} 0`,
  },
  peral_spacing: {
    width: pxToRem(50),
    textAlign: "center",
  },
  textCenter: {
    textAlign: "center",
  },
  padding_top_50: {
    paddingTop: pxToRem(50),
  },
  gamification_table: {
    width: "77%",
  },
  table1: {
    textAlign: "right",
    width: pxToRem(231),
    paddingRight: pxToRem(10),
  },
  table2: {
    textAlign: "center",
    paddingBottom: pxToRem(15),
  },
  table3: {
    textAlign: "center",
    paddingBottom: pxToRem(15),
  },
  subTable: {
    width: pxToRem(100),
    height: pxToRem(60),
    margin: "0 auto",
    alignItems: "center",
    display: "flex",
    paddingTop: pxToRem(10),
  },
  visibility: {
    visibility: "hidden",
    // paddingTop: pxToRem(5),
    paddingBottom: pxToRem(5),
  },
  visibility_sec2: {
    visibility: "hidden",
    // paddingTop: pxToRem(5),
    paddingBottom: pxToRem(5),
  },
  padding_5: {
    paddingTop: pxToRem(5),
    paddingBottom: pxToRem(5),
    position: "relative",
  },
  main_section_pading: {
    paddingTop: pxToRem(5),
    paddingBottom: pxToRem(2),
  },
  main_section_pading_pitch: {
    paddingTop: pxToRem(5),
    paddingBottom: pxToRem(3),
  },
  activity_num1: {
    ...tradework_theme.typography.styles.Helvetica,
    fontWeight: "bold",
    position: "relative",
  },
  check_mark: {
    position: "absolute",
    right: pxToRem(18),
  },
  heading_height: {
    height: pxToRem(55),
    paddingBottom: pxToRem(10),
  },
  subTable_activity: {
    width: "100%",
    height: pxToRem(60),
    margin: "0 auto",
    alignItems: "center",
    display: "flex",
    paddingTop: pxToRem(10),
    justifyContent: "flex-end",
  },
  tableMission: {
    borderRight: `solid ${pxToRem(1.2)} ${
      color.primary_palette.franklin_purple
    }`,
    position: "relative",
  },
  border_rightPos: {
    position: "relative",
    paddingTop: pxToRem(35),
    "&::after": {
      position: "absolute",
      width: pxToRem(1),
      height: "86%",
      backgroundColor: color.primary_palette.franklin_purple,
      right: 0,
      top: pxToRem(100),
      content: "''",
    },
    "&::before": {
      position: "absolute",
      width: "98%",
      height: pxToRem(1),
      backgroundColor: color.primary_palette.franklin_purple,
      left: pxToRem(90),
      top: pxToRem(100),
      content: "''",
    },
  },
  points_earned_width: {
    width: "80%",
    position: "relative",
  },
  star_icon: {
    position: "absolute",
    top: 0,
    right: pxToRem(-54),
  },
  completion_goals_width: {
    display: "flex",
    justifyContent: "flex-end",
    paddingRight: pxToRem(28),
    alignItems: "flex-end",
  },
  border_bottom: {
    borderBottom: `solid ${pxToRem(1.2)} ${color.primary_palette.black}`,
  },
  margin_right: {
    marginRight: pxToRem(14),
  },
  helevetica: {
    ...tradework_theme.typography.styles.Helvetica,
    fontWeight: "bold",
  },
  process: {
    display: "flex",
    justifyContent: "flex-end",
    paddingRight: pxToRem(30),
    paddingTop: pxToRem(6),
  },
  percentage: {
    fontSize: pxToRem(25),
    color: color.form_colors.sucess_color,
  },
  color_green: {
    color: color.form_colors.sucess_color,
  },
  points_style: {
    color: color.gamification_green,
    fontSize: pxToRem(25),
  },
  process_points: {
    position: "relative",
    paddingRight: pxToRem(20),
    "&::after": {
      position: "absolute",
      content: "''",
      width: pxToRem(56.47),
      height: pxToRem(1.2),
      backgroundColor: color.primary_palette.black,
      right: pxToRem(42),
      top: 0,
    },
  },
  text_captial: {
    textTransform: "uppercase",
  },
  dilution: {
    width: pxToRem(191),
    borderRadius: pxToRem(11.2),
    border: `solid ${pxToRem(1.6)} ${color.greyish_brown}`,
    paddingTop: pxToRem(10),
    marginTop: pxToRem(12),
  },
  decoration: {
    textDecoration: "underline",
    position: "relative",
  },
  static_data: {
    flexGrow: 1,
    width: "50%",
  },
  padding_10: {
    padding: `${pxToRem(5)} ${pxToRem(10)}`,
  },
  levels: {
    width: pxToRem(167),
    borderRadius: pxToRem(11.2),
    border: `solid ${pxToRem(1.6)} ${color.greyish_brown}`,
    margin: `${pxToRem(0)} ${pxToRem(15)}`,
    paddingTop: pxToRem(30),
    marginTop: pxToRem(12),
  },
  TextRight: {
    textAlign: "right",
  },
  padding_left_5: {
    paddingLeft: pxToRem(5),
  },
  Static_heading: {
    height: pxToRem(28),
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
    display: "flex",
  },
  padding_scroll: {
    padding: `${pxToRem(0)} ${pxToRem(10)}`,
    paddingRight: pxToRem(0),
  },
  table_padding: {
    padding: `${pxToRem(0)} ${pxToRem(20)}`,
  },
  flag_icon: {
    position: "absolute",
    // top: pxToRem(-36),
    left: pxToRem(100),
    width: pxToRem(58),
    height: pxToRem(65),
  },
  bold: {
    fontWeight: "bold",
  },
  width_color: {
    width: pxToRem(95),
    height: pxToRem(8),
    display: "inline-block",
    marginTop: pxToRem(5),
    marginBottom: pxToRem(15),
  },
  color_maroon: {
    backgroundColor: color.uniformed_rotten,
  },
  color_orange: {
    backgroundColor: color.conversational_lemon,
  },
  color_yellow: {
    backgroundColor: color.melodic_chartreuse,
  },
  color_light_green: {
    backgroundColor: color.accent_colors_and_one_offs.benjamin_green,
  },
  color_dark_green: {
    backgroundColor: color.menial_cerulean,
  },
  padding_bottom_9: {
    paddingBottom: pxToRem(9),
  },
  star_required: {
    fontSize: pxToRem(30),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.Helvetica,
    fontWeight: "bold",
    position: "relative",
    top: pxToRem(15),
    right: pxToRem(17),
  },
  field_required: {
    position: "absolute",
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.Helvetica,
    fontWeight: "bold",
    fontSize: pxToRem(30),
    top: 0,
  },
  star_points: {
    fontSize: pxToRem(30),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.Helvetica,
    fontWeight: "bold",
    verticalAlign: "top",
  },
  star_points_abs: {
    position: "absolute",
    left: pxToRem(6),
    top: pxToRem(-3),
  },
  btnRight: {
    textAlign: "right",
  },
  crossBtn: {
    minWidth: pxToRem(30),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
      padding: pxToRem(10),
    },
  },
  sub_text: {
    ...tradework_theme.typography.styles.Helvetica,
    color: color.primary_palette.black,
    position: "absolute",
    paddingLeft: pxToRem(4),
  },
  padding_left_9: {
    paddingLeft: pxToRem(9),
  },
  padding_top_5: {
    paddingLeft: pxToRem(5),
  },
  green_text: {
    color: color.form_colors.sucess_color,
  },
  communityLogo: {
    width: pxToRem(500),
    marginBottom: pxToRem(10),
  },
});

export default styles;
