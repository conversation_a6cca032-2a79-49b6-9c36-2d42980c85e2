import React, { useEffect, useState } from "react";
import { map, get, filter, find } from "lodash";
import { color, pxToRem } from "../../utilities/themes";
import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import GamificationStyles from "./styles/gamificationStyles";
import CustomModal from "../inputs/custom_modal";
import CustomScrollbars from "../data_display/custom_scoll_modal";
import { useSelector } from "react-redux";
import Close_Icon from "../data_display/icons/Close";
import CustomButton from "../navigations/custom_buttons";

function Gamification(props) {
  const { classes, showGamification, handleGamification } = props;
  const [values, setValues] = useState({
    staticMandatoryFields: [1, 4, 5, 6],
  });
  const gamificationData = useSelector((state) => state.Profile.gamification);

  useEffect(() => {
    if (gamificationData) {
      const numOfOfficeShots = find(gamificationData.mainSection, {
        activity: "Our Offices shots (pictures)",
      });
      const numOfWatercooler = find(gamificationData.mainSection, {
        activity: "Water Cooler (videos)",
      });
      const numOfCompanyHighlights = find(gamificationData.mainSection, {
        activity: "Our Culture-Highlights",
      });

      const socialMediaFeeds = find(gamificationData.companyPitch, {
        activity: "Social Media Feeds",
      });

      const mainSection = map(gamificationData.mainSection, (each) => {
        return each.activity;
      });
      const companyPitch = map(gamificationData.companyPitch, (each) => {
        return each.activity;
      });
      const necessaryToComplete = map(gamificationData.mainSection, (each) => {
        return {
          value: each.necessarytoActivityComplete,
          checked: each.necessarytoActivityComplete <= each.currentlyPublished,
        };
      });
      const necessaryToComplete2 = map(
        gamificationData.companyPitch,
        (each) => {
          return {
            value: each.necessarytoActivityComplete,
            checked:
              each.necessarytoActivityComplete <= each.currentlyPublished,
          };
        }
      );
      const currentlyDisplayed = map(gamificationData.mainSection, (each) => {
        return each.currentlyPublished;
      });
      const currentlyDisplayed2 = map(gamificationData.companyPitch, (each) => {
        return each.currentlyPublished;
      });

      const pointsPerDisplayedActivity = map(
        gamificationData.mainSection,
        (each) => {
          return each.coninsGarentedPerActivity;
        }
      );
      const pointsPerDisplayedActivity2 = map(
        gamificationData.companyPitch,
        (each) => {
          return each.coninsGarentedPerActivity;
        }
      );
      const pointsOriginallyEarned = map(
        gamificationData.mainSection,
        (each) => {
          return each.conisnEarned;
        }
      );
      const pointsOriginallyEarned2 = map(
        gamificationData.companyPitch,
        (each) => {
          return each.conisnEarned;
        }
      );
      const currentPoints = map(gamificationData.mainSection, (each) => {
        return each.currentPoints;
      });
      const currentPoints2 = map(gamificationData.companyPitch, (each) => {
        return each.currentPoints;
      });
      setValues({
        ...values,
        currentPoints,
        pointsOriginallyEarned,
        pointsPerDisplayedActivity,
        currentlyDisplayed,
        necessaryToComplete,
        currentPoints2,
        necessaryToComplete2,
        currentlyDisplayed2,
        pointsOriginallyEarned2,
        pointsPerDisplayedActivity2,
        activityData,
        mainSection,
        companyPitch,
        numOfCompanyHighlights,
        numOfOfficeShots,
        numOfWatercooler,
        socialMediaFeeds,
      });
    }
  }, [gamificationData]);

  const {
    currentPoints,
    pointsOriginallyEarned,
    pointsPerDisplayedActivity,
    currentlyDisplayed,
    necessaryToComplete,
    activityData,
    mainSection,
    companyPitch,
    currentPoints2,
    necessaryToComplete2,
    currentlyDisplayed2,
    pointsOriginallyEarned2,
    pointsPerDisplayedActivity2,
    numOfCompanyHighlights,
    numOfOfficeShots,
    numOfWatercooler,
    staticMandatoryFields,
    socialMediaFeeds,
  } = values;

  const returnCurrentItem = (value, idx, section) => {
    const mainTotals = {
      0: 1,
      1: 2,
      2: 1,
      3: 1,
      4: 3,
      5: 3,
      6: 2,
      7: 2,
    };
    const companyPitchTotals = {
      0: 1,
      1: 1,
      2: 0,
      3: 4,
      4: 2,
    };
    const total = section === 1 ? mainTotals[idx] : companyPitchTotals[idx];
    if (Number(value) < Number(total) && Number(value) !== 0) {
      return (
        <div>
          <span className={classes.green_text}>{value}</span>
          <span className={classes.sub_text}> of {total}</span>
        </div>
      );
    }
    return value;
  };

  const time_data = [
    "30 days",
    "90 days",
    "180 Days",
    "1 year",
    "1.5 years",
    "2 years +",
  ];
  const redit_data = ["100 %", "80%", "60%", "40%", "20%", "0%"];
  const level_data = ["Starter", "Fair", "Good", "Very Good", "Excellent"];
  const level_num = [
    "0 - 200",
    "201 - 400",
    "401 - 600",
    "601 - 750",
    "751 - 850",
  ];

  return (
    <CustomModal open={true}>
      <div className={classes.gamification_width}>
        <div className={classes.btnRight}>
          <CustomButton className={classes.crossBtn}>
            <Close_Icon onClick={handleGamification} />
          </CustomButton>
        </div>
        <div className={classes.padding_scroll}>
          <div className={classes.textCenter}>
            {/* <Text
              size={22}
              family="OptimaLT"
              color={color.primary_palette.black}
              transform="uppercase"
              className={classes.padding_left_9}
            >
              {strings.gamification.titles.contribute}
            </Text> */}
            {/* <div>
              <div
                className={`${classes.width_color} ${classes.color_maroon}`}
              ></div>
              <div
                className={`${classes.width_color} ${classes.color_orange}`}
              ></div>
              <div
                className={`${classes.width_color} ${classes.color_yellow}`}
              ></div>
              <div
                className={`${classes.width_color} ${classes.color_light_green}`}
              ></div>
              <div
                className={`${classes.width_color} ${classes.color_dark_green}`}
              ></div>
            </div> */}
            <img
              src="assets/images/Contribute to the Trade community.svg"
              className={classes.communityLogo}
            />
          </div>
          <CustomScrollbars className={classes.scrollbar}>
            <Row className={classes.table_padding}>
              <div className={classes.scroll_bg}>
                <Text
                  size={20}
                  family="StencilStd"
                  color={color.primary_palette.black}
                  className={classes.padding_top_50}
                >
                  {strings.gamification.titles.mission}
                </Text>
                <Row className={classes.padding_top}>
                  <div className={classes.scroll_img}>
                    <Row className={classes.scroll_text}>
                      <Text
                        size={28.8}
                        family="Helvetica"
                        color={color.primary_palette.black}
                        transform="uppercase"
                        className={`${classes.bold} ${classes.padding_bottom_9}`}
                      >
                        {get(gamificationData, "precentage", 10)}%
                      </Text>
                      <Text
                        size={11.1}
                        family="gillsans_sb"
                        color={color.primary_palette.black}
                        transform="uppercase"
                        className={classes.text_captial}
                      >
                        complete
                      </Text>
                    </Row>
                  </div>
                  <div>
                    <Row className={classes.block}>
                      <Row>
                        <div className={classes.peral_spacing}>
                          <div className={classes.peral_img}>
                            {get(numOfOfficeShots, "currentlyPublished", "")}
                          </div>
                          <Text
                            size={7.4}
                            family="gillsans_sb"
                            color={color.primary_palette.black}
                          >
                            {strings.gamification.titles.office_shoots}
                          </Text>
                        </div>
                        <div className={classes.peral_spacing}>
                          <div className={classes.peral_img}>
                            {" "}
                            {get(
                              numOfCompanyHighlights,
                              "currentlyPublished",
                              ""
                            )}
                          </div>
                          <Text
                            size={7.4}
                            family="gillsans_sb"
                            color={color.primary_palette.black}
                          >
                            {strings.gamification.titles.Company_highlights}
                          </Text>
                        </div>
                      </Row>
                      <Row>
                        <div
                          className={`${classes.peral_spacing} ${classes.padding_top}`}
                        >
                          <div className={classes.peral_img}>
                            {get(numOfWatercooler, "currentlyPublished", "")}
                          </div>
                          <Text
                            size={7.4}
                            family="gillsans_sb"
                            color={color.primary_palette.black}
                          >
                            {strings.gamification.titles.water_cooler}
                          </Text>
                        </div>
                        <div
                          className={`${classes.peral_spacing} ${classes.padding_top}`}
                        >
                          <div className={classes.peral_img}>
                            {get(socialMediaFeeds, "currentlyPublished", "")}
                          </div>
                          <Text
                            size={7.4}
                            family="gillsans_sb"
                            color={color.primary_palette.black}
                          >
                            {strings.gamification.titles.social_media}
                          </Text>
                        </div>
                      </Row>
                    </Row>
                  </div>
                </Row>
              </div>
              <Row className={classes.gamification_table}>
                <Row className={classes.border_rightPos}>
                  <div className={classes.tableMission}>
                    <Text
                      size={24}
                      color={color.primary_palette.franklin_purple}
                      family="gillsans_sb"
                      transform="uppercase"
                      className={`${classes.textCenter} ${classes.heading_height} ${classes.completion_goals_width}`}
                    >
                      <img
                        src="assets/icons/chequered_flag.svg"
                        className={classes.flag_icon}
                      />{" "}
                      {strings.gamification.titles.completion_goals}
                    </Text>
                    <Row className={classes.borderTable1}>
                      <div className={`${classes.table1} `}>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={`${classes.subTable_activity}`}
                        >
                          {strings.gamification.titles.activity}
                        </Text>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.main_section_pading}
                        >
                          {strings.gamification.titles.main_section}
                        </Text>
                        {map(mainSection, (item, idx) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5}  ${classes.table_flex} ${classes.flex_end}`}
                            style={{
                              marginTop:
                                item === "Current Team & Alumni" && pxToRem(3),
                            }}
                          >
                            {item}
                            {staticMandatoryFields.includes(Number(idx)) && (
                              <span className={classes.field_required}>*</span>
                            )}
                          </Text>
                        ))}
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.main_section_pading_pitch}
                        >
                          {strings.gamification.titles.company_pitch}
                        </Text>
                        {map(companyPitch, (item) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5}  ${classes.table_flex} ${classes.flex_end}`}
                          >
                            {item}
                          </Text>
                        ))}
                      </div>
                      <div
                        className={`${classes.table2} ${classes.border_bottom}`}
                      >
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.subTable}
                        >
                          {strings.gamification.titles.Necessary}
                        </Text>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility}
                        >
                          1
                        </Text>
                        {map(necessaryToComplete, (item) => (
                          <Text
                            size={16.8}
                            color={
                              item.checked
                                ? color.form_colors.sucess_color
                                : color.primary_palette.black
                            }
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {get(item, "value", "")}
                            {item.checked && (
                              <img
                                src="assets/images/Checkmark Kelly Green.svg"
                                className={classes.check_mark}
                              />
                            )}
                          </Text>
                        ))}
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility_sec2}
                        >
                          1
                        </Text>
                        {map(necessaryToComplete2, (item) => (
                          <Text
                            size={16.8}
                            color={
                              item.checked
                                ? color.form_colors.sucess_color
                                : color.primary_palette.black
                            }
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {get(item, "value", "")}
                            {item.checked && (
                              <img
                                src="assets/images/Checkmark Kelly Green.svg"
                                className={classes.check_mark}
                              />
                            )}
                          </Text>
                        ))}
                      </div>
                      <div
                        className={`${classes.table3} ${classes.border_bottom} ${classes.margin_right}`}
                      >
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.subTable}
                        >
                          {strings.gamification.titles.currently}
                        </Text>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility}
                        >
                          1
                        </Text>
                        {map(currentlyDisplayed, (item, idx) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {returnCurrentItem(item, idx, 1)}
                          </Text>
                        ))}
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility_sec2}
                        >
                          1
                        </Text>
                        {map(currentlyDisplayed2, (item, idx) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {returnCurrentItem(item, idx, 2)}
                          </Text>
                        ))}
                      </div>
                    </Row>
                    <div className={classes.process}>
                      <Text
                        size={22}
                        color={color.primary_palette.black}
                        className={`${classes.helevetica} ${classes.textCenter}`}
                      >
                        <span className={classes.color_green}>
                          {get(gamificationData, "progress", "")}
                        </span>{" "}
                        / {get(gamificationData, "totalProgress", "")} <br />
                        Goals Completed <br />
                        <span className={classes.percentage}>
                          {get(gamificationData, "precentage", 10)}%
                        </span>
                      </Text>
                    </div>
                  </div>
                  <div className={classes.tableCoins}>
                    <Text
                      size={24}
                      color={color.primary_palette.franklin_purple}
                      family="gillsans_sb"
                      transform="uppercase"
                      className={`${classes.table_main_text} ${classes.textCenter} ${classes.heading_height} ${classes.points_earned_width}`}
                    >
                      {strings.gamification.titles.points_earned}
                      <img
                        src="assets/images/stard.png"
                        className={classes.star_icon}
                      />
                    </Text>
                    <Row className={`${classes.borderTable} `}>
                      <div
                        className={`${classes.table3} ${classes.padding_left} ${classes.flex_center}`}
                      >
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.subTable}
                        >
                          {strings.gamification.titles.Points_per}
                        </Text>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility}
                        >
                          1
                        </Text>
                        {map(pointsPerDisplayedActivity, (item) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {item}
                          </Text>
                        ))}
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility_sec2}
                        >
                          1
                        </Text>
                        {map(pointsPerDisplayedActivity2, (item) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {item}
                          </Text>
                        ))}
                      </div>
                      <div className={classes.table3}>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.subTable}
                        >
                          {strings.gamification.titles.points_original}
                        </Text>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility}
                        >
                          1
                        </Text>
                        {map(pointsOriginallyEarned, (item) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {item}
                          </Text>
                        ))}
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility_sec2}
                        >
                          1
                        </Text>
                        {map(pointsOriginallyEarned2, (item) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {item}
                          </Text>
                        ))}
                      </div>
                      <div className={`${classes.table3}`}>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.subTable}
                        >
                          {strings.gamification.titles.current_point}
                          <span className={classes.star_required}>*</span>
                        </Text>
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility}
                        >
                          1
                        </Text>
                        {map(currentPoints, (item) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {item}
                          </Text>
                        ))}
                        <Text
                          size={16.8}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={classes.visibility_sec2}
                        >
                          1
                        </Text>
                        {map(currentPoints2, (item) => (
                          <Text
                            size={16.8}
                            color={color.primary_palette.black}
                            className={`${classes.padding_5} ${classes.activity_num1}`}
                          >
                            {item}
                          </Text>
                        ))}
                      </div>
                    </Row>
                    <div
                      className={`${classes.process} ${classes.process_points}`}
                    >
                      <Text
                        size={18}
                        color={color.primary_palette.black}
                        className={`${classes.helevetica} ${classes.textCenter}`}
                      >
                        <span className={classes.points_style}>
                          {get(gamificationData, "currentPoint", 0)} Points
                        </span>
                        <br /> Toward Good
                        <br />
                        Citizen Status
                      </Text>
                    </div>
                  </div>
                </Row>
              </Row>
            </Row>
            <div>
              <Text
                size={16.6}
                family="gillsans_sb"
                color={color.primary_palette.black}
                className={classes.text_captial}
              >
                {strings.gamification.titles.current_total}
              </Text>
              <Text
                size={18.7}
                family="gillsans_r"
                color={color.primary_palette.black}
                className={classes.padding_top_5}
              >
                <span className={classes.star_points}>*</span>
                {strings.gamification.titles.points_des}
              </Text>
              <Row>
                <div className={classes.dilution}>
                  <Text
                    size={14.4}
                    color={color.primary_palette.franklin_purple}
                    family="gillsans_sb"
                    className={`${classes.text_captial} ${classes.decoration} ${classes.textCenter}`}
                  >
                    <span
                      className={`${classes.star_points} ${classes.star_points_abs}`}
                    >
                      *
                    </span>
                    {strings.gamification.titles.dilution}
                  </Text>
                  <Row className={classes.padding_10}>
                    <div
                      className={`${classes.static_data} ${classes.textCenter}`}
                    >
                      <Text
                        size={12}
                        color={color.primary_palette.franklin_purple}
                        family="gillsans_sb"
                        className={classes.Static_heading}
                      >
                        {strings.gamification.titles.time}
                      </Text>
                      {time_data.map((item) => (
                        <Text
                          size={14}
                          color={color.primary_palette.black}
                          className={`${classes.table_flex} ${classes.flex_end}`}
                        >
                          {item}
                        </Text>
                      ))}
                    </div>
                    <div
                      className={`${classes.static_data} ${classes.textCenter}`}
                    >
                      <Text
                        size={12}
                        color={color.primary_palette.franklin_purple}
                        family="gillsans_sb"
                      >
                        {strings.gamification.titles.Credit}
                      </Text>
                      {redit_data.map((item) => (
                        <Text
                          size={14}
                          color={color.primary_palette.black}
                          className={`${classes.table_flex} ${classes.flex_end}`}
                        >
                          {item}
                        </Text>
                      ))}
                    </div>
                  </Row>
                </div>
                <div className={classes.levels}>
                  <Text
                    size={14.4}
                    color={color.primary_palette.franklin_purple}
                    family="gillsans_sb"
                    className={`${classes.text_captial} ${classes.decoration} ${classes.textCenter}`}
                  >
                    {strings.gamification.titles.levels}
                  </Text>
                  <Row className={classes.padding_10}>
                    <div
                      className={`${classes.static_data} ${classes.TextRight}`}
                    >
                      {level_data.map((item) => (
                        <Text
                          size={14}
                          color={color.primary_palette.black}
                          className={`${classes.table_flex} ${classes.flex_end}`}
                        >
                          {item}
                        </Text>
                      ))}
                    </div>
                    <div
                      className={`${classes.static_data} ${classes.padding_left_5}`}
                    >
                      {level_num.map((item) => (
                        <Text
                          size={14}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          className={`${classes.table_flex} ${classes.flex_end}`}
                        >
                          {item}
                        </Text>
                      ))}
                    </div>
                  </Row>
                </div>
              </Row>
            </div>
          </CustomScrollbars>
        </div>
      </div>
    </CustomModal>
  );
}

export default GamificationStyles(Gamification);
