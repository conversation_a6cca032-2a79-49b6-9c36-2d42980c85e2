import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { get } from "lodash";

import { color } from "../../utilities/themes";
import { ProfileActions } from "../../redux/actions";

import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import CompanyNameChangeStyle from "./styles/companyNameChangeStyle";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomInputCount from "../inputs/custom_input_count";
import CustomModal from "../inputs/custom_modal";

function CompanyNameChange(props) {
  const { classes, handleCompanyNameChange, handleToggleModal, companyName } = props;
  const dispatch = useDispatch();
  const [values, setValues] = useState({
    name: "",
    companyId: localStorage.getItem('companyId')
  });
  const { name, companyId } = values;

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setValues({ ...values, [name]: value });
  };

  const handleSubmit = () => {
    dispatch(
      ProfileActions.sendCompanyNameChangeRequest(
        {
          ...values,
        },
        (resp) => {
          if (!get(resp, "error.error", false)) {
            handleToggleModal("showCompanyNameChange", "showCompanyNameChangeSuccess");
          }
        }
      )
    );
  };

  const clearFields = (name) => () => {
    setValues({ ...values, [name]: "" });
  };
  return (
    <CustomModal open={true}>
      <div className={classes.companyName_width}>
        <div className={classes.btnRight}>
          <CustomButton className={classes.crossBtn}>
            <Close_Icon onClick={handleCompanyNameChange} />
          </CustomButton>
        </div>
        <div className={`${classes.textCenter} ${classes.margin_15}`}>
          <img
            src="assets/icons/pencil.svg"
            className={classes.envelope_width}
          />
        </div>
        <div className={classes.padding_bottom_20}>
          <Text
            size={20}
            color={color.primary_palette.franklin_purple}
            className={classes.textCenter}
          >
            {strings.modals.titles.rename_your_company}
          </Text>
        </div>
        <div className={classes.padding_bottom_15}>
          <Text
            size={14}
            color={color.primary_palette.franklin_purple}
            className={classes.textCenter}
          >
            {strings.modals.titles.current_company_name}
          </Text>
          <Text
            size={16}
            color={color.primary_palette.black}
            className={classes.textCenter}
          >
            {companyName}
          </Text>
        </div>
        <div>
          <div className={classes.checkBox_area}>
            <div className={`${classes.name_spacing} ${classes.padding_bottom_28}`}>
              <Text size={14} className={classes.textCenter}
                color={color.primary_palette.franklin_purple}>
                {strings.modals.titles.new_company_name}
              </Text>
              <Close_Icon
                onClick={clearFields("name")}
                className={classes.field_clear}
              />
              <CustomInputCount
                name="name"
                enableClear
                defaultValue={name}
                onBlur={handleInputChange}
                className={classes.input_box}
              />
            </div>
            <div className={classes.textCenter}>
              <CustomButton className={classes.sendBtn} onClick={handleSubmit}>
                {strings.modals.titles.Submit}
              </CustomButton>
            </div>
          </div>
        </div>
      </div>
    </CustomModal>
  );
}

export default CompanyNameChangeStyle(CompanyNameChange);
