import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { pxToRem, color } from "../../../utilities/themes";
// import tradework_theme, { pxToRem, color } from "../../../utilities/themes";

const styles = withStyles({
  currentlyLoggedUser: {
    width: pxToRem(558),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    "&:focus": {
      outline: "none",
    },
  },
  btnRight: {
    textAlign: "right",
  },
  cross_mark: {
    fontSize: pxToRem(15),
    // padding: pxToRem(15),
    position: "relative",
    right: pxToRem(-14),
    minWidth: 0,
  },
  firstName_field: {
    width: pxToRem(309),
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    borderRadius: pxToRem(1),
    marginTop: pxToRem(4),
    backgroundColor: color.primary_palette.white,
    "& .MuiInputBase-input": {
      padding: 0,
      paddingTop: pxToRem(2),
      paddingBottom: pxToRem(2),
      paddingLeft: pxToRem(6),
    },
    "& .MuiInput-underline": {
      "&:before": {
        display: "none",
      },
      "&:after": {
        display: "none",
      },
    },
  },
  width_10: {
    width: "14%",
  },
  width_90: {
    width: "90%",
  },
  subject_field: {
    width: "100%",
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    borderRadius: pxToRem(2),
    backgroundColor: color.primary_palette.white,
    "& .MuiInputBase-input": {
      padding: 0,
      paddingTop: pxToRem(2),
      paddingBottom: pxToRem(2),
      paddingLeft: pxToRem(6),
    },
    "& .MuiInput-underline": {
      "&:before": {
        display: "none",
      },
      "&:after": {
        display: "none",
      },
    },
  },
  TextArea: {
    width: "99%",
    paddingLeft: pxToRem(6),
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.NeutraText,
    backgroundColor: color.primary_palette.white,
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    borderRadius: pxToRem(6),
    height: `${pxToRem(90)} !important`,
    resize: "none",
  },
  currentlyLoggedUser_field: {
    padding: `${pxToRem(0)} ${pxToRem(30)}`,
  },
  padding_top_17: {
    paddingTop: pxToRem(17),
  },
  padding_top_10: {
    paddingTop: pxToRem(10),
  },
  txtCenter: {
    textAlign: "center",
  },
  sendBtn: {
    width: pxToRem(131),
    height: pxToRem(25),
    borderRadius: pxToRem(12.5),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    backgroundColor: `${color.primary_palette.white} !important`,
    marginTop: pxToRem(10),
    marginBottom: pxToRem(18),
    "& .MuiButton-label": {
      color: color.primary_palette.pine_green,
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
    },
  },
});

export default styles;
