import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  pingAdmin_width: {
    width: pxToRem(605),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    paddingBottom: pxToRem(27),
    "&:focus": {
      outline: "none",
    },
  },
  btnRight: {
    textAlign: "right",
  },
  crossBtn: {
    minWidth: pxToRem(30),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
      padding: pxToRem(10),
    },
  },
  envelope_width: {
    height: pxToRem(83),
    paddingBottom: pxToRem(15),
  },
  textCenter: {
    textAlign: "center",
  },
  checkBox_area: {
    width: "85%",
    margin: "0 auto",
    paddingBottom: pxToRem(11),
    paddingTop: pxToRem(8),
    "& .MuiTypography-root": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.avenir_light,
      color: color.big_tar,
      paddingLeft: pxToRem(9),
    },
    "& .MuiCheckbox-root": {
      padding: 0,
    },
  },
  textArea: {
    height: `${pxToRem(44)} !important`,
    width: "99%",
    resize: "none",
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
  },
  form_width: {
    width: "88%",
    margin: "0 auto",
  },
  input_box: {
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    height: pxToRem(27),
    width: "100%",
    color: color.big_tar,
    backgroundColor: color.primary_palette.white,
    "& .MuiInputBase-input": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.NeutraText,
      color: color.big_tar,
      paddingLeft: pxToRem(8),
    }
  },
  errorCheck: {
    border: `solid ${pxToRem(2)} ${color.primary_palette.christmas_red} !important`,
  },
  errorCheckRequired:{
    color: `${color.primary_palette.christmas_red} !important`,
  },
  textarea_spacing: {
    paddingBottom: pxToRem(12),
  },
  name_spacing: {
    paddingBottom: pxToRem(26),
    position: "relative",
  },
  sendBtn: {
    width: pxToRem(176),
    height: pxToRem(37),
    borderRadius: pxToRem(18.6),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    "& .MuiButton-label": {
      ...tradework_theme.typography.styles.gillsans_r,
      fontSize: pxToRem(21),
      color: color.form_colors.royal_purple_1,
    },
    "&:hover": {
      backgroundColor: `${color.button_hover} !important`,
    },
  },

  // message sent
  message_width: {
    width: pxToRem(475),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    "&:focus": {
      outline: "none",
    },
  },
  text_transform: {
    textTransform: "uppercase",
  },
  message_sent_des: {
    paddingTop: pxToRem(20),
    paddingBottom: pxToRem(34),
  },
  margin_15: {
    marginTop: pxToRem(-15),
  },
  field_clear: {
    fontSize: pxToRem(7),
    position: "absolute",
    top: pxToRem(26),
    right: pxToRem(10),
    zIndex: 9,
    cursor: "pointer",
  },  
  checkBox_required: {
    fontSize: pxToRem(14),
    color: color.primary_palette.franklin_purple,
    position: "absolute",
    // left: pxToRem(0),
    // top: pxToRem(20),
    zIndex: 9    
  },
  padding_Top_26: {
    paddingTop: pxToRem(26)
  }
});
export default styles;
