import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  newcompanyModal_width: {
    width: pxToRem(500),
    paddingTop: pxToRem(30),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.grays.fafa_gray,
    "&:focus": {
      outline: "none",
    },
  },
  btnRight: {
    textAlign: "right",
  },
  crossBtn: {
    minWidth: pxToRem(30),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(20),
    },
  },
  textCenter: {
    textAlign: "center",
  },
  envelope_width: {
    width: pxToRem(75),
  },
  margin_top_50: {
    marginTop: pxToRem(-50),
  },
  padding_top_20: {
    paddingTop: pxToRem(20),
    width: "80%",
    margin: "0 auto",
  },
  emailBtn: {
    width: pxToRem(218),
    height: pxToRem(37),
    borderRadius: pxToRem(18.6),
    marginTop: pxToRem(40),
    border: `solid ${pxToRem(2.1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    marginBottom: pxToRem(35),
    "& .MuiButton-label": {
      ...tradework_theme.typography.styles.gillsans_sb,
      fontSize: pxToRem(21),
      color: color.form_colors.royal_purple_1,
    },
    "&:hover": {
      backgroundColor: `${color.button_hover} !important`,
    },
  },
  pingBtn: {
    width: pxToRem(160),
    height: pxToRem(25),
    borderRadius: pxToRem(18.6),
    marginLeft: pxToRem(64),
    marginTop: pxToRem(40),
    border: `solid ${pxToRem(2.1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    marginBottom: pxToRem(35),
    "& .MuiButton-label": {
      ...tradework_theme.typography.styles.gillsans_sb,
      fontSize: pxToRem(16),
      color: color.form_colors.royal_purple_1,
    },
    "&:hover": {
      backgroundColor: `${color.button_hover} !important`,
    },
  },
  modal_flag: {
    height: pxToRem(90),
  },
  padding_Top_bottom: {
    paddingTop: pxToRem(19),
    paddingBottom: pxToRem(12),
  },
  padding_bottom_35: {
    paddingBottom: pxToRem(35),
  },
  margin_top_60: {
    marginTop: pxToRem(-60),
  },
  min_height: {
    minHeight: pxToRem(245),
  },
});
export default styles;
