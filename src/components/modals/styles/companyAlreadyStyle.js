import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  comapny_width: {
    width: pxToRem(490),
    // border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.grays.fafa_gray,
    "&:focus": {
      outline: "none",
    },
  },
  btnRight: {
    textAlign: "right",
  },
  crossBtn: {
    minWidth: pxToRem(30),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(20),
    },
  },
  textCenter: {
    textAlign: "center",
  },
  info_icon: {
    width: pxToRem(25),
    verticalAlign: "middle",
    paddingRight: pxToRem(5),
  },
  okBtn: {
    width: pxToRem(182),
    height: pxToRem(43),
    borderRadius: pxToRem(27),
    marginTop: pxToRem(30),
    marginBottom: pxToRem(25),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    padding: 0,
    "& .MuiButton-label": {
      color: color.primary_palette.franklin_purple,
      ...tradework_theme.typography.styles.avenir_black_r,
      textTransform: "none",
      fontSize: pxToRem(20),
    },
  },
});
export default styles;
