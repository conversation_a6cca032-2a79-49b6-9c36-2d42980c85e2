import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { pxToRem, color } from "../../../utilities/themes";

const styles = withStyles({
  copperModal: {
    width: pxToRem(590),
    border: `solid ${pxToRem(2)} ${color.primary_palette.free_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
  },
  btnRight: {
    textAlign: "right",
  },
  crossBtn: {
    minWidth: pxToRem(30),
    padding: pxToRem(15),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
    },
  },
  warn_icon: {
    marginRight: pxToRem(6),
  },
  errorAlign: {
    marginLeft: pxToRem(124),
    marginTop: pxToRem(10),
  },
  textCenter: {
    textAlign: "center",
  },
  copperModalContent: {
    padding: `${pxToRem(0)} ${pxToRem(50)}`,
  },
  vignette_link: {
    flexGrow: 1,
    width: "35%",
  },
  vignette_link_radio: {
    flexGrow: 1,
    width: "65%",
  },
  inputField: {
    ...tradework_theme.typography.styles.avenir_light,
    marginLeft: pxToRem(0),
    "&:hover": {
      "&:not(.Mui-disabled)": {
        "&:after": {
          borderBottom: "transparent !important",
        },
        "&:before": {
          borderBottom: "transparent !important",
        },
      },
    },
    "&:before": {
      borderBottom: "0 !important",
    },
    "& .MuiInputBase-root": {
      width: "100%",
      border: "none",
      "&:hover": {
        border: "none !important",
      },
      "&:active": {
        border: "none !important",
      },
      "&:focus": {
        border: "none !important",
      },
      "&:focus-within": {
        border: "none !important",
      },
    },
    "& .MuiSvgIcon-root": {
      display: "none",
    },

    "& .MuiInputBase-input": {
      "&::placeholder": {
        fontSize: pxToRem(14),
        color: color.secondary_palette.grays.shadow_gray,
        // fontStyle: "oblique",
      },
    },
  },
  bodereRed: {
    "& .Mui-error": {
      borderBottom: `solid ${pxToRem(1)} ${
        color.primary_palette.christmas_red
      } !important`,
      border: `${pxToRem(0)} !important`,
    },
  },
  borderBlack: {
    borderBottom: `solid ${pxToRem(1)} ${
      color.secondary_palette.grays.shadow_gray
    }`,
  },
  linkRadio: {
    "& .MuiTypography-root ": {
      fontSize: pxToRem(15),
      color: color.secondary_palette.grays.drag_text,
      ...tradework_theme.typography.styles.avenir_light,
    },
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
    },
  },
  vignette_linkAlign: {
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
  },
  vignette_link_input: {
    alignSelf: "flex-end",
  },
  save_btn: {
    marginTop: pxToRem(22),
    marginBottom: pxToRem(22),
    width: pxToRem(208),
    height: pxToRem(35),
    borderRadius: pxToRem(22),
    border: `solid ${pxToRem(1.6)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    "& .MuiButton-label": {
      ...tradework_theme.typography.styles.avenir_black_r,
      fontSize: pxToRem(16.3),
      color: color.primary_palette.franklin_purple,
    },
  },
  save_btn_disable: {
    marginTop: pxToRem(40),
    marginBottom: pxToRem(38),
    width: pxToRem(208),
    height: pxToRem(35),
    borderRadius: pxToRem(22),
    backgroundColor: color.primary_palette.white,
    border: `solid ${pxToRem(1.6)} ${color.primary_palette.disable_border}`,
    "& .MuiButton-label": {
      ...tradework_theme.typography.styles.avenir_black_r,
      fontSize: pxToRem(16.3),
    },
  },
  justifyCenter: {
    justifyContent: "center",
  },
  drag_text: {
    textAlign: "center",
  },
  cropperDiv: {
    position: "relative",
    width: pxToRem(408),
    height: pxToRem(296),
    margin: "0 auto",
    border: `solid ${pxToRem(2)} ${color.primary_palette.free_purple}`,
    marginTop: pxToRem(16),
    marginBottom: pxToRem(18),
  },
  slider_width: {
    width: pxToRem(293),
    margin: "0 auto",
    "& .MuiSlider-thumb": {
      width: pxToRem(22),
      height: pxToRem(22),
      marginTop: pxToRem(-10),
    },
    "& .MuiSlider-track": {
      backgroundColor: color.secondary_palette.grays.shadow_gray,
    },
    "& .MuiSlider-rail": {
      backgroundColor: color.secondary_palette.grays.shadow_gray,
      opacity: 1,
    },
  },
  margin_top_20: {
    marginTop: pxToRem(-20),
  },
  margin_left_12: {
    marginLeft: pxToRem(-12),
  },

  justifyLeft: {
    flexGrow: 1,
  },
  justifyRight: {
    flexGrow: 1,
    justifyContent: "flex-end",
  },
  btn_spacing: {
    padding: `${pxToRem(33)} ${pxToRem(0)}`,
  },
  choose_btn: {
    width: pxToRem(208),
    height: pxToRem(35),
    borderRadius: pxToRem(22),
    border: `solid ${pxToRem(1.6)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    "& .MuiButton-label": {
      fontSize: pxToRem(16.3),
      ...tradework_theme.typography.styles.avenir_sb,
      color: color.primary_palette.franklin_purple,
    },
    "&:hover": {
      backgroundColor: `${color.button_hover} !important`,
    },
  },
});

export default styles;
