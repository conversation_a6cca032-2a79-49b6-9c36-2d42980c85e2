import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { pxToRem, color } from "../../../utilities/themes";

const styles = withStyles({
  upagrde_width: {
    width: pxToRem(725),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    "&:focus": {
      outline: "none",
    },
  },
  btnRight: {
    textAlign: "right",
  },
  crossBtn: {
    minWidth: pxToRem(30),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(20),
      padding: pxToRem(10),
    },
  },
  text_captial: {
    textTransform: "uppercase",
  },
  txtCenter: {
    textAlign: "center",
  },
  form_text: {
    ...tradework_theme.typography.styles.avenir_black_r,
  },
  padding_space: {
    padding: `${pxToRem(0)} ${pxToRem(35)}`,
  },
  checkBox: {
    "& .MuiTypography-root": {
      fontSize: pxToRem(12),
      ...tradework_theme.typography.styles.avenir_light,
      textAlign: "center",
      height: pxToRem(32),
    },
  },
  request_options: {
    flexGrow: 1,
    width: "20%",
    padding: `${pxToRem(0)} ${pxToRem(6)}`,
  },
  border_bottom: {
    borderBottom: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
  },
  checkBoxs_two: {
    "& label": {
      margin: `${pxToRem(0)} !important`,
    },
  },
  thank_text: {
    paddingTop: pxToRem(30),
  },
  requestBtn: {
    width: pxToRem(161),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    fontSize: pxToRem(20),
    color: `${color.primary_palette.franklin_purple} !important`,
    padding: 0,
    ...tradework_theme.typography.styles.avenir_black_r,
    marginBottom: pxToRem(30),
    marginTop: pxToRem(20),
    "&:hover": {
      backgroundColor: `${color.button_hover} !important`
    },
  },
  padding_request: {
      paddingTop: pxToRem(20),
      paddingBottom: pxToRem(10)
  },
  main_spacing: {
      paddingBottom: pxToRem(18)
  }
});

export default styles;
