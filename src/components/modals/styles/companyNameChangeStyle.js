import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  companyName_width: {
    width: pxToRem(350),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    paddingBottom: pxToRem(27),
    "&:focus": {
      outline: "none",
    },
  },
  btnRight: {
    textAlign: "right",
  },
  crossBtn: {
    minWidth: pxToRem(30),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
      padding: pxToRem(10),
    },
  },
  envelope_width: {
    width: pxToRem(45),
    paddingBottom: pxToRem(15),
  },
  textCenter: {
    textAlign: "center",
  },
  checkBox_area: {
    width: "85%",
    margin: "0 auto",
    paddingBottom: pxToRem(11),
    paddingTop: pxToRem(8),
    "& .MuiTypography-root": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.avenir_light,
      color: color.big_tar,
      paddingLeft: pxToRem(9),
    },
    "& .MuiCheckbox-root": {
      padding: 0,
    },
  },
  textArea: {
    height: `${pxToRem(44)} !important`,
    width: "99%",
    resize: "none",
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
  },
  form_width: {
    width: "88%",
    margin: "0 auto",
  },
  input_box: {
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    height: pxToRem(27),
    width: "100%",
    color: color.big_tar,
    backgroundColor: color.primary_palette.white,
    borderRadius: "2",
    "& .MuiInputBase-input": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.NeutraText,
      color: color.big_tar,
      paddingLeft: pxToRem(8),
    },
  },
  textarea_spacing: {
    paddingBottom: pxToRem(12),
  },
  name_spacing: {
    paddingBottom: pxToRem(26),
    position: "relative",
  },
  sendBtn: {
    width: pxToRem(140),
    height: pxToRem(28),
    borderRadius: pxToRem(18.6),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    backgroundColor: color.primary_palette.white,
    "& .MuiButton-label": {
      ...tradework_theme.typography.styles.gillsans_sb,
      fontSize: pxToRem(15),
      color: color.primary_palette.pine_green,
    },
    "&:hover": {
      backgroundColor: `${color.button_hover} !important`,
    },
  },

  // message sent
  message_width: {
    width: pxToRem(475),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    "&:focus": {
      outline: "none",
    },
  },
  text_transform: {
    textTransform: "uppercase",
  },
  message_sent_des: {
    paddingTop: pxToRem(20),
    paddingBottom: pxToRem(34),
  },
  margin_15: {
    marginTop: pxToRem(-15),
  },
  field_clear: {
    fontSize: pxToRem(7),
    position: "absolute",
    top: pxToRem(26),
    right: pxToRem(10),
    zIndex: 9,
    cursor: "pointer",
  },
  padding_bottom_20: {
    paddingBottom: pxToRem(20),
  },
  padding_bottom_15: {
    paddingBottom: pxToRem(15),
  },
  padding_bottom_28: {
    paddingBottom: pxToRem(28),
  },
  padding_bottom_10: {
    paddingBottom: pxToRem(10),
  },
});
export default styles;
