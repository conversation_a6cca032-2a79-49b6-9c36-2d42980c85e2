import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  newJoinModal_width: {
    width: pxToRem(490),
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.grays.fafa_gray,
    "&:focus": {
      outline: "none"
    }
  },
  btnRight: {
    textAlign: "right",
  },
  crossBtn: {
    minWidth: pxToRem(30),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(20),
    },
  },
  textCenter: {
    textAlign: "center",
  },
  envelope_width: {
    width: pxToRem(75),
  },
  margin_top_63: {
    marginTop: pxToRem(-63),
  },
  padding_top_11: {
    paddingTop: pxToRem(11),
  },
  emailBtn: {
    width: pxToRem(200),
    height: pxToRem(30),
    borderRadius: pxToRem(20),
    border: `solid ${pxToRem(1)} ${color.form_colors.blueberry_purple}`,
    backgroundColor: color.primary_palette.white,
    "& .MuiButton-label": {
        ...tradework_theme.typography.styles.avenir_black_r,
        fontSize: pxToRem(15),
        color: color.primary_palette.franklin_purple,
    },
    "&:hover": {
      backgroundColor: `${color.button_hover} !important`
    }
  },
  padding_Top_bottom: {
    paddingTop: pxToRem(19),
    paddingBottom: pxToRem(12),
  },
  padding_bottom_35: {
    paddingBottom: pxToRem(35),
  },
});
export default styles;
