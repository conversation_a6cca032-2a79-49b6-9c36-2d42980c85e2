import React from "react";
import { get, find } from "lodash";
import { useSelector, useDispatch } from "react-redux";

import { color } from "../../utilities/themes";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import NewJoinModalStyles from "./styles/new_joinModalStyle";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomModal from "../inputs/custom_modal";
import { ProfileActions } from "../../redux/actions";

function NewJoinModal(props) {
  const { classes, redirectApp, businessCard } = props;
  const loggedInUser = useSelector((state) => state.Profile.companyinfo);
  const dispatch = useDispatch();

  const triggerEmail = () => {
    const headQuarterCard = find(businessCard, { isHeadOffice: true });
    const dataToSubmit = {
      isTeamMember: true,
      name: get(loggedInUser, "tradeWorkUrl", ""),
      email: get(loggedInUser, "email", ""),
      companyName: get(headQuarterCard, "companyId.name", ""),
      companyId: get(headQuarterCard, "companyId._id", ""),
    };
    dispatch(ProfileActions.resendEmailTM(dataToSubmit));
  };
  return (
    <>
      <CustomModal open={true}>
        <div className={classes.newJoinModal_width}>
          <div className={classes.btnRight}>
            <CustomButton className={classes.crossBtn} onClick={redirectApp}>
              <Close_Icon />
            </CustomButton>
          </div>
          <div className={classes.textCenter}>
            <img
              src="assets/images/Invite_Envelope.png"
              className={classes.envelope_width}
            />
          </div>
          <div className={classes.padding_bottom_35}>
            <Text
              size={25}
              color={color.primary_palette.franklin_purple}
              family="Zapfino"
              className={classes.textCenter}
            >
              {strings.modals.titles.thank_you}
            </Text>
            <div className={classes.margin_top_63}>
              <Text
                size={25}
                color={color.primary_palette.franklin_purple}
                family="gillsans_light"
                className={classes.textCenter}
              >
                {strings.modals.titles.joining_trade}
              </Text>
              <div className={classes.padding_top_11}>
                <Text
                  size={18}
                  color={color.primary_palette.black}
                  family="gillsans_light"
                  className={classes.textCenter}
                >
                  {strings.modals.titles.thank_you_des1}
                </Text>
                <Text
                  size={18}
                  color={color.primary_palette.black}
                  family="gillsans_light"
                  className={classes.textCenter}
                >
                  {strings.modals.titles.thank_you_des2}
                </Text>
              </div>
              <Text
                size={18}
                color={color.primary_palette.black}
                family="gillsans_light"
                className={`${classes.textCenter} ${classes.padding_Top_bottom}`}
              >
                {strings.modals.titles.email_info}
              </Text>
              <div className={classes.textCenter}>
                <CustomButton
                  className={classes.emailBtn}
                  onClick={triggerEmail}
                >
                  {strings.modals.titles.email_link}
                </CustomButton>
              </div>
            </div>
          </div>
        </div>
      </CustomModal>
    </>
  );
}

export default NewJoinModalStyles(NewJoinModal);
