import React, { useEffect, useState } from "react";

import { pxToRem, color } from "../../utilities/themes";
import styleSheet from "./styles/currentlyLoggedStyles";
import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import Close_Icon from "../data_display/icons/Close";
import CustomButton from "../navigations/custom_buttons";
import CustomModal from "../inputs/custom_modal";
import CustomTextField from "../inputs/custom_textfields";
import CustomTextArea from "../inputs/custom_text_area";
import { get } from "lodash";
import { useSelector } from "react-redux";

function CurrentlyLoggedUser(props) {
  const { classes, onClose, officeData } = props;
  const [values, setValues] = useState({});
  const userData = useSelector((state) => state.Profile.companyinfo);

  useEffect(() => {
    setValues({
      ...values,
      userName: `${get(userData, "firstName", "")} ${get(
        userData,
        "lastName",
        ""
      )}`,
      email: get(userData, "email", ""),
      subject: "Hello!",
    });
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setValues({
      ...values,
      [name]: value,
    });
  };

  const { userName, email, subject, message } = values;
  return (
    <>
      <CustomModal open={true}>
        <div className={classes.currentlyLoggedUser}>
          <div className={classes.btnRight}>
            <CustomButton className={classes.crossBtn}>
              <Close_Icon className={classes.cross_mark} onClick={onClose} />
            </CustomButton>
          </div>
          <div className={classes.currentlyLoggedUser_field}>
            <Row>
              <div>
                <Row>
                  <Text
                    size={15}
                    family="avenir_bold"
                    color={color.primary_palette.franklin_purple}
                    className={classes.width_10}
                  >
                    To:
                  </Text>
                  <Text
                    size={15}
                    family="avenir_sb"
                    color={color.primary_palette.black}
                  >
                    {get(officeData, "email", "")}
                  </Text>
                </Row>
                <Row>
                  <Text
                    size={15}
                    family="avenir_bold"
                    color={color.primary_palette.franklin_purple}
                    className={classes.width_10}
                  >
                    From:
                  </Text>
                  <div className={classes.width_90}>
                    <CustomTextField
                      className={classes.firstName_field}
                      value={userName}
                    />
                    <CustomTextField
                      className={classes.firstName_field}
                      value={email}
                    />
                  </div>
                </Row>
              </div>
              <div>
                <img
                  src="assets/images/invite_stamp.png"
                  className={classes.tradeworks_icon}
                />
              </div>
            </Row>
            <div className={classes.padding_top_17}>
              <Text
                size={15}
                family="avenir_bold"
                color={color.primary_palette.franklin_purple}
                transform="uppercase"
              >
                Subject:
              </Text>
              <CustomTextField
                className={classes.subject_field}
                value={subject}
                name="subject"
                onChange={handleChange}
              />
            </div>
            <div className={classes.padding_top_10}>
              <Text
                size={15}
                family="avenir_bold"
                color={color.primary_palette.franklin_purple}
                transform="uppercase"
              >
                Message
              </Text>
              <CustomTextArea
                className={classes.TextArea}
                name="message"
                defaultValue={message}
                onBlur={handleChange}
              />
            </div>
            <div className={classes.txtCenter}>
              <CustomButton className={classes.sendBtn} onClick={onClose}>
                Send
              </CustomButton>
            </div>
          </div>
        </div>
      </CustomModal>
    </>
  );
}

export default styleSheet(CurrentlyLoggedUser);
