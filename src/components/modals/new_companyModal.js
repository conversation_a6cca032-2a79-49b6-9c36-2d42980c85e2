import React from "react";
import { useSelector } from "react-redux";
import { get } from "lodash";

import { color } from "../../utilities/themes";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import NewCompanyModalStyles from "./styles/new_companyModalStyles";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomModal from "../inputs/custom_modal";

function NewCompanyModal(props) {
  const { classes, redirectToEditProfile, redirectToProfile } = props;
  const userInfo = useSelector((state) => state.Profile.companyinfo);

  return (
    <>
      <CustomModal open={true}>
        <div className={classes.newcompanyModal_width}>
          {/* <div className={classes.btnRight}>
            <CustomButton
              className={classes.crossBtn}
              onClick={redirectToProfile}
            >
              <Close_Icon />
            </CustomButton>
          </div> */}
          <div className={classes.min_height}>
          <div className={classes.textCenter}>
            <img
              src="assets/images/Wel Done.svg"
              // className={classes.envelope_width}
            />
          </div>
            {/* <Text
              size={20.6}
              color={color.primary_palette.franklin_purple}
              family="Zapfino"
              className={classes.textCenter}
            >
              {strings.modals.titles.well_done}
            </Text> */}
            <Text
              size={20.6}
              color={color.primary_palette.franklin_purple}
              family="Zapfino"
              className={`${classes.textCenter}`}
            >
              {get(userInfo, "firstName", "")} {get(userInfo, "lastName", "")}
            </Text>
            </div>
            <div className={classes.margin_top_50}>
              <Text
                size={16}
                color={color.primary_palette.black}
                family="avenir_sb"
                className={classes.textCenter}
              >
                {strings.modals.titles.thankyouFinish}
              </Text>
              <div className={classes.padding_top_20}>
                <Text
                  size={16}
                  color={color.primary_palette.black}
                  family="avenir_sb"
                  className={classes.textCenter}
                >
                  {strings.modals.titles.thankyouFinish_des}
                </Text>
              </div>
              <div className={classes.textCenter}>
                <CustomButton
                  className={classes.emailBtn}
                  onClick={redirectToEditProfile}
                >
                  {strings.modals.titles.go_to_profile}
                </CustomButton>
              </div>
            </div>
        </div>
      </CustomModal>
    </>
  );
}

export default NewCompanyModalStyles(NewCompanyModal);
