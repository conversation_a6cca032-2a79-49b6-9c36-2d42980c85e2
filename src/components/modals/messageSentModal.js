import React, { useEffect } from "react";

import { color } from "../../utilities/themes";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import PingAdminStyles from "./styles/pingAdminStyles";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomModal from "../inputs/custom_modal";

function MessageModal(props) {
  const { classes, handleToggleModal, type } = props;
  useEffect(() => {
    activateTimer();
  }, []);
  const activateTimer = () => {
    setTimeout(() => {
      handleToggleModal("showPingAdminSuccess");
    }, 3000);
  };
  return (
    <CustomModal open={true}>
      <div className={classes.message_width}>
        <div className={classes.btnRight}>
          <CustomButton className={classes.crossBtn}>
            <Close_Icon
              onClick={() => handleToggleModal("showPingAdminSuccess")}
            />
          </CustomButton>
        </div>
        <div className={classes.textCenter}>
          <img
            src="assets/images/Mailbox.jpg"
            className={classes.envelope_width}
          />
        </div>
        <div className={classes.padding_Top_26}>
          <Text
            size={30}
            family="gillsans_sb"
            color={color.primary_palette.franklin_purple}
            className={`${classes.textCenter} ${classes.text_transform}`}
          >
            {strings.modals.titles.message_sent}
          </Text>
          <Text
            size={20}
            family="avenir_sb"
            color={color.primary_palette.black}
            className={`${classes.textCenter} ${classes.message_sent_des}`}
          >
            {type === "public"
              ? strings.modals.titles.message_sent_desc_public
              : strings.modals.titles.message_sent_des}
          </Text>
        </div>
      </div>
    </CustomModal>
  );
}

export default PingAdminStyles(MessageModal);
