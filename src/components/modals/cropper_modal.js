import React, { useState, useEffect } from "react";
import { get } from "lodash";
import { useDispatch } from "react-redux";

import { pxToRem, color } from "../../utilities/themes";
import { Slider } from "@material-ui/core";
import styleSheet from "./styles/cropper_modal_styles";
import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import Close_Icon from "../data_display/icons/Close";
import CustomButton from "../navigations/custom_buttons";
import CustomCropper from "../cropper/custom_cropper";
import getCroppedImg from "../cropper/getCroppedNewImg";

import { ApiActions } from "../../redux/actions";

function CroppingModal(props) {
  const {
    classes,
    onClose,
    image,
    setCroppedImage,
    chooseNew,
    shape,
    closeOnSave,
    multipleVal,
    errorMessage,
    clearImage,
    edit,
  } = props;
  const dispatch = useDispatch();

  const [values, setValues] = useState({
    errors: {},
    zoom: 2,
    crop: { x: 0, y: 0 },
  });

  const showCroppedImage = async () => {
    const { croppedAreaPixels } = values;
    try {
      ApiActions.request(dispatch);
      let urlObj = "";
      urlObj = image;
      const imageFile = await getCroppedImg(
        urlObj,
        croppedAreaPixels,
        "#FFFFFF"
      );
      const blobUrl = URL.createObjectURL(imageFile);
      setValues({ ...values });
      setCroppedImage(blobUrl, imageFile);
      closeOnSave && onClose();
      ApiActions.success(dispatch);
    } catch (e) {
      ApiActions.failure(dispatch);
      console.error(e);
    }
  };

  const onCropComplete = (coords) => {
    const croppedAreaPixels = get(coords, "main.croppedAreaPixels", "");
    setValues({ ...values, croppedAreaPixels });
  };

  const setCrop = (value) => {
    setValues({ ...values, crop: value });
  };

  const setZoom = (e, value) => {
    setValues({ ...values, zoom: value });
  };

  const returnAspectRatio = (shape) => {
    switch (shape) {
      case "round":
        return 1;
      case "rect": {
        return 10 / 11;
      }
      case "rect2": {
        return 9 / 5;
      }
      case "cover": {
        return 9 / 3;
      }
      case "newRect": {
        return 5 / 7;
      }
      case "rect2_2": {
        return 31 / 20;
      }
      default:
        return 1;
    }
  };

  const { zoom, crop } = values;
  return (
    <>
      <div className={classes.copperModal}>
        <div className={classes.btnRight}>
          <CustomButton className={classes.crossBtn}>
            <Close_Icon className={classes.cross_mark} onClick={onClose} />
          </CustomButton>
        </div>
        <div className={classes.copperModalContent}>
          {edit ? (
            <Text
              size={30}
              family="avenir_light"
              color={color.primary_palette.black}
              className={`${classes.textCenter} ${classes.margin_top_20}`}
              transform="uppercase"
            >
              • PICK A GREAT IMAGE •
            </Text>
          ) : (
            <Text
              size={30}
              family="avenir_light"
              color={color.primary_palette.black}
              className={`${classes.textCenter} ${classes.margin_top_20}`}
              transform="uppercase"
            >
              • CROP YOUR IMAGE •
            </Text>
          )}
          <div
            className={classes.cropperDiv}
            style={{ backgroundColor: "white" }}
          >
            <CustomCropper
              image={image}
              crop={crop}
              multiple={multipleVal || false}
              zoom={zoom}
              cropShape={shape}
              aspect={returnAspectRatio(shape)}
              onCropChange={setCrop}
              onCropComplete={onCropComplete}
              onZoomChange={setZoom}
              showGrid={false}
            />
          </div>
          <div className={classes.slider_width}>
            <Slider
              value={zoom}
              min={0.4}
              max={3}
              step={0.1}
              aria-labelledby="Zoom"
              classes={classes.slider}
              onChange={setZoom}
            />
          </div>
          <div className={classes.drag_text}>
            <Text
              size={15}
              color={color.secondary_palette.grays.drag_text}
              family="avenir_roman"
            >
              Drag the image to desired position.
            </Text>
            <Text
              size={15}
              color={color.secondary_palette.grays.drag_text}
              family="avenir_roman"
            >
              Move the zoom handle to change photo size.
            </Text>
          </div>
          <div>
            {errorMessage && (
              <Row className={classes.errorAlign}>
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  // className={`${classes.errorMessage} ${errorStyles}`}
                >
                  {errorMessage}
                </Text>
              </Row>
            )}
          </div>
          <Row className={classes.btn_spacing}>
            <Row className={classes.justifyLeft}>
              <CustomButton className={classes.choose_btn} onClick={chooseNew}>
                SWAP IMAGE
              </CustomButton>
            </Row>
            <Row className={classes.justifyRight}>
              <CustomButton
                className={classes.choose_btn}
                onClick={showCroppedImage}
              >
                SAVE & CONTINUE
              </CustomButton>
            </Row>
          </Row>
        </div>
      </div>
    </>
  );
}

export default styleSheet(CroppingModal);
