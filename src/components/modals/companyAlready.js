import React from "react";

import { color } from "../../utilities/themes";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import CompanyAlreadyStyle from "./styles/companyAlreadyStyle";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomModal from "../inputs/custom_modal";

function CompanyAlready(props) {
  const { classes, setCompany, handleAlertModalClose } = props;
  return (
    <>
      <CustomModal open={true}>
        <div className={classes.comapny_width}>
          <div className={classes.btnRight}>
            <CustomButton className={classes.crossBtn}>
              <Close_Icon onClick={handleAlertModalClose} />
            </CustomButton>
          </div>
          <div className={classes.textCenter}>
            <img src="assets/images/Little_Company.png" />
            <Text
              size={20}
              color={color.primary_palette.black}
              family="avenir_sb"
            >
              <img
                src="assets/icons/info_1.svg"
                className={classes.info_icon}
              />
              {strings.modals.titles.company_already}
            </Text>
            <Text
              size={20}
              color={color.primary_palette.black}
              family="avenir_sb"
            >
              {strings.modals.titles.company_already_des1}
            </Text>
            <Text
              size={20}
              color={color.primary_palette.black}
              family="avenir_sb"
            >
              {strings.modals.titles.company_already_des2}
            </Text>
            <CustomButton className={classes.okBtn} onClick={handleAlertModalClose}>
              {" "}
              {strings.modals.titles.ok}
            </CustomButton>
          </div>
        </div>
      </CustomModal>
    </>
  );
}

export default CompanyAlreadyStyle(CompanyAlready);
