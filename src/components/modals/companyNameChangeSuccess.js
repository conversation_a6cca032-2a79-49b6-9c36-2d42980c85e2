import React from "react";

import { color } from "../../utilities/themes";

import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import CompanyNameChangeStyle from "./styles/companyNameChangeStyle";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomModal from "../inputs/custom_modal";

function CompanyNameChangeSuccess(props) {
  const { classes, handleCompanyNameChangeSuccess } = props;
  return (
    <CustomModal open={true}>
      <div className={classes.companyName_width}>
        <div className={classes.btnRight}>
          <CustomButton className={classes.crossBtn}>
            <Close_Icon onClick={handleCompanyNameChangeSuccess} />
          </CustomButton>
        </div>
        <div className={classes.padding_bottom_10}>
          <Text
            size={25}
            color={color.primary_palette.franklin_purple}
            className={classes.textCenter}
          >
            {strings.modals.titles.thank_you}
          </Text>
        </div>
        <div className={classes.padding_bottom_28}>
          <Text
            size={14}
            color={color.primary_palette.black}
            className={classes.textCenter}
          >
            {strings.modals.titles.allow_24}
          </Text>
        </div>
        <div>
          <div className={classes.checkBox_area}>
            <div className={classes.textCenter}>
              <CustomButton className={classes.sendBtn} onClick={handleCompanyNameChangeSuccess}>
                {strings.modals.titles.Great}
              </CustomButton>
            </div>
          </div>
        </div>
      </div>
    </CustomModal>
  );
}

export default CompanyNameChangeStyle(CompanyNameChangeSuccess);
