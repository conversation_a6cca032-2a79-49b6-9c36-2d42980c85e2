import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { get, find } from "lodash";

import { color } from "../../utilities/themes";
import { ProfileActions } from "../../redux/actions";

import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import PingAdminStyles from "./styles/pingAdminStyles";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomCheckbox from "../inputs/custom_checkbox";
import CustomTextArea from "../inputs/custom_text_area";
import CustomInputCount from "../inputs/custom_input_count";
import CustomModal from "../inputs/custom_modal";
import Element_Required_Icon from "../../components/data_display/icons/ElementRequiered";

function PingAdminModal(props) {
  const { classes, handlePingAdmin, handleToggleModal, businessCard } = props;
  const dispatch = useDispatch();
  const [values, setValues] = useState({
    message: "",
    email: "",
    name: "",
    pingValues: [],
  });
  const { message, name, email, pingValues } = values;
  const userData = useSelector((state) => state.Profile.companyinfo);

  useEffect(() => {
    if (!userData) {
      return;
    }
    const { firstName, email, lastName } = userData;
    const { companyId } = businessCard;
    const headQuarter = find(companyId.address, { type: "headquarters" });
    setValues({
      ...values,
      email,
      name: firstName + lastName,
      companyEmail: get(headQuarter, "email", ""),
      companyId: get(headQuarter, "_id", ""),
    });
  }, [userData]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === "pingValues") {
      if (pingValues.includes(value)) {
        const updatedPingValues = pingValues.filter((each) => {
          return each !== value;
        });
        setValues({ ...values, pingValues: updatedPingValues });
        return;
      }
      pingValues.push(value);
      setValues({ ...values, pingValues });
      return;
    }
    setValues({ ...values, [name]: value });
  };

  const handleSubmit = () => {
    dispatch(
      ProfileActions.pingAdminTeamMember(
        {
          ...values,
        },
        (resp) => {
          if (!get(resp, "error.error", false)) {
            handleToggleModal("showPingAdmin", "showPingAdminSuccess");
          }
        }
      )
    );
  };

  const clearFields = (name) => () => {
    setValues({ ...values, [name]: "" });
  };
  return (
    <CustomModal open={true}>
      <div className={classes.pingAdmin_width}>
        <div className={classes.btnRight}>
          <CustomButton className={classes.crossBtn}>
            <Close_Icon onClick={handlePingAdmin} />
          </CustomButton>
        </div>
        <div className={`${classes.textCenter} ${classes.margin_15}`}>
          <img
            src="assets/images/Ping Icon.svg"
            className={classes.envelope_width}
          />
        </div>
        <div className={classes.padding_bottom_35}>
          <Text
            size={30}
            color={color.primary_palette.franklin_purple}
            className={classes.textCenter}
          >
            {strings.modals.titles.ping_Admin}
          </Text>
          <div className={classes.checkBox_area}>
            <CustomCheckbox
              name="pingValues"
              onChange={handleInputChange}
              value={strings.modals.titles.check_box1}
              checked={pingValues.includes(strings.modals.titles.check_box1)}
              label={strings.modals.titles.check_box1}
            />
            <CustomCheckbox
              name="pingValues"
              onChange={handleInputChange}
              value={strings.modals.titles.check_box2}
              checked={pingValues.includes(strings.modals.titles.check_box2)}
              label={strings.modals.titles.check_box2}
            />
            <CustomCheckbox
              name="pingValues"
              onChange={handleInputChange}
              value={strings.modals.titles.check_box3}
              checked={pingValues.includes(strings.modals.titles.check_box3)}
              label={strings.modals.titles.check_box3}
            />
            <CustomCheckbox
              name="pingValues"
              onChange={handleInputChange}
              value={strings.modals.titles.check_box4}
              checked={pingValues.includes(strings.modals.titles.check_box4)}
              label={strings.modals.titles.check_box4}
            />
            <CustomCheckbox
              name="pingValues"
              onChange={handleInputChange}
              value={strings.modals.titles.check_box5}
              checked={pingValues.includes(strings.modals.titles.check_box5)}
              label={strings.modals.titles.check_box5}
            />
          </div>
          <div className={classes.form_width}>
            <div className={classes.textarea_spacing}>
              <Text size={15} color={color.Nero}>
                {strings.modals.titles.message}
              </Text>

              <CustomTextArea
                name="message"
                defaultValue={message}
                onBlur={handleInputChange}
                className={classes.textArea}
              />
            </div>
            <div className={classes.name_spacing}>
              <Element_Required_Icon
                className={classes.checkBox_required}
              />
              <Text size={15} color={color.Nero}>
                {strings.modals.titles.your_name}
              </Text>
              <Close_Icon
                onClick={clearFields("name")}
                className={classes.field_clear}
              />
              <CustomInputCount
                name="name"
                enableClear
                defaultValue={name}
                onBlur={handleInputChange}
                className={classes.input_box}
              />
            </div>
            <div className={classes.name_spacing}>
              <Text size={15} color={color.Nero}>
                {strings.modals.titles.your_email}
              </Text>
              <Close_Icon
                onClick={clearFields("email")}
                className={classes.field_clear}
              />
              <CustomInputCount
                name="email"
                defaultValue={email}
                onBlur={handleInputChange}
                className={classes.input_box}
              />
            </div>
            <div className={classes.textCenter}>
              <CustomButton className={classes.sendBtn} onClick={handleSubmit}>
                {strings.modals.titles.Send}
              </CustomButton>
            </div>
          </div>
        </div>
      </div>
    </CustomModal>
  );
}

export default PingAdminStyles(PingAdminModal);
