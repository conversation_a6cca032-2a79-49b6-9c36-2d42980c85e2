import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { get } from "lodash";

import { color } from "../../utilities/themes";
import { ProfileActions } from "../../redux/actions";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import PingAdminStyles from "./styles/pingAdminStyles";
import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomCheckbox from "../inputs/custom_checkbox";
import CustomTextArea from "../inputs/custom_text_area";
import CustomInputCount from "../inputs/custom_input_count";
import CustomModal from "../inputs/custom_modal";
import Element_Required_Icon from "../../components/data_display/icons/ElementRequiered";

import {
  REGEX_EMAIL
} from "../../validations/validations";

function PingAdminModal(props) {
  const { classes, handlePingAdmin, handleToggleModal, adminEmail } = props;
  const dispatch = useDispatch();  
  const userInfo = useSelector((state) => state.Profile.profileUserData);
  const [values, setValues] = useState({
    message: "",
    email: "",
    name: "",
    emailErr: false,
    nameErr: false,
    adminEmail: adminEmail
  });
  const { message, name, email, emailErr, nameErr } = values;

  useEffect(() => {
    if (userInfo) {
      setValues({
        ...values,
        email: get(userInfo, "email", ""),
        name: get(userInfo, "firstName", "") + " " + get(userInfo, "lastName", "")
      });}
  }, [userInfo]);

const handleInputChange = (e) => {
  const { name, value } = e.target;
  if (name == 'message') {
    values.message = value
    setValues({ ...values })
  }
  if (name == 'name') {
    if (value.trim() == "") {
      values.nameErr = true
      setValues({ ...values })
      return
    } else {
      values.nameErr = false
      values.name = value
      setValues({ ...values })
    }
  }
  if (name == 'email') {
    if (value.trim() == "" || !REGEX_EMAIL.test(value)) {
      values.emailErr = true
      setValues({ ...values })
      return
    } else {
      values.emailErr = false
      values.email = value
      setValues({ ...values })
    }
  }
};

const handleInputKeyUp = (e) => {
  const { name } = e.target;
  if (name == 'name') {
    values.nameErr = false
    setValues({ ...values })
  }
  if (name == 'email') {
    values.emailErr = false
    setValues({ ...values })
  }
};

const handleSubmit = () => {
  if (values.name.trim() == "") {
    values.nameErr = true
    setValues({ ...values })
    return
  } else {
    values.nameErr = false
    setValues({ ...values })
  }
  if (values.email.trim() == "" || !REGEX_EMAIL.test(values.email)) {
    values.emailErr = true
    setValues({ ...values })
    return
  } else {
    values.emailErr = false
    setValues({ ...values })
  }
  dispatch(
    ProfileActions.pingAdminPublic(
      { message: values.message, name: values.name, emai: values.email, adminEmail: values.adminEmail },
      (resp) => {
        if (!get(resp, "error.error", false)) {
          handleToggleModal("showPingAdmin", "showPingAdminSuccess");
        }
      }
    )
  );
};
return (
  <CustomModal open={true}>
    <div className={classes.pingAdmin_width}>
      <div className={classes.btnRight}>
        <CustomButton className={classes.crossBtn}>
          <Close_Icon onClick={handlePingAdmin} />
        </CustomButton>
      </div>
      <div className={classes.textCenter}>
        <img
          src="assets/images/Ping Icon.svg"
          className={classes.envelope_width}
        />
      </div>
      <div className={classes.padding_bottom_35}>
        <Text
          size={30}
          color={color.primary_palette.franklin_purple}
          className={classes.textCenter}
        >
          {strings.modals.titles.ping_Admin}
        </Text>
        <div className={classes.form_width}>
          <div className={classes.textarea_spacing}>
            <Text size={16} family="gillsans_sb" color={color.primary_palette.black}>
              {strings.modals.titles.message}
            </Text>
            <CustomTextArea
              name="message"
              value={message}
              onBlur={handleInputChange}
              className={classes.textArea}
            />
          </div>
          <div className={classes.name_spacing} >
            <Text size={16} family="gillsans_sb" color={color.primary_palette.black}>
              <Element_Required_Icon className={`${classes.checkBox_required} ${nameErr ? classes.errorCheckRequired : ""}`} />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{strings.modals.titles.your_name}
            </Text>
            <CustomInputCount
              name="name"
              defaultValue={name}
              onKeyUp={handleInputKeyUp}
              onBlur={handleInputChange}
              className={`${classes.input_box} ${nameErr ? classes.errorCheck : ""}`}
            />
          </div>
          <div className={classes.name_spacing}>
            <Text size={16} family="gillsans_sb" color={color.primary_palette.black}>
              <Element_Required_Icon className={`${classes.checkBox_required} ${emailErr ? classes.errorCheckRequired : ""}`} />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{strings.modals.titles.your_email}
            </Text>
            <CustomInputCount
              name="email"
              defaultValue={email}
              onKeyUp={handleInputKeyUp}
              onBlur={handleInputChange}
              className={`${classes.input_box} ${emailErr ? classes.errorCheck : ""}`}
            />
          </div>
          <div className={classes.textCenter}>
            <CustomButton className={classes.sendBtn} onClick={handleSubmit}>
              {strings.modals.titles.Send}
            </CustomButton>
          </div>
        </div>
      </div>
    </div>
  </CustomModal>
);
}

export default PingAdminStyles(PingAdminModal);
