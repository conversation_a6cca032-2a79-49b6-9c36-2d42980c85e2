import React from "react";
import { useSelector } from "react-redux";
import { get } from "lodash";

import { color } from "../../utilities/themes";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import NewCompanyModalStyles from "./styles/new_companyModalStyles";
import Text from "../common/ui_kit/text";
// import strings from "../../../utilities/strings";
import CustomModal from "../inputs/custom_modal";
import { Row } from "../common/ui_kit";

function NewCompanyConfirm(props) {
  const { classes, handleToggleModal } = props;
  //   const userInfo = useSelector((state) => state.Profile.companyinfo);

  return (
    <>
      <CustomModal open={true}>
        <div className={classes.newcompanyModal_width}>
          <div className={classes.textCenter}>
            <img src="assets/images/Arrow.svg" className={classes.modal_flag} />
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_sb"
              className={classes.textCenter}
            >
              This New Company will be public immediately.
            </Text>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_sb"
              className={classes.textCenter}
            >
              Also, this name (and logo) will be added to the
            </Text>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_sb"
              className={classes.textCenter}
            >
              Drop Down list of Companies within 24 hours.
            </Text>
            <CustomButton
              className={classes.pingBtn}
              style={{ marginLeft: "0px" }}
              onClick={handleToggleModal}
            >
              GOT IT
            </CustomButton>
          </div>
        </div>
      </CustomModal>
    </>
  );
}

export default NewCompanyModalStyles(NewCompanyConfirm);
