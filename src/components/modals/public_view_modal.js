import React from "react";
import { useSelector } from "react-redux";
import { get } from "lodash";

import { color } from "../../utilities/themes";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import NewCompanyModalStyles from "./styles/new_companyModalStyles";
import Text from "../common/ui_kit/text";
// import strings from "../../../utilities/strings";
import CustomModal from "../inputs/custom_modal";
import { Row } from "../common/ui_kit";

function PublicViewModal(props) {
  const { classes, handlePingAdmin, onCloseClick } = props;
  const userInfo = useSelector((state) => state.Profile.companyinfo);

  return (
    <>
      <CustomModal open={true}>
        <div className={classes.newcompanyModal_width}>
          <div className={classes.textCenter}>
            <img
              src="assets/images/Modal Flag.svg"
              className={classes.modal_flag}
            />
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_sb"
              className={classes.textCenter}
            >
              You have been added as a company member of
            </Text>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_sb"
              className={classes.textCenter}
              style={{ marginBottom: "20px" }}
            >
              {localStorage.getItem("companyName")}
            </Text>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_sb"
              className={classes.textCenter}
            >
              If you have any questions or suggestions for an edit to this
            </Text>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_sb"
              className={classes.textCenter}
            >
              Company Profile, please ping the administrator.
            </Text>
            <Row>
              <CustomButton
                className={classes.pingBtn}
                onClick={handlePingAdmin}
              >
                PING ADMIN
              </CustomButton>
              <CustomButton className={classes.pingBtn} onClick={onCloseClick}>
                CONTINUE
              </CustomButton>
            </Row>
          </div>
        </div>
      </CustomModal>
    </>
  );
}

export default NewCompanyModalStyles(PublicViewModal);
