import React, { useEffect } from "react";

import { color } from "../../utilities/themes";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import PingAdminStyles from "./styles/pingAdminStyles";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CustomModal from "../inputs/custom_modal";

function PingAdminSuccessModal(props) {
  const { classes, handleToggleModal, type } = props;
  useEffect(() => {
    activateTimer();
  }, []);
  const activateTimer = () => {
    setTimeout(() => {
      handleToggleModal("showPingAdminSuccess");
    }, 10000);
  };
  return (
    <CustomModal open={true}>
      <div className={classes.message_width}>
        <div className={`${classes.textCenter} ${classes.padding_Top_26}`}>
            <img
                src="assets/images/Ping Icon.svg"
                className={classes.envelope_width}
            />
        </div>
        <div>
          <Text
            size={25}
            family="gillsans_r"
            color={color.primary_palette.franklin_purple}
            className={`${classes.textCenter} ${classes.text_transform}`}
          >
            {/* {strings.modals.titles.message_sent} */}
            YOUR PING HAS BEEN SENT
          </Text>
        </div>        
        <div style={{paddingTop:"20px",paddingBottom:"15px"}}>
          <div className={classes.checkBox_area}>
            <div className={classes.textCenter}>
              <CustomButton className={classes.sendBtn} onClick={() => handleToggleModal("showPingAdminSuccess")}>
                {strings.modals.titles.Great}
              </CustomButton>
            </div>
          </div>
        </div>
      </div>
    </CustomModal>
  );
}

export default PingAdminStyles(PingAdminSuccessModal);
