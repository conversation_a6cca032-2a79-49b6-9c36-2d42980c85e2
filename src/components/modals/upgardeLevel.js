import React, { useEffect } from "react";

import { color } from "../../utilities/themes";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import upgradeLevelStyles from "./styles/upgradeLevelStyles";
import Text from "../common/ui_kit/text";
import Row from "../common/ui_kit/row";
import strings from "../../utilities/strings";
import CustomModal from "../inputs/custom_modal";
import CustomCheckbox from "../inputs/custom_checkbox";

function upgradeLevel(props) {
  const { classes } = props;
  return (
    <CustomModal open={true}>
    <div className={classes.upagrde_width}>
      <div className={classes.btnRight}>
        <CustomButton className={classes.crossBtn}>
          <Close_Icon />
        </CustomButton>
      </div>
      <div className={classes.padding_space}>
        <div className={classes.txtCenter}>
          <Text
            size={25}
            color={color.primary_palette.franklin_purple}
            family="avenir_black_r"
            className={`${classes.text_captial} ${classes.main_spacing}`}
          >
            {strings.upgarde.titles.level_change}
          </Text>
        </div>
        <div>
          <Text
            size={15}
            color={color.primary_palette.black}
            family="avenir_roman"
          >
            <span className={classes.form_text}>
              {strings.upgarde.titles.form}
            </span>
            userName, userLevel
          </Text>
          <Text
            size={15}
            color={color.primary_palette.black}
            family="avenir_roman"
          >
            {strings.upgarde.titles.please_apply}
          </Text>
        </div>
        <div>
          <Text
            size={15}
            color={color.primary_palette.franklin_purple}
            family="avenir_black_r"
            className={`${classes.text_captial} ${classes.padding_request}`}
          >
            {strings.upgarde.titles.request_change}
          </Text>
          <Row>
            <div className={classes.request_options}>
              <Text
                size={15}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
                className={`${classes.txtCenter} ${classes.border_bottom}`}
              >
                {strings.upgarde.titles.admin}
              </Text>
              <div>
                <Text
                  size={12}
                  color={color.primary_palette.black}
                  family="avenir_light"
                  className={classes.txtCenter}
                >
                  {strings.upgarde.titles.admin_des}
                </Text>
              </div>
            </div>
            <div className={classes.request_options}>
              <Text
                size={15}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
                className={`${classes.txtCenter} ${classes.border_bottom}`}
              >
                {strings.upgarde.titles.hr}
              </Text>
              <Row className={classes.checkBoxs_two}>
                <CustomCheckbox
                  className={classes.checkBox}
                  label={strings.upgarde.titles.hr_des1}
                  labelPlacement="top"
                />
                <CustomCheckbox
                  className={classes.checkBox}
                  label={strings.upgarde.titles.hr_des2}
                  labelPlacement="top"
                />
              </Row>
            </div>
            <div className={classes.request_options}>
              <Text
                size={15}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
                className={`${classes.txtCenter} ${classes.border_bottom}`}
              >
                {strings.upgarde.titles.marketing}
              </Text>
              <div>
                <CustomCheckbox
                  className={classes.checkBox}
                  label={strings.upgarde.titles.marketing_des}
                  labelPlacement="top"
                />
              </div>
            </div>
            <div className={classes.request_options}>
              <Text
                size={15}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
                className={`${classes.txtCenter} ${classes.border_bottom}`}
              >
                {strings.upgarde.titles.accounting}
              </Text>
              <div>
                <CustomCheckbox
                  className={classes.checkBox}
                  label={strings.upgarde.titles.accounting_des}
                  labelPlacement="top"
                />
              </div>
            </div>
            <div className={classes.request_options}>
              <Text
                size={15}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
                className={`${classes.txtCenter} ${classes.border_bottom}`}
              >
                {strings.upgarde.titles.team_mem}
              </Text>
              <div>
                <CustomCheckbox
                  className={classes.checkBox}
                  label={strings.upgarde.titles.team_mem_des}
                  labelPlacement="top"
                />
              </div>
            </div>
          </Row>
        </div>
        <div className={classes.thank_text}>
          <Text
            size={15}
            color={color.primary_palette.black}
            family="avenir_roman"
          >
            {strings.upgarde.titles.thank}
          </Text>
          <Text
            size={15}
            color={color.primary_palette.black}
            family="avenir_roman"
          >
            email
          </Text>
        </div>
        <div className={classes.txtCenter}>
          <CustomButton className={classes.requestBtn}>{strings.upgarde.titles.request}</CustomButton>
        </div>
      </div>
    </div>
    </CustomModal>
  );
}

export default upgradeLevelStyles(upgradeLevel);
