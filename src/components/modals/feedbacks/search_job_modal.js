import React, { Component } from "react";
import _, { get } from "lodash";
import { color } from "../../../utilities/themes";
import RequestStyles from "./feedback_style";
import Text from "../../common/ui_kit/text";
import Row from "../../common/ui_kit/row";
import CustomButton from "../../navigations/custom_buttons";
import Close_Icon from "../../data_display/icons/Close";
import CustomTextField from "../../inputs/custom_textfields";
import { APP_URLS_JPACK, BASEURL, TOKEN_KEY } from "../../../constants";
import { TextareaAutosize } from "@material-ui/core";

class SearchJobModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      fname: "",
      lname: "",
      email: "",
      message: "",
    };
  }

  onTextChange = (e) => {
    const { name, value } = e.target;
    let { message } = this.state;
    this.setState({ message, [name]: value });
  };

  sendRequest = () => {
    const { fname } = this.state;
    const token = localStorage.getItem(TOKEN_KEY);
    let host = get(window, "location.host", "");
    let route = "";
    if (host) {
      if (host.includes("localhost")) {
        route = `${APP_URLS_JPACK.LOCAL}`;
      } else {
        if (host.includes("-dev-")) {
          route = `${APP_URLS_JPACK.DEV}`;
        }
        if (host.includes("-qa-")) {
          route = `${APP_URLS_JPACK.QA}`;
        }
        if (host.includes("-stage-")) {
          route = `${APP_URLS_JPACK.STAGE}`;
        } else {
          route = `${BASEURL.URL}jpack/`;
          // route = "http://twwstage.franklinreport.com/jpack/";
        }
      }
    }
    window.location.href = `${route}auth/${token}/${
      fname || null
    }/${null}/false`;
  };

  handleKeypress = (event) => {
    if (event.key === "Enter") {
      this.sendRequest();
    }
  };

  render() {
    const { classes, onClose, onRequestSent, user } = this.props;
    const { fname, lname, email, message } = this.state;
    return (
      <>
        <div className={classes.searchModal_width}>
          <div className={classes.btnRight}>
            <CustomButton className={classes.crossBtn}>
              <Close_Icon onClick={onClose} />
            </CustomButton>
          </div>
          <div className={classes.textCenter}>
            <img
              src="assets/images/get_found.png"
              style={{ height: "100px" }}
            />
          </div>
          <div className={classes.spacing_from}>
            <Text
              size={25}
              color={color.primary_palette.franklin_purple}
              family="gillsans_sb"
              className={classes.textCenter}
            >
              GET FOUND
            </Text>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="gillsans_sb"
              className={classes.textCenter}
            >
              Search Job Title, Company or Keywords
            </Text>
            {/* <Row className={classes.field_spacing}>
              <Text
                size={16}
                color={color.primary_palette.franklin_purple}
                family="gillsans_sb"
              >
                SEND REQUEST TO:
              </Text>
            </Row> */}
            <Row className={classes.field_spacing_top}>
              <div className={classes.input_field_Name}>
                <CustomTextField
                  name="fname"
                  value={fname}
                  onChange={this.onTextChange}
                  className={classes.search_input_field}
                  onKeyPress={this.handleKeypress}
                  // placeholder="SEARCH JOBS: Job Title, Company or Keywords"
                />
              </div>
            </Row>
            <div className={classes.textCenter}>
              <CustomButton
                className={classes.reqBtn}
                onClick={this.sendRequest}
                style={{ width: "100px" }}
              >
                GO
              </CustomButton>
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default RequestStyles(SearchJobModal);
