import React, { Component } from "react";
import _, { get } from "lodash";
import { color } from "../../../utilities/themes";
import Feedbackstyles from "./feedback_style";
import Text from "../../common/ui_kit/text";
import Row from "../../common/ui_kit/row";
import CustomButton from "../../navigations/custom_buttons";
import Close_Icon from "../../data_display/icons/Close";
import CustomRadioButton from "../.././inputs/custom_radio_button";
import CustomScrollbars from "../../data_display/custom_scroll";
import { BASEURL, TDW_URL, TOKEN_KEY } from "../../../constants";

class SelectCompanyModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      fname: "",
      lname: "",
      email: "",
      message: "",
      selectedResume: "",
    };
  }

  sendRequest = () => {
    const { selectedResume } = this.state;
    const { onClose } = this.props;
    localStorage.setItem("newCompanyId", selectedResume);
    this.redirectToIndividualPack();
    onClose();
  };

  redirectToIndividualPack = () => {
    const data = localStorage.getItem("selectedMenuItem");
    if (data === "redirectWpack") {
      this.redirectToWpack();
    }
    if (data === "redirectCats") {
      this.redirectToCats();
    }
  };

  redirectToWpack = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    localStorage.removeItem("isFromCats");
    localStorage.setItem("isPostNewJob", true);
    let companyId = localStorage.getItem("newCompanyId");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/wpack";
      // window.location.href = `http://${host}/${token}/${companyId}`;
      if (host.includes("localhost")) {
        host = "localhost:3003";
        window.location.href = `http://${host}/${token}/${companyId}`;
      } else {
        if (host.includes("-dev-")) {
          host = TDW_URL.WPACK.DEV;
        }
        if (host.includes("-qa-")) {
          host = TDW_URL.WPACK.QA;
        }
        if (host.includes("-stage-")) {
          host = TDW_URL.WPACK.STAGE;
        }
        if (host.includes("-twwstage-")) {
          host = `${BASEURL.URL}wpack`;
          // host = "twwstage.franklinreport.com/wpack";
          // window.location.href = `http://${host}/${token}/${companyId}`;
        } else {
          host = `${BASEURL.URL}wpack`;
          // host = "http://twwstage.franklinreport.com/wpack";
        }
        window.location.href = `${host}/${token}/${companyId}`;
      }
    }
  };

  redirectToCats = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("newCompanyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}cats`;
      // host = "twwstage.franklinreport.com/cats";
      window.location.href = `${host}/${token}/${companyId}`;
    }
  };

  onSelectResume = (id) => () => {
    this.setState({
      selectedResume: id,
    });
  };

  render() {
    const { classes, onClose, onRequestSent, user, CompanyListByUser } =
      this.props;
    const { selectedResume } = this.state;
    return (
      <>
        <div className={classes.companyListModal_width}>
          <div className={classes.btnRight}>
            <CustomButton className={classes.crossBtn}>
              <Close_Icon onClick={onClose} />
            </CustomButton>
          </div>
          <div className={classes.spacing_from}>
            <Text
              size={25}
              color={color.primary_palette.franklin_purple}
              family="gillsans_sb"
              className={classes.textCenter}
              style={{ marginBottom: "15px" }}
            >
              SELECT COMPANY
            </Text>
            <div style={{ paddingLeft: "40px" }}>
              <CustomScrollbars style={{ minHeight: "120px" }}>
                {CompanyListByUser.map((eachCompany, index) => {
                  return (
                    <Row>
                      <CustomRadioButton
                        checked={selectedResume === eachCompany._id}
                        onChange={this.onSelectResume(eachCompany._id)}
                      />
                      <Text
                        size={14}
                        color={color.primary_palette.black}
                        family="gillsans_sb"
                        style={{
                          marginTop: "10px",
                          textTransform: "capitalize",
                        }}
                        //   className={classes.textCenter}
                      >
                        {eachCompany.name}
                      </Text>
                    </Row>
                  );
                })}
              </CustomScrollbars>
            </div>
            <div className={classes.textCenter}>
              <CustomButton
                className={classes.reqBtn}
                onClick={this.sendRequest}
                style={{ width: "100px" }}
              >
                GO
              </CustomButton>
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default Feedbackstyles(SelectCompanyModal);
