import React, { Component } from "react";
import _, { get } from "lodash";
import { color } from "../../../utilities/themes";
import Feedbackstyles from "./feedback_style";
import Text from "../../common/ui_kit/text";
import Row from "../../common/ui_kit/row";
import CustomButton from "../../navigations/custom_buttons";
import Close_Icon from "../../data_display/icons/Close";
import CustomRadioButton from "../.././inputs/custom_radio_button";
import CustomScrollbars from "../../data_display/custom_scroll";
import { BASEURL, TDW_URL, TOKEN_KEY } from "../../../constants";

class RedirectProfileModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      fname: "",
      lname: "",
      email: "",
      message: "",
      selectedResume: "",
    };
  }

  sendRequest = () => {
    const { onClose } = this.props;
    let host = get(window, "location.host", "");
    localStorage.removeItem("companyId");
    localStorage.removeItem("newCompanyId");
    localStorage.removeItem("companyName");
    let token = localStorage.getItem("tradeworks_user_token");
    if (host.includes("localhost")) {
      host = "localhost:3001";
      window.location.href = `http://${host}/lpack/wizard/${token}`;
    } else {
      // host = "twwstage.franklinreport.com";
      host = `${BASEURL.URL}`;
      window.location.href = `${host}lpack/lpack/wizard/${token}`;
    }
    onClose();
  };

  redirectToIndividualPack = () => {
    const data = localStorage.getItem("selectedMenuItem");
    if (data === "redirectWpack") {
      this.redirectToWpack();
    }
    if (data === "redirectCats") {
      this.redirectToCats();
    }
  };

  redirectToWpack = () => {
    let host = get(window, "location.host", "");
    localStorage.setItem("isPostNewJob", true);
    let token = localStorage.getItem(TOKEN_KEY);
    localStorage.removeItem("isFromCats");
    let companyId = localStorage.getItem("newCompanyId");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/wpack";
      // window.location.href = `http://${host}/${token}/${companyId}`;
      if (host.includes("localhost")) {
        host = "localhost:3003";
        window.location.href = `http://${host}/${token}/${companyId}`;
      } else {
        if (host.includes("-dev-")) {
          host = TDW_URL.WPACK.DEV;
        }
        if (host.includes("-qa-")) {
          host = TDW_URL.WPACK.QA;
        }
        if (host.includes("-stage-")) {
          host = TDW_URL.WPACK.STAGE;
        }
        if (host.includes("-twwstage-")) {
          // host = "twwstage.franklinreport.com/wpack";
          host = `${BASEURL.URL}wpack`;
          // window.location.href = `http://${host}/${token}/${companyId}`;
        } else {
          host = `${BASEURL.URL}wpack`;
          // host = "http://twwstage.franklinreport.com/wpack";
        }
        window.location.href = `${host}/${token}/${companyId}`;
      }
    }
  };

  redirectToCats = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("newCompanyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}cats`;
      // host = "twwstage.franklinreport.com/cats";
      window.location.href = `${host}/${token}/${companyId}`;
    }
  };

  onSelectResume = (id) => () => {
    this.setState({
      selectedResume: id,
    });
  };

  render() {
    const { classes, onClose, onRequestSent, user, CompanyListByUser } =
      this.props;
    const { selectedResume } = this.state;
    return (
      <>
        <div className={classes.companyListModal_width}>
          <div className={classes.btnRight}>
            <CustomButton className={classes.crossBtn}>
              <Close_Icon onClick={onClose} />
            </CustomButton>
          </div>
          <div className={classes.textCenter}>
            <img
              src="assets/images/TW.svg"
              style={{ height: "75px", marginBottom: "20px" }}
            />
          </div>
          <div className={classes.spacing_from}>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="gillsans_sb"
              className={classes.textCenter}
            >
              Join the Trade Community.
            </Text>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="gillsans_sb"
              className={classes.textCenter}
            >
              Create Your Professional Presence.
            </Text>
            {/* <div style={{ paddingLeft: "40px" }}>
              <CustomScrollbars style={{ minHeight: "120px" }}>
                {CompanyListByUser.map((eachCompany, index) => {
                  return (
                    <Row>
                      <CustomRadioButton
                        checked={selectedResume === eachCompany._id}
                        onChange={this.onSelectResume(eachCompany._id)}
                      />
                      <Text
                        size={14}
                        color={color.primary_palette.black}
                        family="gillsans_sb"
                        style={{
                          marginTop: "10px",
                          textTransform: "capitalize",
                        }}
                        //   className={classes.textCenter}
                      >
                        {eachCompany.name}
                      </Text>
                    </Row>
                  );
                })}
              </CustomScrollbars>
            </div> */}
            <div className={classes.textCenter}>
              <CustomButton
                className={classes.reqBtn}
                onClick={onClose}
                style={{ width: "134px", marginRight: "106px" }}
              >
                MAYBE LATER
              </CustomButton>
              <CustomButton
                className={classes.reqBtn}
                onClick={this.sendRequest}
                style={{ width: "100px" }}
              >
                LETS GO
              </CustomButton>
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default Feedbackstyles(RedirectProfileModal);
