import React, { useState, useEffect } from "react";
import { map, find, get } from "lodash";
import { useSelector } from "react-redux";
import ReactPlayer from "react-player";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import StyleSheet from "./styles/profile_container_styles";
import OurOffices from "./our_offices";
import CustomModal from "../../components/inputs/custom_modal";

import { color, pxToRem } from "../../utilities/themes";
import Lil_Plus_filled from "../data_display/icons/Lil_Plus_filled";
import { useHistory } from "react-router-dom";
import SvgPencil from "../data_display/icons/Pencil";

function AboutSection(props) {
  const { classes, accolades, companyPitch, autoScroll, userRole } = props;
  const [values, setValues] = useState({
    tabs: ["About", "People", "Offices", "Culture", "Connect"],
    waterCoolers: [],
    openMediaPlayer: false,
  });
  const history = useHistory();
  const accoladesMaster = useSelector((state) => state.Profile.accolades);
  const waterCooler = useSelector((state) => state.Profile.watercooler);
  const isPublicProfile = localStorage.getItem("publicPage") === "true";

  useEffect(() => {
    if (waterCooler && waterCooler.length) {
      setValues({ ...values, waterCoolers: waterCooler.slice(0, 3) });
    } else {
      if (!isPublicProfile) {
        const waterCooler = [
          {
            headShotLink: "assets/images/Shadow Colleague 1.jpg",
            position: 1,
            teamMemberName: "Colleague 1",
            title: "",
            uploadType: "image",
            default: true,
          },
          {
            headShotLink: "assets/images/Shadow Colleague 2.jpg",
            position: 2,
            teamMemberName: "Colleague 2",
            title: "",
            default: true,
            uploadType: "video",
          },
          {
            headShotLink: "assets/images/Shadow Colleague 3.jpg",
            position: 3,
            teamMemberName: "Colleague 3",
            title: "",
            uploadType: "image",
            default: true,
          },
        ];
        setValues({ ...values, waterCoolers: waterCooler });
      } else {
        const waterCooler = [];
        setValues({ ...values, waterCoolers: waterCooler });
      }
    }
  }, [waterCooler]);

  const handleShowMore = () => {
    const { waterCoolers } = values;
    const length = waterCoolers.length;
    const updatedData =
      length === 3 ? waterCooler.slice(0, 6) : waterCooler.slice(0, 9);
    setValues({ ...values, waterCoolers: [...updatedData] });
  };

  const handleShowLess = () => {
    const updatedData = waterCooler.slice(0, 3);
    setValues({ ...values, waterCoolers: [...updatedData] });
  };
  const returnClientele = (companyPitchData) => {
    let str = "";
    map(get(companyPitchData, "clientele", []), (each, index) => {
      str += each.name;
      if (index !== companyPitchData.clientele.length - 1) {
        str += ", ";
      }
      return;
    });
    return str;
  };
  const returnProclivities = (companyPitchData) => {
    let str = "";
    map(get(companyPitchData, "designProclivities", []), (each, index) => {
      str += each.name;
      if (index !== companyPitchData.designProclivities.length - 1) {
        str += ", ";
      }
      return;
    });
    return str;
  };
  const handleOpenMediaPlayer = (link) => () => {
    if (!link) {
      setValues({
        ...values,
        openMediaPlayer: false,
        videoUrl: "",
      });
      return;
    }
    setValues({
      ...values,
      openMediaPlayer: !values.openMediaPlayer,
      videoUrl: link,
    });
  };
  const handleButtonClick = (step) => (e) => {
    history.push({ pathname: "/wizard", state: { step, edit: true } });
  };
  const { tabs, waterCoolers } = values;
  return (
    <div className={classes.left_section}>
      <>
        {props.displayContent != 0 &&
          !isPublicProfile &&
          userRole !== "staff" && (
            <Row>
              {map(tabs, (tab, index) => {
                return (
                  <Text
                    size={15}
                    fontWeight={600}
                    color={color.wizard_box_colors.shadow_gray}
                    family="gillsans_sb"
                    className={classes.tab_name}
                  >
                    <span
                      className={`${classes.cur_point} ${
                        index == 0 && classes.tab_highlight
                      }`}
                      onClick={autoScroll(tab.toLowerCase())}
                    >
                      {tab}
                    </span>
                  </Text>
                );
              })}
            </Row>
          )}
        {props.displayContent ? (
          <div>
            <Row className={classes.about_section_row}>
              <div className={classes.about_img_sec}>
                <img
                  className={classes.about_img}
                  src={
                    isPublicProfile
                      ? get(
                          companyPitch,
                          "topFeatureImage1",
                          "assets/images/office_default.png"
                        )
                      : get(
                          companyPitch,
                          "topFeatureImage1",
                          "assets/images/Shadow_Asset_Default.png"
                        )
                  }
                />
                {!isPublicProfile && userRole !== "staff" && (
                  // <img
                  //   src="assets/images/pencil_icon.png"
                  //   className={classes.edit_company_pencil_round}
                  //   onClick={handleButtonClick(2)}
                  //   style={{ position: "absolute", right: "8px", top: "8px" }}
                  // />
                  <SvgPencil
                    className={classes.edit_company_pencil_round}
                    style={{ position: "absolute", right: "8px", top: "8px" }}
                    onClick={handleButtonClick(2)}
                  />
                )}
                {get(companyPitch, "topFeatureImage2") && (
                  <img
                    className={`${classes.about_img} ${classes.p_t_5}`}
                    src={get(companyPitch, "topFeatureImage2")}
                  />
                )}
              </div>
              <div className={classes.about_desc_sec}>
                <Text
                  size={15}
                  fontWeight={300}
                  color={color.primary_palette.black}
                  family="gillsans_light"
                  className={classes.about_desc_content}
                >
                  {get(companyPitch, "companyPitch", "")}
                </Text>
                <div className={classes.company_keywords}>
                  {get(companyPitch, "companyVibe.length") !== 0 && (
                    <Text
                      size={12}
                      fontWeight={900}
                      color={color.form_colors.royal_purple_1}
                      family="gillsans_r"
                    >
                      Company Vibe
                    </Text>
                  )}
                  <Row className={classes.company_keywords_row}>
                    {companyPitch && companyPitch.companyVibe ? (
                      map(companyPitch.companyVibe, (companyVibe) => {
                        return (
                          <Row>
                            <Text
                              size={12}
                              fontWeight={300}
                              color={color.greyish_brown}
                              family="gillsans_light"
                              className={classes.keyword_name}
                            >
                              {companyVibe.name}
                            </Text>
                          </Row>
                        );
                      })
                    ) : (
                      <Text
                        size={12}
                        fontWeight={300}
                        color={color.greyish_brown}
                        family="gillsans_light"
                        className={classes.keyword_name}
                      >
                        {!isPublicProfile && "Interior Design"}
                      </Text>
                    )}
                  </Row>
                  <Row className={classes.accolade_row}>
                    <div className={classes.about_accolade_container}>
                      {map(accolades, (accolade) => {
                        return (
                          <div>
                            <img
                              src={get(accolade, "logo", "")}
                              className={classes.about_accolade_img}
                              title={get(accolade, "name", "")}
                            />
                            <Text
                              size={10}
                              family="gillsans_r"
                              color={color.primary_palette.black}
                              style={{ width: "80px", textAlign: "center" }}
                            >
                              {get(accolade, "name", "")}
                            </Text>
                          </div>
                        );
                      })}
                    </div>
                  </Row>
                  {get(companyPitch, "employees") && (
                    <Row className={classes.keyval_row}>
                      <div className={classes.about_keyvalue_container}>
                        <Text
                          size={13}
                          fontWeight={600}
                          color={color.form_colors.textfield_color}
                          family="gillsans_sb"
                          className={classes.about_keyvalue}
                        >
                          Size
                        </Text>
                        <Text
                          size={13}
                          fontWeight={300}
                          color={color.form_colors.textfield_color}
                          family="gillsans_light"
                          className={`${classes.about_keyvalue} ${classes.p_l_5}`}
                        >
                          {get(companyPitch, "employees", 0)}
                        </Text>
                      </div>
                      {get(companyPitch, "yearEstablished", "") && (
                        <div className={classes.about_keyvalue_container}>
                          <Text
                            size={13}
                            fontWeight={600}
                            color={color.form_colors.textfield_color}
                            family="gillsans_sb"
                            className={classes.about_keyvalue}
                          >
                            Founded
                          </Text>
                          <Text
                            size={13}
                            fontWeight={300}
                            color={color.form_colors.textfield_color}
                            family="gillsans_light"
                            className={`${classes.about_keyvalue} ${classes.p_l_5}`}
                          >
                            {get(companyPitch, "yearEstablished", "")}
                          </Text>
                        </div>
                      )}
                    </Row>
                  )}
                  <Row>
                    <div
                      className={classes.about_keyvalue_container}
                      style={{ display: "flex" }}
                    >
                      {get(companyPitch, "venueInfo[0].name", "") && (
                        <Text
                          size={13}
                          fontWeight={600}
                          color={color.form_colors.textfield_color}
                          family="gillsans_sb"
                          className={classes.about_keyvalue}
                        >
                          Venue
                        </Text>
                      )}
                      <Text
                        size={13}
                        fontWeight={300}
                        color={color.form_colors.textfield_color}
                        family="gillsans_light"
                        className={`${classes.about_keyvalue} ${classes.p_l_5}`}
                      >
                        {get(companyPitch, "venueInfo[0].name", "")}
                      </Text>
                    </div>
                  </Row>
                  {get(companyPitch, "clientele.length") !== 0 && (
                    <Row>
                      <div style={{ display: "flex" }}>
                        <Text
                          size={13}
                          fontWeight={600}
                          color={color.form_colors.textfield_color}
                          family="gillsans_sb"
                          className={classes.about_keyvalue}
                        >
                          Clientele
                        </Text>
                        <Text
                          size={13}
                          fontWeight={300}
                          color={color.form_colors.textfield_color}
                          family="gillsans_light"
                          className={`${classes.clientele_inp}`}
                        >
                          <span title={returnClientele(companyPitch)}>
                            {companyPitch && returnClientele(companyPitch)}
                          </span>
                        </Text>
                      </div>
                    </Row>
                  )}
                  {get(companyPitch, "designProclivities.length") !== 0 && (
                    <Row>
                      <div style={{ display: "flex" }}>
                        <Text
                          size={13}
                          fontWeight={600}
                          color={color.form_colors.textfield_color}
                          family="gillsans_sb"
                          className={classes.about_keyvalue}
                          style={{ width: pxToRem(110) }}
                        >
                          Design Proclivities
                        </Text>
                        <Text
                          size={13}
                          fontWeight={300}
                          color={color.form_colors.textfield_color}
                          family="gillsans_light"
                          className={`${classes.proc_inp}`}
                        >
                          <span title={returnProclivities(companyPitch)}>
                            {companyPitch && returnProclivities(companyPitch)}
                          </span>
                        </Text>
                      </div>
                    </Row>
                  )}
                  {get(companyPitch, "companyWebsiteUrl") &&
                    get(companyPitch, "companyWebsiteUrl") !== "" &&
                    get(companyPitch, "companyWebsiteUrl") !== null && (
                      <Row>
                        <div style={{ display: "flex" }}>
                          <Text
                            size={13}
                            fontWeight={600}
                            color={color.form_colors.textfield_color}
                            family="gillsans_sb"
                            className={classes.about_keyvalue}
                            style={{ width: pxToRem(110) }}
                          >
                            Company Website
                          </Text>
                          <Text
                            size={13}
                            fontWeight={300}
                            color={color.form_colors.textfield_color}
                            family="gillsans_light"
                            className={`${classes.proc_inp} ${classes.cursor_pointer}`}
                            onClick={() => {
                              const url = get(
                                companyPitch,
                                "companyWebsiteUrl",
                                ""
                              );
                              if (!url) return;

                              let validUrl = url;
                              if (!url.match(/^https?:\/\//i)) {
                                validUrl = `https://${url}`;
                              }

                              try {
                                new URL(validUrl);
                                window.open(
                                  validUrl,
                                  "_blank",
                                  "noopener,noreferrer"
                                );
                              } catch (e) {
                                console.error("Invalid URL:", url);
                              }
                            }}
                          >
                            {get(companyPitch, "companyWebsiteUrl", "")}
                          </Text>
                        </div>
                      </Row>
                    )}
                </div>
              </div>
            </Row>
          </div>
        ) : (
          <div>
            <Row className={classes.about_section_row}>
              <div className={classes.about_img_sec}>
                <img
                  className={classes.about_img}
                  src={
                    isPublicProfile
                      ? get(
                          companyPitch,
                          "topFeatureImage1",
                          "assets/images/office_default.png"
                        )
                      : get(
                          companyPitch,
                          "topFeatureImage1",
                          "assets/images/Shadow_Asset_Default.png"
                        )
                  }
                />
                {!isPublicProfile && userRole !== "staff" && (
                  <img
                    src="assets/images/pencil_icon.png"
                    className={classes.edit_company_pencil_round}
                    onClick={handleButtonClick(2)}
                    style={{ position: "absolute", right: "8px", top: "8px" }}
                  />
                )}
                {get(companyPitch, "topFeatureImage2") && (
                  <img
                    className={`${classes.about_img} ${classes.p_t_5}`}
                    src={get(companyPitch, "topFeatureImage2")}
                  />
                )}
              </div>
              <div className={classes.about_desc_sec}>
                <Text
                  size={20}
                  color={color.form_colors.royal_purple_1}
                  family="gillsans_sb"
                  transform="uppercase"
                >
                  Company Blurb
                  {!isPublicProfile && userRole !== "staff" && (
                    // <img
                    //   src="assets/icons/pencil.svg"
                    //   className={classes.edit_company_pencil}
                    //   onClick={handleButtonClick(2)}
                    // />
                    <SvgPencil
                      className={classes.edit_company_pencil}
                      onClick={handleButtonClick(2)}
                    />
                  )}
                </Text>
                <Text
                  size={16}
                  // fontWeight={300}
                  color={color.secondary_palette.grays.shadow_gray}
                  family="NeutraText"
                  className={classes.about_desc_content}
                >
                  Our company provides fast and reliable services and products
                  aimed to facilitate the processes of manufacturing and
                  building system designed to solve the many problems that a
                  modern-day communal infrastructure industry presents. We can
                  deliver an array of solution set to eliminate the clutter and
                  noise of multiple ideas.
                </Text>
                <div className={classes.company_keywords}>
                  {isPublicProfile ? (
                    (get(companyPitch, "companyVibe.length") !== 0 ||
                      get(accolades, "length") !== 0) && (
                      <Text
                        size={14}
                        color={color.form_colors.royal_purple_1}
                        family="gillsans_sb"
                        transform="uppercase"
                      >
                        Accolades and company vibe
                      </Text>
                    )
                  ) : (
                    <Text
                      size={14}
                      color={color.form_colors.royal_purple_1}
                      family="gillsans_sb"
                      transform="uppercase"
                    >
                      Accolades and company vibe
                    </Text>
                  )}

                  {/* <Row className={classes.company_keywords_row}>
                    <Text
                      size={12}
                      fontWeight={300}
                      color={color.greyish_brown}
                      family="gillsans_light"
                      className={classes.keyword_name}
                    >
                      Interior Design
                    </Text>
                  </Row> */}
                  {/* <Row className={classes.accolade_row}>
                    <div className={classes.about_accolade_container}>
                      {map(accolades, (accolade) => {
                        return (
                          <img
                            src={get(accolade, "logo", "")}
                            className={classes.about_accolade_img}
                          />
                        );
                      })}
                    </div>
                  </Row>
                  <Row className={classes.keyval_row}>
                    <div className={classes.about_keyvalue_container}>
                      <Text
                        size={13}
                        fontWeight={600}
                        color={color.form_colors.textfield_color}
                        family="gillsans_sb"
                        className={classes.about_keyvalue}
                      >
                        Size
                      </Text>
                      <Text
                        size={13}
                        fontWeight={300}
                        color={color.form_colors.textfield_color}
                        family="gillsans_light"
                        className={`${classes.about_keyvalue} ${classes.p_l_5}`}
                      >
                        {get(companyPitch, "employees", 0)}
                      </Text>
                    </div>
                    <div className={classes.about_keyvalue_container}>
                      <Text
                        size={13}
                        fontWeight={600}
                        color={color.form_colors.textfield_color}
                        family="gillsans_sb"
                        className={classes.about_keyvalue}
                      >
                        Founded
                      </Text>
                      <Text
                        size={13}
                        fontWeight={300}
                        color={color.form_colors.textfield_color}
                        family="gillsans_light"
                        className={`${classes.about_keyvalue} ${classes.p_l_5}`}
                      >
                        {get(companyPitch, "yearEstablished", "")}
                      </Text>
                    </div>
                  </Row>
                  <Row>
                    <div
                      className={classes.about_keyvalue_container}
                      style={{ display: "flex" }}
                    >
                      <Text
                        size={13}
                        fontWeight={600}
                        color={color.form_colors.textfield_color}
                        family="gillsans_sb"
                        className={classes.about_keyvalue}
                      >
                        Venue
                      </Text>
                      <Text
                        size={13}
                        fontWeight={300}
                        color={color.form_colors.textfield_color}
                        family="gillsans_light"
                        className={`${classes.about_keyvalue} ${classes.p_l_5}`}
                      >
                        {get(companyPitch, "venueInfo[0].name", "")}
                      </Text>
                    </div>
                  </Row>
                  <Row>
                    <div style={{ display: "flex" }}>
                      <Text
                        size={13}
                        fontWeight={600}
                        color={color.form_colors.textfield_color}
                        family="gillsans_sb"
                        className={classes.about_keyvalue}
                      >
                        Clientele
                      </Text>
                      <Text
                        size={13}
                        fontWeight={300}
                        color={color.form_colors.textfield_color}
                        family="gillsans_light"
                        className={`${classes.clientele_inp}`}
                      >
                        <span title={returnClientele(companyPitch)}>
                          {companyPitch && returnClientele(companyPitch)}
                        </span>
                      </Text>
                    </div>
                  </Row> */}
                </div>
              </div>
            </Row>
          </div>
        )}
        {/* {get(waterCoolers, "length", 0) > 0 && ( */}
        <div className={classes.water_cooler_wrapper}>
          {/* {!isPublicProfile && get(waterCoolers, "[0].default") !== true && ( */}
          {get(waterCoolers, "length") === 0 && isPublicProfile ? (
            <></>
          ) : (
            <Text
              size={20}
              color={color.form_colors.royal_purple_1}
              family="gillsans_r"
              fontWeight={600}
              className={classes.title_underlined}
            >
              OUR PEOPLE
              {!isPublicProfile && userRole !== "staff" && (
                // <img
                //   src="assets/icons/pencil.svg"
                //   className={classes.edit_company_pencil}
                //   onClick={handleButtonClick(3)}
                // />
                <SvgPencil
                  className={classes.edit_company_pencil}
                  onClick={handleButtonClick(3)}
                />
              )}
            </Text>
          )}
          {/* )} */}
        </div>
        {/* )} */}
        <Row style={{ flexWrap: "wrap", justifyContent: "center" }}>
          {waterCoolers &&
            map(waterCoolers, (block) => {
              return (
                <>
                  {!isPublicProfile && userRole !== "staff" ? (
                    <Row className={classes.water_cooler_block}>
                      <Row className={classes.water_cooler_content}>
                        <div className={classes.pos_rel}>
                          <img
                            src={
                              get(block, "headShotLink", "") ||
                              "assets/images/water_cooler.png"
                            }
                            className={
                              !get(block, "default", false)
                                ? classes.circle_img_border
                                : classes.circle_img
                            }
                          />
                          {get(block, "uploadType", "") === "video" && (
                            <img
                              src="assets/images/playerIcon.png"
                              className={classes.player_icon}
                              value={get(block, "videoLink", "")}
                              onClick={handleOpenMediaPlayer(block.videoLink)}
                            />
                          )}
                        </div>
                        <div className={classes.water_cooler_p2}>
                          <Text
                            size={14}
                            color={color.form_colors.black}
                            family="gillsans_light"
                          >
                            {get(block, "teamMemberName", "")}
                          </Text>
                          <Text
                            size={13}
                            color={color.form_colors.textfield_color}
                            family="gillsans_r"
                          >
                            {get(block, "title", "")}
                          </Text>
                          <Text
                            size={12}
                            color={color.form_colors.textfield_color}
                            family="gillsans_light"
                            className={classes.p_t_5}
                          >
                            {get(block, "videoSummery", "")}
                          </Text>
                        </div>
                      </Row>
                    </Row>
                  ) : (
                    <>
                      {get(block, "headShotLink", "") && (
                        <Row className={classes.water_cooler_block}>
                          <Row className={classes.water_cooler_content}>
                            <div className={classes.pos_rel}>
                              <img
                                src={
                                  get(block, "headShotLink", "") ||
                                  "assets/images/water_cooler.png"
                                }
                                className={
                                  !get(block, "default", false)
                                    ? classes.circle_img_border
                                    : classes.circle_img
                                }
                              />
                              {get(block, "uploadType", "") === "video" && (
                                <img
                                  src="assets/images/playerIcon.png"
                                  className={classes.player_icon}
                                  value={get(block, "videoLink", "")}
                                  onClick={handleOpenMediaPlayer(
                                    block.videoLink
                                  )}
                                />
                              )}
                            </div>
                            <div className={classes.water_cooler_p2}>
                              <Text
                                size={14}
                                color={color.form_colors.black}
                                family="gillsans_light"
                              >
                                {get(block, "teamMemberName", "")}
                              </Text>
                              <Text
                                size={13}
                                color={color.form_colors.textfield_color}
                                family="gillsans_r"
                              >
                                {get(block, "title", "")}
                              </Text>
                              <Text
                                size={12}
                                color={color.form_colors.textfield_color}
                                family="gillsans_light"
                                className={classes.p_t_5}
                              >
                                {get(block, "videoSummery", "")}
                              </Text>
                            </div>
                          </Row>
                        </Row>
                      )}
                    </>
                  )}
                </>
              );
            })}
        </Row>
        {get(waterCooler, "length", 0) > 3 && (
          <>
            <div className={classes.show_more_wrap}>
              {get(waterCoolers, "length", 0) <
              get(waterCooler, "length", 0) ? (
                <Lil_Plus_filled
                  className={classes.showmore_add_icon}
                  onClick={handleShowMore}
                />
              ) : (
                <img
                  src="assets/images/minus.png"
                  className={classes.minus_showless}
                />
              )}
              <Text
                size={14}
                color={color.kettleman}
                family="gillsans_r"
                className={classes.water_cooler_more}
                onClick={
                  get(waterCoolers, "length", 0) < get(waterCooler, "length", 0)
                    ? handleShowMore
                    : handleShowLess
                }
              >
                {/* WATER COOLERS === SPLICED DATA; WATER COOLER = API DATA */}
                {get(waterCoolers, "length", 0) < get(waterCooler, "length", 0)
                  ? "SHOW MORE"
                  : "SHOW LESS"}
              </Text>
            </div>
          </>
        )}

        {/* OUR OFFICES CAROUSEL */}
        <OurOffices userRole={userRole} />
        <CustomModal
          open={get(values, "openMediaPlayer", false)}
          aria-labelledby="modal-title"
          aria-describedby="modal-description"
          onClose={handleOpenMediaPlayer("")}
        >
          <ReactPlayer
            url={get(values, "videoUrl", "")}
            controls
            height={"262px"}
            width={"429px"}
            playing
          />
        </CustomModal>
      </>
    </div>
  );
}
export default StyleSheet(AboutSection);
