import React, { useEffect, useState } from "react";
// useDispatch
import { get, map, filter } from "lodash";
import { Grid } from "@material-ui/core";

import PreviewPostStyles from "./styles/job_preview_styles";
import Text from "../common/ui_kit/text";
import Row from "../common/ui_kit/row";
import strings from "../../utilities/strings";
import ElevatorPitchIcon from "../data_display/icons/ElevatorPitch";
import CustomModal from "../inputs/custom_modal";
import { color, pxToRem } from "../../utilities/themes";
import { formatPhoneNumber } from "../../utilities/utils";
// import CloseIcon from "../data_display/icons/Close";
import CustomScrollbars from "../data_display/custom_scroll";
// import Square from "../../assets/icons/square.png";
import { useSelector } from "react-redux";
import { BASEURL } from "../../constants";

function JobPreview(props) {
  const { classes, open, onClose } = props;

  const [values, setValues] = useState({
    edit: false,
    displayApplicantData: {},
    previewData: {},
  });

  const { edit, displayApplicantData, previewData } = values;
  const jobData = useSelector((state) => state.Profile.jobInfoData);

  useEffect(() => {
    if (jobData && jobData.job) {
      const appData = {
        required: [],
        preferred: [],
      };

      //filtering required and perferred data
      const skillsData = jobData.job.applicantQualifications.skills.map(
        (each) => {
          return each.level.map((skill) => {
            return { ...skill, skillName: each.skillsName };
          });
        }
      );

      appData.required = filter(skillsData.flat(), (each) => {
        return each.manadatory;
      });

      appData.preferred = filter(skillsData.flat(), (each) => {
        return !each.manadatory;
      });
      // structuring data to required display format
      const formattedData = {
        required: {},
        preferred: {},
      };
      // formatting required data to display format
      appData.required.map((skill) => {
        if (!formattedData.required[skill.skillName]) {
          formattedData.required[skill.skillName] = [];
        }
        formattedData.required[skill.skillName] = [
          ...formattedData.required[skill.skillName],
          getSkillvalue(skill),
        ];
        return null;
      });
      // formatting preferred data to display format
      appData.preferred.map((skill) => {
        if (!formattedData.preferred[skill.skillName]) {
          formattedData.preferred[skill.skillName] = [];
        }
        formattedData.preferred[skill.skillName] = [
          ...formattedData.preferred[skill.skillName],
          getSkillvalue(skill),
        ];
        return null;
      });
      setValues({
        ...values,
        formattedApplicantData: appData,
        displayApplicantData: formattedData,
        previewData: jobData.job,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobData]);

  //returns true value
  const returnPositiveValue = (obj) => {
    let keys = Object.keys(obj || {});

    let filtered = keys.filter(function (key) {
      return obj[key];
    });
    return filtered;
  };

  //returns formatted date in US
  const getPreviewDate = (date) => {
    if (!date) {
      return "";
    }
    const NewDt = new Date(date);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    }).format(NewDt);
  };

  const returnSalaryRange = (salary) => {
    switch (salary) {
      case "Hourly":
        return 2;
      case "Daily":
        return 12;
      case "Weekly":
        return 250;
      case "Monthly":
        return 600;
      case "Yearly":
        return 3000;
      default:
        return 2;
    }
  };

  // returns formatted salary in USD
  const getFormattedCurrency = (salary) => {
    if (!salary) {
      return "";
    }
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(
      salary * returnSalaryRange(get(jobData, "job.primaryPoints.duration", ""))
    );
  };

  //returns skill data
  const getSkillvalue = (skill) => {
    if (skill.skills && skill.levelName) {
      return `${skill.skills} - ${skill.levelName}`;
    }
    return skill.skills;
  };

  // This method returns required section of applicant qualification
  const requriedSkillsSection = () => {
    const { required } = displayApplicantData;
    if (!required) {
      return;
    }
    const allKeys = Object.keys(required);
    return map(allKeys, (key) => {
      return (
        <>
          {key !== "Add Your Own Text Challenge Question" &&
            key !== "Add Your Yes/No Challenge Question" &&
            key !== "ADD YOUR OWN TEXT CHALLENGE QUESTION" &&
            key !==
              "Additional Job Requirements or Notes to Applicant, that will appear on the Job Listing" &&
            key !== "ADD YOUR OWN YES/NO CHALLENGE QUESTION" && (
              <Text
                size={14}
                family="gillsans_r"
                color={color.primary_palette.black}
                className={classes.padding_5}
              >
                <span className={`${classes.gray_font} ${classes.padding_R_5}`}>
                  {key}
                </span>
                <span style={{ fontSize: pxToRem(15) }}>
                  {map(required[key], (value, idx) => {
                    if (idx === required[key]["length"] - 1) {
                      return value;
                    }
                    return value + ", ";
                  })}
                </span>
              </Text>
            )}
        </>
      );
    });
  };

  const getPublicProfileLink = (companyInfo) => () => {
    if (!companyInfo) {
      return;
    }
    window.open(
      `${BASEURL.URL}cpack/company/public/${companyInfo.tradeWorkUrl}`,
      "_blank" // <- This is what makes it open in a new window.
    );
  };

  // This method returns preferred section of applicant qualification
  const preferredSkillsSection = () => {
    const { preferred } = displayApplicantData;
    if (!preferred) {
      return;
    }
    const allKeys = Object.keys(preferred);
    return map(allKeys, (key) => {
      return (
        <>
          {key !== "Add Your Own Text Challenge Question" &&
            key !== "Add Your Yes/No Challenge Question" &&
            key !== "ADD YOUR OWN TEXT CHALLENGE QUESTION" &&
            key !==
              "Additional Job Requirements or Notes to Applicant, that will appear on the Job Listing" &&
            key !== "ADD YOUR OWN YES/NO CHALLENGE QUESTION" && (
              <Text
                size={14}
                family="gillsans_r"
                color={color.primary_palette.black}
                className={classes.padding_5}
              >
                <span
                  className={`${classes.gray_font} ${classes.padding_R_5} ${classes.txtTransform}`}
                >
                  {key}
                </span>
                <span style={{ fontSize: pxToRem(15) }}>
                  {map(preferred[key], (value, idx) => {
                    if (idx === preferred[key]["length"] - 1) {
                      return value;
                    }
                    return value + ", ";
                  })}
                </span>
              </Text>
            )}
        </>
      );
    });
  };

  return (
    <CustomModal open={open}>
      <>
        <div className={classes.wrapper}>
          <div className={classes.txtRight}>
            {/* <CloseIcon className={classes.close_styles} /> */}
            <img
              src="assets/images/Thick X Black.svg"
              alt="close"
              className={classes.close_styles}
              onClick={onClose}
            />
          </div>
          <CustomScrollbars style={{ minHeight: "660px" }}>
            <div className={edit && `${classes.border_edit_section}`}>
              <div
                className={`${classes.preview_width} ${
                  classes.border_bottom_none
                } ${edit && `${classes.border_none}`}`}
              >
                <Row className={edit && classes.edit_space}>
                  <Text
                    size={30}
                    family="gillsans_sb"
                    color={color.form_colors.blueberry_purple}
                    className={`${classes.txtTransform} ${classes.paddingSides}`}
                  >
                    {get(previewData, "basicDetails.jobTitle", "")}
                  </Text>
                </Row>
                <Row className={edit && `${classes.listingHover}`}>
                  <Text
                    size={14.4}
                    family="gillsans_r"
                    color={color.primary_palette.black}
                    className={`${classes.border_transparent} ${classes.paddingSides}`}
                  >
                    {get(previewData, "jobDescription.jobListing", "")}
                  </Text>
                </Row>
                <Grid container xs={12} className={classes.padding_top_11}>
                  <Grid item xs={2} style={{ height: "140px" }}>
                    <div
                      className={`${edit && `${classes.hover_purple}`} ${
                        classes.logo_wrapper
                      }`}
                    >
                      <img
                        className={`${classes.companyLogo} ${classes.border_transparent}`}
                        src={
                          get(
                            previewData,
                            "companyId.companyLogo",
                            "assets/images/job_desc.PNG"
                          ) || "assets/images/job_desc.PNG"
                        }
                        alt="companyLogo"
                      />
                    </div>
                  </Grid>
                  <Grid item xs={6} style={{ marginLeft: "20px" }}>
                    <Text
                      size={24}
                      family="avenir_light"
                      transform="uppercase"
                      color={color.primary_palette.franklin_purple}
                      className={`${edit && `${classes.companyName_hover}`} ${
                        classes.border_transparent
                      } ${classes.txtTransform}`}
                    >
                      {get(previewData, "primaryPoints.companyName", "")}
                    </Text>
                    <Row className={edit && `${classes.postedBlock}`}>
                      <div>
                        <Text
                          size={17}
                          family="gillsans_r"
                          color={color.primary_palette.black}
                        >
                          {get(previewData, "basicDetails.location.name", "")}
                        </Text>
                        <Text
                          size={17}
                          family="gillsans_r"
                          color={color.primary_palette.black}
                        >
                          {strings.preview_post.titles.posted_on}
                          {getPreviewDate(
                            get(
                              previewData,
                              "basicDetails.createdOrUpdated",
                              ""
                            )
                          )}
                        </Text>
                      </div>
                    </Row>
                    {!edit && (
                      <Text
                        size={17}
                        family="gillsans_r"
                        color={color.primary_palette.black}
                      >
                        {get(previewData, "basicDetails.tradeName", "")},{" "}
                        {previewData &&
                          get(previewData, "primaryPoints.level", []).join(
                            ", "
                          )}
                      </Text>
                    )}
                  </Grid>
                </Grid>
                <div className={edit && `${classes.summaryHover}`}>
                  <div className={classes.txtRight}></div>
                  <div
                    dangerouslySetInnerHTML={{
                      __html: get(
                        previewData,
                        "jobDescription.jobDescriptionSummary",
                        ""
                      ),
                    }}
                    className={`${classes.paddingSides} ${classes.border_transparent} 
             ${classes.descriptionData}`}
                  ></div>
                </div>
                <Grid container xs={12} className={classes.padding_top_30}>
                  <Grid
                    item
                    xs={6}
                    className={`${classes.border_transparent} ${
                      classes.paddingSides
                    } ${edit && `${classes.hover_blue}`}`}
                  >
                    <Text
                      size={18}
                      family="gillsans_sb"
                      color={color.mock_steel}
                      className={classes.txtTransform}
                    >
                      {strings.preview_post.titles.compensation}
                    </Text>
                    <Text
                      size={15}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        {strings.preview_post.titles.salary}
                      </span>
                      {/* {strings.preview_post.titles.salary_amount} */}
                      {getFormattedCurrency(
                        get(previewData, "primaryPoints.minSalary", "")
                      )}
                      <span> - </span>
                      {getFormattedCurrency(
                        get(previewData, "primaryPoints.maxSalary", "")
                      )}
                      <span>, </span>
                      {get(previewData, "primaryPoints.duration", "")}
                    </Text>
                    {get(previewData, "primaryPoints.jobPerks.length") > 0 && (
                      <Text
                        size={15}
                        family="gillsans_r"
                        color={color.primary_palette.black}
                      >
                        <span className={classes.gray_font}>
                          ADDITIONAL PERKS
                        </span>{" "}
                        {previewData &&
                          previewData.primaryPoints &&
                          map(
                            previewData.primaryPoints.jobPerks,
                            (perk, index) => {
                              return (
                                <span className={classes.perks_data}>
                                  {get(perk, "name", "")}
                                  {previewData.primaryPoints.jobPerks.length -
                                    1 !==
                                    index && ", "}
                                </span>
                              );
                            }
                          )}
                      </Text>
                    )}
                    {get(previewData, "primaryPoints.benefits.length") > 0 && (
                      <Text
                        size={15}
                        family="gillsans_r"
                        color={color.primary_palette.black}
                      >
                        <span className={classes.gray_font}>
                          {strings.preview_post.titles.benefits}
                        </span>
                        {previewData &&
                          previewData.primaryPoints &&
                          map(
                            previewData.primaryPoints.benefits,
                            (perk, index) => {
                              return (
                                <span className={classes.perks_data}>
                                  {get(perk, "name", "")}
                                  {previewData.primaryPoints.benefits.length -
                                    1 !==
                                    index && ", "}
                                </span>
                              );
                            }
                          )}
                      </Text>
                    )}
                    {get(previewData, "primaryPoints.apprenticeship", [])
                      .length > 0 && (
                      <Text
                        size={15}
                        family="gillsans_r"
                        color={color.primary_palette.black}
                      >
                        <span className={classes.gray_font}>
                          {strings.preview_post.titles.apprenticeship}
                        </span>
                        <span className={classes.perks_data}>
                          {get(
                            previewData,
                            "primaryPoints.apprenticeship",
                            []
                          ).join(", ")}
                        </span>
                      </Text>
                    )}
                    {get(previewData, "primaryPoints.internship", []).length >
                      0 && (
                      <Text
                        size={15}
                        family="gillsans_r"
                        color={color.primary_palette.black}
                      >
                        <span className={classes.gray_font}>
                          {strings.preview_post.titles.internship}
                        </span>
                        <span className={classes.perks_data}>
                          {get(
                            previewData,
                            "primaryPoints.internship",
                            []
                          ).join(", ")}
                        </span>
                      </Text>
                    )}
                  </Grid>
                  <Grid item xs={6} className={classes.alignCenter}>
                    {get(previewData, "primaryPoints.jobDuration", []).includes(
                      "Apprenticeship"
                    ) && (
                      <div className={classes.txtCenter}>
                        <img
                          src="assets/images/appenticeship.png"
                          alt="appenticeship"
                        />
                      </div>
                    )}
                    {get(previewData, "primaryPoints.jobDuration", []).includes(
                      "Internship"
                    ) && (
                      <div className={classes.txtCenter}>
                        <img
                          src="assets/images/internship.PNG"
                          alt="internship"
                        />
                      </div>
                    )}
                  </Grid>
                </Grid>
                <Grid
                  container
                  xs={12}
                  className={`${classes.padding_top_30} ${
                    classes.border_transparent
                  } ${
                    edit && `${classes.hover_yellow} ${classes.paddingSides}`
                  }`}
                >
                  <Grid
                    item
                    xs={edit ? 12 : 6}
                    className={classes.padding_bottom_15}
                  >
                    <Text
                      size={18}
                      family="gillsans_sb"
                      color={color.mock_steel}
                      className={classes.txtTransform}
                    >
                      {strings.preview_post.titles.required}
                    </Text>
                    <div>{requriedSkillsSection()}</div>
                  </Grid>
                  <Grid
                    item
                    xs={edit ? 12 : 6}
                    className={classes.padding_bottom_15}
                  >
                    <Text
                      size={18}
                      family="gillsans_sb"
                      color={color.mock_steel}
                      className={classes.txtTransform}
                    >
                      {strings.preview_post.titles.Preferred}
                    </Text>
                    <div>{preferredSkillsSection()}</div>
                  </Grid>
                </Grid>
              </div>
              {/* middle block */}
              {/*  */}
              {!edit && (
                <Row className={classes.middle_block}>
                  <div className={classes.company_details}>
                    <Text
                      size={24}
                      family="avenir_light"
                      color={color.primary_palette.franklin_purple}
                      className={classes.txtTransform}
                      style={{ cursor: "pointer" }}
                      transform="uppercase"
                      onClick={getPublicProfileLink(
                        get(previewData, "companyProfile")
                      )}
                    >
                      {get(previewData, "primaryPoints.companyName", "")}
                    </Text>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        {strings.preview_post.titles.venue}
                      </span>
                      {map(
                        get(previewData, "basicCompanyDetails.venue", []).slice(
                          0,
                          5
                        ),
                        (venue, index) => {
                          if (
                            index !==
                            get(
                              previewData,
                              "basicCompanyDetails.venue.length",
                              1
                            ) -
                              1
                          ) {
                            return `${venue.name}, `;
                          }
                          return venue.name;
                        }
                      )}
                    </Text>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        {strings.preview_post.titles.company_vibe}
                      </span>
                      {map(
                        get(previewData, "basicCompanyDetails.vibes", []).slice(
                          0,
                          5
                        ),
                        (vibe, index) => {
                          if (
                            index !==
                            get(
                              previewData,
                              "basicCompanyDetails.vibes.length",
                              1
                            ) -
                              1
                          ) {
                            return `${vibe.name}, `;
                          }
                          return vibe.name;
                        }
                      )}
                    </Text>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        {strings.preview_post.titles.company_size}
                      </span>
                      {/* {get(previewData, "basicCompanyDetails.companySize")} */}
                      {get(previewData, "basicCompanyDetails.companySize", "")}
                    </Text>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        Company Buzz Words{" "}
                      </span>
                      {map(
                        get(
                          previewData,
                          "basicCompanyDetails.buzzWords",
                          []
                        ).slice(0, 5),
                        (vibe, index) => {
                          if (
                            index !==
                            get(
                              previewData,
                              "basicCompanyDetails.buzzWords.length",
                              1
                            ) -
                              1
                          ) {
                            return `${vibe.name}, `;
                          }
                          return vibe.name;
                        }
                      )}
                    </Text>
                    {get(previewData, "companyId.companyAccolades.length") >
                      0 && (
                      <Row className={classes.company_section}>
                        {map(
                          get(previewData, "companyId.companyAccolades").slice(
                            0,
                            3
                          ),
                          (accolade) => {
                            return (
                              <div className={classes.company_logos}>
                                <img src={accolade.logo} alt="companyLogo" />
                              </div>
                            );
                          }
                        )}
                      </Row>
                    )}
                  </div>
                  {get(previewData, "companyId.topFeatureImage1") && (
                    <div className={classes.company_snaps}>
                      <Text
                        size={15}
                        family="gillsans_sb"
                        color={color.primary_palette.franklin_purple}
                        className={`${classes.txtTransform}`}
                      >
                        FEATURED WORK
                      </Text>
                      <img
                        src={get(previewData, "companyId.topFeatureImage1")}
                        alt="company snaps"
                        className={classes.company_snaps_imgs}
                      />
                    </div>
                  )}
                  {get(previewData, "waterColor.length") > 0 && (
                    <div className={classes.company_links}>
                      <Text
                        size={15}
                        family="gillsans_sb"
                        color={color.primary_palette.franklin_purple}
                        className={classes.txtTransform}
                      >
                        {strings.preview_post.titles.water_cooler}
                      </Text>
                      <Row className={classes.person_info}>
                        {map(
                          get(previewData, "waterColor").slice(0, 3),
                          (headshot) => {
                            return (
                              <div>
                                <div className={classes.pos_rel}>
                                  <img
                                    src={headshot.headShotLink}
                                    className={classes.circle_img}
                                    alt="person"
                                  />
                                </div>
                                <Text
                                  size={12}
                                  family="gillsans_sb"
                                  color={color.black}
                                  className={classes.txtCenter}
                                >
                                  {headshot.teamMemberName}
                                </Text>
                                <Text
                                  size={12}
                                  family="gillsans_light"
                                  color={
                                    color.secondary_palette.grays.shadow_gray
                                  }
                                  className={classes.txtCenter}
                                >
                                  {headshot.title}
                                </Text>
                              </div>
                            );
                          }
                        )}
                      </Row>
                      <Text
                        size={12}
                        family="gillsans_sb"
                        color={color.primary_palette.franklin_purple}
                        className={`${classes.txtTransform} ${classes.txtRight} ${classes.txtDecoration} ${classes.padding_top_11}`}
                        style={{ cursor: "pointer" }}
                        onClick={getPublicProfileLink(
                          get(previewData, "companyProfile")
                        )}
                      >
                        {strings.preview_post.titles.view_company}
                      </Text>
                      <Text
                        size={12}
                        family="gillsans_sb"
                        color={color.primary_palette.franklin_purple}
                        className={`${classes.txtTransform} ${classes.txtRight} ${classes.txtDecoration}`}
                      >
                        {strings.preview_post.titles.franklin_report}
                      </Text>
                    </div>
                  )}
                </Row>
              )}

              {/*   */}
              {edit && (
                <div
                  className={`${classes.edit_company_details} ${classes.border_transparent} ${classes.hover_green} ${classes.edit_spacing_details}`}
                >
                  <div className={classes.company_details}>
                    <Text
                      size={16}
                      family="gillsans_sb"
                      color={color.primary_palette.franklin_purple}
                      className={classes.txtTransform}
                    >
                      COMPANY DETAILS
                    </Text>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        {strings.preview_post.titles.venue}
                      </span>
                      {map(
                        get(previewData, "basicCompanyDetails.venue", []),
                        (vibe, index) => {
                          if (
                            index !==
                            get(
                              previewData,
                              "basicCompanyDetails.venue.length",
                              1
                            ) -
                              1
                          ) {
                            return `${vibe.name}, `;
                          }
                          return vibe.name;
                        }
                      )}
                    </Text>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        {strings.preview_post.titles.company_vibe}
                      </span>
                      {map(
                        get(previewData, "basicCompanyDetails.vibes", []),
                        (vibe, index) => {
                          if (
                            index !==
                            get(
                              previewData,
                              "basicCompanyDetails.vibes.length",
                              1
                            ) -
                              1
                          ) {
                            return `${vibe.name}, `;
                          }
                          return vibe.name;
                        }
                      )}
                    </Text>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        {strings.preview_post.titles.company_size}
                      </span>
                      {/* {get(previewData, "basicCompanyDetails.companySize")} */}
                      {get(previewData, "basicCompanyDetails.companySize", "")}
                    </Text>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      <span className={classes.gray_font}>
                        {/* {strings.preview_post.titles.additionalperks} */}
                        Company Buzz Words{" "}
                      </span>
                      {map(
                        get(previewData, "basicCompanyDetails.buzzWords", []),
                        (vibe, index) => {
                          if (
                            index !==
                            get(
                              previewData,
                              "basicCompanyDetails.buzzWords.length",
                              1
                            ) -
                              1
                          ) {
                            return `${vibe.name}, `;
                          }
                          return vibe.name;
                        }
                      )}
                    </Text>
                  </div>
                  <div
                    className={`${classes.application_process} ${classes.border_bottom_none}`}
                  >
                    <Row>
                      <Text
                        size={18}
                        family="gillsans_sb"
                        color={color.primary_palette.franklin_purple}
                      >
                        {strings.preview_post.titles.aplication_process}
                      </Text>
                      <Text
                        size={14}
                        family="gillsans_r"
                        color={color.primary_palette.black}
                        className={`${classes.alignSelfCenter} ${classes.padding_left_50}`}
                      >
                        {get(
                          previewData,
                          "jobSubmission.isApplyThroughTradework",
                          false
                        ) ? (
                          <>
                            <img
                              src="assets/images/Red_Bolt.svg"
                              className={classes.red_bolt}
                              alt="red_bolt"
                            />
                            {/* {strings.preview_post.titles.easy_apply} */}
                          </>
                        ) : (
                          get(previewData, "jobSubmission.companyWebsite", "")
                        )}
                      </Text>
                    </Row>
                    <Grid container xs={12} className={classes.process_ind}>
                      <Grid xs={6}>
                        <Text
                          size={14}
                          family="gillsans_r"
                          color={color.primary_palette.black}
                          className={classes.padding_bottom_5}
                        >
                          <img
                            src="assets/icons/resume.svg"
                            className={classes.processimg_edit}
                            alt="resume"
                          />
                          {strings.preview_post.titles.resume_required}
                          {returnPositiveValue(
                            get(previewData, "jobSubmission.isResume", {})
                          )[0] === "prefered"
                            ? "preferred"
                            : returnPositiveValue(
                                get(previewData, "jobSubmission.isResume", {})
                              )}
                        </Text>
                        <Text
                          size={14}
                          family="gillsans_r"
                          color={color.primary_palette.black}
                          className={classes.padding_bottom_5}
                        >
                          <ElevatorPitchIcon
                            className={`${classes.elevator_pitch_icon} ${classes.elevator_spacing}`}
                          />
                          {strings.preview_post.titles.elevator_pitch}
                          {returnPositiveValue(
                            get(
                              previewData,
                              "jobSubmission.isElevatorPitch",
                              {}
                            )
                          )[0] === "prefered"
                            ? "preferred"
                            : returnPositiveValue(
                                get(
                                  previewData,
                                  "jobSubmission.isElevatorPitch",
                                  {}
                                )
                              )}
                        </Text>
                      </Grid>
                      <Grid xs={6}>
                        {get(
                          previewData,
                          "jobSubmission.isApplyThroughTradework",
                          ""
                        ) && (
                          <Text
                            size={14}
                            family="gillsans_r"
                            color={color.primary_palette.black}
                            className={classes.padding_bottom_5}
                          >
                            <img
                              src="assets/icons/square.png"
                              alt="Recommendations"
                              className={classes.robot_icon}
                            />
                            Recommendation Letter &nbsp;
                            {returnPositiveValue(
                              get(
                                previewData,
                                "jobSubmission.isRobortInterview",
                                {}
                              )
                            )[0] === "prefered"
                              ? "preferred"
                              : returnPositiveValue(
                                  get(
                                    previewData,
                                    "jobSubmission.isRobortInterview",
                                    {}
                                  )
                                )}
                          </Text>
                        )}
                        <Text
                          size={14}
                          family="gillsans_r"
                          color={color.primary_palette.black}
                          className={classes.padding_bottom_5}
                        >
                          <img
                            src="assets/icons/Mail_colored.svg"
                            className={classes.processimg_edit}
                            alt="resume"
                          />
                          {strings.preview_post.titles.cover_letter}
                          {returnPositiveValue(
                            get(previewData, "jobSubmission.isCoverLetter", {})
                          )[0] === "prefered"
                            ? "preferred"
                            : returnPositiveValue(
                                get(
                                  previewData,
                                  "jobSubmission.isCoverLetter",
                                  {}
                                )
                              )}
                        </Text>
                      </Grid>
                    </Grid>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      {strings.preview_post.titles.contact_info}
                      <span className={classes.txtCapital}>
                        {get(previewData, "jobSubmission.contactInfo.name", "")}
                      </span>{" "}
                      |{" "}
                      {formatPhoneNumber(
                        get(previewData, "jobSubmission.contactInfo.phone", "")
                      )}{" "}
                      |{" "}
                      {get(previewData, "jobSubmission.contactInfo.email", "")}
                    </Text>
                  </div>
                </div>
              )}
              <div
                className={`${classes.preview_width} ${
                  classes.border_top_none
                } ${classes.margin_b_20} ${edit && `${classes.border_none}`}`}
              >
                {/*  */}
                {!edit && (
                  <div className={`${classes.application_process}`}>
                    <Row>
                      <Text
                        size={18}
                        family="gillsans_sb"
                        color={color.primary_palette.franklin_purple}
                      >
                        {strings.preview_post.titles.aplication_process}
                      </Text>
                      <Text
                        size={14}
                        family="gillsans_r"
                        color={color.primary_palette.black}
                        className={`${classes.alignSelfCenter} ${classes.padding_left_12} `}
                      >
                        {get(
                          previewData,
                          "jobSubmission.isApplyThroughTradework",
                          false
                        ) ? (
                          <>
                            <img
                              src="assets/images/Red_Bolt.svg"
                              className={classes.red_bolt}
                              alt="red_bolt"
                            />
                            {/* {strings.preview_post.titles.easy_apply} */}
                          </>
                        ) : (
                          get(previewData, "jobSubmission.companyWebsite", "")
                        )}
                      </Text>
                    </Row>
                    <Grid container xs={12} className={classes.process_ind}>
                      <Grid item xs={2}>
                        <Text
                          size={14}
                          family="gillsans_r"
                          color={color.primary_palette.black}
                        >
                          <img
                            src="assets/icons/resume.svg"
                            className={classes.processimg}
                            alt="resume"
                          />
                          {strings.preview_post.titles.resume_required}
                          <span
                            style={{
                              color:
                                returnPositiveValue(
                                  get(previewData, "jobSubmission.isResume", {})
                                )[0] === "required" &&
                                color.primary_palette.christmas_red,
                            }}
                          >
                            {returnPositiveValue(
                              get(previewData, "jobSubmission.isResume", {})
                            )[0] === "prefered"
                              ? "preferred"
                              : returnPositiveValue(
                                  get(previewData, "jobSubmission.isResume", {})
                                )}
                          </span>
                        </Text>
                      </Grid>
                      <Grid item xs={3}>
                        <Text
                          size={14}
                          family="gillsans_r"
                          color={color.primary_palette.black}
                        >
                          <ElevatorPitchIcon
                            className={classes.elevator_pitch_icon}
                          />
                          {strings.preview_post.titles.elevator_pitch}
                          <span
                            style={{
                              color:
                                returnPositiveValue(
                                  get(
                                    previewData,
                                    "jobSubmission.isElevatorPitch",
                                    {}
                                  )
                                )[0] === "required" &&
                                color.primary_palette.christmas_red,
                            }}
                          >
                            {returnPositiveValue(
                              get(
                                previewData,
                                "jobSubmission.isElevatorPitch",
                                {}
                              )
                            )[0] === "prefered"
                              ? "preferred"
                              : returnPositiveValue(
                                  get(
                                    previewData,
                                    "jobSubmission.isElevatorPitch",
                                    {}
                                  )
                                )}
                          </span>
                        </Text>
                      </Grid>
                      {get(
                        previewData,
                        "jobSubmission.isApplyThroughTradework",
                        ""
                      ) && (
                        <Grid item xs={4}>
                          <Text
                            size={14}
                            family="gillsans_r"
                            color={color.primary_palette.black}
                          >
                            {/* <img
                              src="assets/images/robot.PNG"
                              className={classes.processimg}
                              alt="resume"
                            />
                            {strings.preview_post.titles.robot_interview} */}
                            <img
                              src="assets/icons/square.png"
                              alt="Recommendations"
                              className={classes.processimg}
                            />
                            Recommendation Letter{" "}
                            <span
                              style={{
                                color:
                                  returnPositiveValue(
                                    get(
                                      previewData,
                                      "jobSubmission.isRobortInterview",
                                      {}
                                    )
                                  )[0] === "required" &&
                                  color.primary_palette.christmas_red,
                              }}
                            >
                              {returnPositiveValue(
                                get(
                                  previewData,
                                  "jobSubmission.isRobortInterview",
                                  {}
                                )
                              )[0] === "prefered"
                                ? "preferred"
                                : returnPositiveValue(
                                    get(
                                      previewData,
                                      "jobSubmission.isRobortInterview",
                                      {}
                                    )
                                  )}
                            </span>
                          </Text>
                        </Grid>
                      )}
                      <Grid item xs={3}>
                        <Text
                          size={14}
                          family="gillsans_r"
                          color={color.primary_palette.black}
                        >
                          <img
                            src="assets/icons/Mail_colored.svg"
                            className={classes.processimg}
                            alt="resume"
                          />
                          {strings.preview_post.titles.cover_letter}
                          <span
                            style={{
                              color:
                                returnPositiveValue(
                                  get(
                                    previewData,
                                    "jobSubmission.isCoverLetter",
                                    {}
                                  )
                                )[0] === "required" &&
                                color.primary_palette.christmas_red,
                            }}
                          >
                            {returnPositiveValue(
                              get(
                                previewData,
                                "jobSubmission.isCoverLetter",
                                {}
                              )
                            )[0] === "prefered"
                              ? "preferred"
                              : returnPositiveValue(
                                  get(
                                    previewData,
                                    "jobSubmission.isCoverLetter",
                                    {}
                                  )
                                )}
                          </span>
                        </Text>
                      </Grid>
                    </Grid>
                    <Text
                      size={14}
                      family="gillsans_r"
                      color={color.primary_palette.black}
                    >
                      {strings.preview_post.titles.contact_info}
                      <span className={classes.txtCapital}>
                        {get(previewData, "jobSubmission.contactInfo.name", "")}
                      </span>{" "}
                      |{" "}
                      {formatPhoneNumber(
                        get(previewData, "jobSubmission.contactInfo.phone", "")
                      )}{" "}
                      |{" "}
                      {get(previewData, "jobSubmission.contactInfo.email", "")}
                    </Text>
                  </div>
                )}
                {/*  */}
                {!edit && previewData && (
                  <div className={classes.key_responsibilities_blk}>
                    <Text
                      size={18}
                      family="gillsans_sb"
                      color={color.primary_palette.franklin_purple}
                      className={classes.txtTransform}
                    >
                      {strings.preview_post.titles.key_responsibilities}
                    </Text>
                    <div
                      dangerouslySetInnerHTML={{
                        __html: get(
                          previewData,
                          "jobDescription.jobDescriptionKeyResponsiblity",
                          ""
                        ),
                      }}
                      className={classes.descriptionData_summary}
                    ></div>
                  </div>
                )}
              </div>
            </div>
          </CustomScrollbars>
        </div>
      </>
    </CustomModal>
  );
}

export default PreviewPostStyles(JobPreview);
