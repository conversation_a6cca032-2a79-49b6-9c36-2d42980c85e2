import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { get } from "lodash";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import Facebook_Icon from "../../data_display/icons/Facebook_Outline";
import Facebook_Icon_C from "../../data_display/icons/facebook_colored";
import Twitter_Icon from "../../data_display/icons/Twitter_Outline";
import Twitter_Icon_C from "../../data_display/icons/Twitter_Colored";
import LinkedIn_Icon from "../../data_display/icons/Linkedin_Outline";
import LinkedIn_Icon_C from "../../data_display/icons/linkedIn_colored";
import Youtube_Icon_C from "../../data_display/icons/youtube_colored";
import Youtube_Icon from "../../data_display/icons/youtube_outline";
import Instagram_Icon from "../../data_display/icons/Instagram_Outline";
import Instagram_Icon_C from "../../data_display/icons/Instagram_colored_pink";
import styleSheet from "./styles/company_feeds";
import { color } from "../../../utilities/themes";
import {
  TWITTER_SCRIPT_URL,
  TWITTER_URL,
  TWITTER_URL2,
  LINKEDIN_URL,
  INSTAGRAM_AUTHENTICATION_URL,
  FACEBOOK_PLUGIN,
} from "../../../constants";
import { Button } from "@material-ui/core";

let script = null;

function CompanyFeeds(props) {
  const { classes, companyPitch } = props;
  const [values, setValues] = useState({
    active_social_tab: "twitter",
    feedsCredentials: {},
  });
  const { active_social_tab, feedsCredentials } = values;

  const socialMediaProfiles = useSelector(
    (state) => state.Profile.companypitch
  );
  const instagramFeeds = useSelector((state) => state.Profile.instagramfeeds);
  const isPublicProfile = localStorage.getItem("publicPage") === "true";

  useEffect(() => {
    setValues({ ...values, active_social_tab: "twitter" });
  }, []);

  useEffect(() => {
    if (companyPitch && companyPitch.socialMediaFeeds) {
      const profileUrls = companyPitch.socialMediaFeeds;
      const facebookUrl = getURL(get(profileUrls, "facebook", ""));
      const twitterUrl = getURL(get(profileUrls, "twitter", ""));
      const linkedInUrl = getURL(get(profileUrls, "linkedin", ""));
      const instagramUrl = getURL(get(profileUrls, "instagram", ""));
      const youtubeUrl = getURL(get(profileUrls, "youtube", ""));
      setValues({
        ...values,
        feedsCredentials: {
          facebookUrl,
          twitterUrl,
          instagramUrl,
          linkedInUrl,
          youtubeUrl,
        },
      });
    }
    // if (active_social_tab && active_social_tab === "twitter") {
    //   let script = document.createElement("script");
    //   script.src = TWITTER_SCRIPT_URL;
    //   script.async = true;
    //   document.body.appendChild(script);
    //   return () => {
    //     script.parentNode.removeChild(script);
    //     script = null;
    //   };
    // }
  }, [companyPitch]);

  const getURL = (url) => {
    if (!url) {
      return;
    }
    if (url.includes("/")) {
      return url.replace("/", "/");
    }
    if (url.includes("@")) {
      return url.replace("@", "@");
    }
    return url;
  };

  const redirectToLinkedIn = () => {
    if (feedsCredentials.linkedInUrl) {
      const url = getURL(feedsCredentials.linkedInUrl);
      if (url.includes("linkedin.com")) {
        window.open(url);
        return;
      } else {
        window.open(`${LINKEDIN_URL}${url}`);
        return;
      }
    }
    window.open(LINKEDIN_URL);
  };

  const handleTabClick = (tab) => () => {
    if (tab === "linkedin") {
      redirectToLinkedIn();
    }
    setValues({ ...values, active_social_tab: tab });
  };

  const authorizeInstagram = () => {
    let host = get(window.location, "host", "");
    if (host.includes("-dev-")) {
      window.location.href =
        INSTAGRAM_AUTHENTICATION_URL.P1 +
        get(feedsCredentials, "instagramId", "") +
        INSTAGRAM_AUTHENTICATION_URL.P2_DEV;
    }
    if (host.includes("-qa-")) {
      window.location.href =
        INSTAGRAM_AUTHENTICATION_URL.P1 +
        get(feedsCredentials, "instagramId", "") +
        INSTAGRAM_AUTHENTICATION_URL.P2_QA;
    }
    if (host.includes("-stage-")) {
      window.location.href =
        INSTAGRAM_AUTHENTICATION_URL.P1 +
        get(feedsCredentials, "instagramId", "") +
        INSTAGRAM_AUTHENTICATION_URL.P2_STAGE;
    }
    window.location.href =
      INSTAGRAM_AUTHENTICATION_URL.P1 +
      get(feedsCredentials, "instagramId", "") +
      INSTAGRAM_AUTHENTICATION_URL.P2_QA;
  };

  const returnInstagramBlock = () => {
    if (get(instagramFeeds, "length", 0) > 0) {
      return (
        <div className={classes.insta_block_wrap}>
          {instagramFeeds.map((each) => {
            return <img className={classes.insta_img} src={each.media_url} />;
          })}
        </div>
      );
    }
  };
  return (
    <div className={classes.parent_wrap}>
      <Row className={classes.icons_container}>
        {get(feedsCredentials, "facebookUrl") !== "" &&
        get(feedsCredentials, "facebookUrl") !== undefined ? (
          <Facebook_Icon_C
            className={classes.social_media_icons}
            url={feedsCredentials ? feedsCredentials.facebookUrl : ""}
            redirect
          />
        ) : (
          !isPublicProfile?
          <Facebook_Icon
            name="facebook"
            className={classes.social_media_icons}
            onClick={handleTabClick("facebook")}
          />:""
        )}

        {get(feedsCredentials, "twitterUrl") !== "" &&
        get(feedsCredentials, "twitterUrl") !== undefined ? (
          <Twitter_Icon_C
            className={classes.social_media_icons}
            url={feedsCredentials ? feedsCredentials.twitterUrl : ""}
            redirect
          />
        ) : (
          !isPublicProfile?
          <Twitter_Icon
            name="twitter"
            className={classes.social_media_icons}
            onClick={handleTabClick("twitter")}
          />:""
        )}
        {get(feedsCredentials, "linkedInUrl") !== "" &&
        get(feedsCredentials, "linkedInUrl") !== undefined ? (
          <LinkedIn_Icon_C
            className={classes.social_media_icons}
            url={feedsCredentials ? feedsCredentials.linkedInUrl : ""}
            redirect
          />
        ) : (
          !isPublicProfile?
          <LinkedIn_Icon
            name="linkedin"
            className={classes.social_media_icons}
            onClick={handleTabClick("linkedin")}
          />:""
        )}
        {get(feedsCredentials, "instagramUrl") !== "" &&
        get(feedsCredentials, "instagramUrl") !== undefined ? (
          <Instagram_Icon_C
            className={classes.social_media_icons}
            url={feedsCredentials ? feedsCredentials.instagramUrl : ""}
            redirect
          />
        ) : (
          !isPublicProfile?
          <Instagram_Icon
            name="instagram"
            className={classes.social_media_icons}
            onClick={handleTabClick("instagram")}
          />:""
        )}
        {get(feedsCredentials, "youtubeUrl") !== "" &&
        get(feedsCredentials, "youtubeUrl") !== undefined ? (
          <Youtube_Icon_C
            className={classes.social_media_icons}
            url={feedsCredentials ? feedsCredentials.youtubeUrl : ""}
            redirect
          />
        ) : (
          !isPublicProfile?
          <Youtube_Icon
            name="instagram"
            className={classes.social_media_icons}
          />:""
        )}
      </Row>
      {active_social_tab === "facebook" && feedsCredentials.facebookUrl && (
        <div className={classes.facebook_plugin}>
          <iframe
            className={classes.facebook_plugin}
            src={
              FACEBOOK_PLUGIN.url1 +
              feedsCredentials.facebookUrl +
              FACEBOOK_PLUGIN.url2
            }
            width="270"
            height="375"
            minWidth="270"
            style={{ border: "none", overflowX: "scroll", minWidth: "270px" }}
            scrolling="no"
            frameborder="0"
            allowTransparency="true"
            allow="encrypted-media"
          />
        </div>
      )}
      {active_social_tab === "twitter" && feedsCredentials.twitterUrl && (
        <div>
          <a
            className="twitter-timeline"
            data-width="270"
            data-height="375"
            data-theme="light"
            href={TWITTER_URL + feedsCredentials.twitterUrl + TWITTER_URL2}
          />
        </div>
      )}
      {/* {active_social_tab === "linkedin" && (
        <div>
          <Button className={classes.linkedIn_button} disabled>
            Click on the LinkedIn logo above to see our feed
          </Button>
        </div>
      )}
      {active_social_tab === "instagram" && <div>{returnInstagramBlock()}</div>} */}
    </div>
  );
}

export default styleSheet(CompanyFeeds);
