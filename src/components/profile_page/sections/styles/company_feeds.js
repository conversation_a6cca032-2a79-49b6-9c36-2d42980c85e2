import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../../utilities/themes";

const styles = withStyles({
  parent_wrap: {
    padding: `${pxToRem(13)} ${pxToRem(10)}`
  },
  facebook_plugin: {
    minWidth: "270px !important",
    width: "270px !important",
    "& _2p3a": {
      minWidth: "270px !important",
      width: "270px !important",
    },
  },
  icons_container: {
    width: "100%", 
    margin:"0px auto", 
    paddingTop: pxToRem(5), 
    display: "inline-block",
    textAlign: "center"
  },
  social_media_icons: {
    height: pxToRem(40),
    width: pxToRem(40),
    paddingLeft: pxToRem(5),
    paddingRight: pxToRem(5),
    // width: "60%",
    cursor: "pointer",
  },
  profile_pic_wrapper: {
    height: pxToRem(30),
    width: pxToRem(30),
    position: "relative",
    top: pxToRem(4),
    "& img": {
      width: "100%",
      borderRadius: "100%",
    },
  },
  username_wrapper: {
    margin: `${pxToRem(5)} ${pxToRem(10)}`,
  },
  feeds_wrapper: {
    marginTop: pxToRem(10),
    borderBottom: `solid ${pxToRem(1)} #c3c3c3`,
  },
  action_row: {
    width: "100%",
    margin: `${pxToRem(10)} ${pxToRem(0)} ${pxToRem(5)} ${pxToRem(0)}`,
  },
  twitter_actions: {
    width: "50%",
  },
  action_like: {
    height: pxToRem(12),
    width: pxToRem(12),
    marginLeft: pxToRem(10),
    opacity: "0.66",
  },
  action_share: {
    height: pxToRem(12),
    width: pxToRem(12),
    marginLeft: pxToRem(25),
    opacity: "0.66",
  },

  twitter_date: {
    width: "50%",
    textAlign: "right",
  },
  fb_styles: {
    paddingBottom: pxToRem(1),
    "& ._10b4": {
      "&::-webkit-scrollbar": {
        width: pxToRem(10),
      },
      "&::-webkit-scrollbar-thumb": {
        backgroundColor: color.secondary_palette.grays.background_gray,
        borderRadius: pxToRem(20),
      },
    },
  },
  linkedIn_button: {
    margin: `${pxToRem(100)} ${pxToRem(10)}`,
    backgroundColor: color.secondary_palette.blues.hover_blue,
    color: `${color.primary_palette.white} !important`,
    borderRadius: `${pxToRem(5)} !important`,
    textTransform: "none !important",
    fontSize: pxToRem(16),
  },
  instagram_button: {
    margin: `${pxToRem(100)} ${pxToRem(50)}`,
    backgroundColor: color.secondary_palette.purples.franklin_purple,
    color: `${color.primary_palette.white} !important`,
    borderRadius: `${pxToRem(5)} !important`,
    textTransform: "none !important",
    fontSize: pxToRem(16),
  },
  insta_block_wrap: {
    overflowY: "auto",
    maxHeight: pxToRem(360),
  },
  insta_img: {
    width: "30%",
    height: pxToRem(70),
    marginRight: pxToRem(5),
  },
  provide_auth: {
    marginTop: pxToRem(100),
  },
  insta_img_color: {
    width: pxToRem(32),
  },
});

export default styles;
