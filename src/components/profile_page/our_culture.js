import React, { useState, useEffect } from "react";
import { map, get, sortBy } from "lodash";
import { useSelector } from "react-redux";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import StyleSheet from "./styles/profile_container_styles";
import { color, pxToRem } from "../../utilities/themes";
import { useHistory } from "react-router-dom";
import SvgPencil from "../data_display/icons/Pencil";

function OurCulture(props) {
  const { classes, userRole } = props;
  const [values, setValues] = useState({});
  const history = useHistory();
  const defaultCulture = {
    layoutType: "layout1",
    content:
      "With people from Chile, Mexico, Spain, and the majority of Latin and South American countries represented, the team affectionately refers to the office as the United Nations. Their diverse backgrounds allow for a unique understanding of the Hispanic and multicultural segment—and make for an incredibly collaborative team.",
    title: "Company Diversity",
    url: ["assets/images/Shadow Culture.jpg"],
  };

  const ourCultureLayouts = useSelector(
    (state) => state.Profile.companyhighlights
  );
  const isPublicProfile = localStorage.getItem("publicPage") === "true";

  useEffect(() => {
    if (ourCultureLayouts) {
      const sortedLayouts = sortBy(ourCultureLayouts, "layoutType");
      setValues({ ...values, companyLayouts: [...sortedLayouts] });
    }
  }, [ourCultureLayouts]);

  const TextWithLineBreaks = ({ text }, className) => {
    const formattedText = text.split("\n").map((line, index) => (
      <React.Fragment key={index}>
        {line}
        <br />
      </React.Fragment>
    ));

    return (
      <div
        className={className}
        style={{
          lineHeight: "18px",
          fontSize: pxToRem(14),
          fontFamily: "gillsans_light",
          paddingTop: pxToRem(10),
        }}
      >
        {formattedText}
      </div>
    );
  };

  const getDefaultLayout = (layout) => {
    return (
      <Row className={classes.o_c_layout_wrapper}>
        <div>
          <img
            src={get(layout, "url[0]", "assets/images/Gray_Frame_02.png")}
            className={classes.o_c_default_img}
          />
        </div>
        <Row className={classes.layout_content}>
          <div className={classes.margin_left_25}>
            <Text size={20} color="#b098be" family="gillsans_r">
              {get(layout, "title", "")}
            </Text>
            {/* <Text
              size={14}
              family="gillsans_light"
              // fontWeight={300}
              className={classes.padding_top_10}
              color="#979797"
              style={{ lineHeight: "18px" }}
            >
              {get(layout, "content", "")}
            </Text> */}
            {TextWithLineBreaks(
              { text: get(layout, "content", "") },
              classes.padding_top_10
            )}
          </div>
        </Row>
      </Row>
    );
  };

  const getLayout = (layout) => {
    switch (layout.layoutType) {
      case "layout1":
        return (
          <Row className={classes.o_c_layout_wrapper}>
            <div>
              <img
                src={get(layout, "url[0]", "assets/images/Gray_Frame_02.png")}
                className={classes.o_c_l1_img}
              />
            </div>
            <Row className={classes.layout_content}>
              <div className={classes.margin_left_25}>
                <Text
                  size={20}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_r"
                >
                  {get(layout, "title", "")}
                </Text>
                {TextWithLineBreaks(
                  { text: get(layout, "content", "") },
                  classes.padding_top_10
                )}
                {/* <Text
                  size={14}
                  family="gillsans_light"
                  // fontWeight={300}
                  style={{ lineHeight: "18px" }}
                  className={classes.padding_top_10}
                >
                  {get(layout, "content", "")}
                </Text> */}
              </div>
            </Row>
          </Row>
        );

      case "layout2":
        return (
          <>
            <Row className={classes.o_c_layout_wrapper}>
              <Row className={classes.layout_content}>
                <div>
                  <Text
                    size={20}
                    color={color.secondary_palette.purples.franklin_purple}
                    family="gillsans_r"
                    className={classes.o_c_title_right}
                  >
                    {get(layout, "title", "")}
                  </Text>
                  <Text
                    size={14}
                    family="gillsans_light"
                    // fontWeight={300}
                    className={classes.o_c2_content_left}
                  >
                    {get(layout, "paragraph1", "")}
                  </Text>
                </div>
              </Row>
              <div>
                <img
                  src={get(layout, "url[0]", "assets/images/Gray_Frame_02.png")}
                  className={classes.o_c_l2_img}
                />
              </div>
            </Row>
            <Row className={classes.o_c_layout_wrapper}>
              <div>
                <img
                  src={get(layout, "url[1]", "assets/images/Gray_Frame_02.png")}
                  className={classes.o_c_l2_img}
                />
              </div>
              <Row className={classes.layout_content}>
                <div className={classes.margin_left_25}>
                  <Text
                    size={20}
                    color={color.secondary_palette.purples.franklin_purple}
                    family="gillsans_r"
                    className={classes.o_c_title_right}
                  >
                    {get(layout, "title2", "")}
                  </Text>
                  <Text
                    size={14}
                    family="gillsans_light"
                    // fontWeight={300}
                    className={classes.o_c_content_right}
                  >
                    {get(layout, "paragraph2", "")}
                  </Text>
                </div>
              </Row>
            </Row>
          </>
        );
      case "layout3":
        return (
          <Row className={classes.o_c_layout_wrapper}>
            <Row className={classes.layout_content}>
              <div className={classes.margin_right_25}>
                <Text
                  size={20}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_r"
                  className={classes.o_c_title_right}
                >
                  {get(layout, "title", "")}
                </Text>
                {/* <Text
                  size={14}
                  family="gillsans_light"
                  fontWeight={300}
                  className={classes.o_c_content_left}
                >
                  {get(layout, "content", "")}
                </Text> */}
                {TextWithLineBreaks(
                  { text: get(layout, "content", "") },
                  classes.o_c_content_left
                )}
              </div>
            </Row>
            <div>
              <img
                src={get(layout, "url[0]", "assets/images/Gray_Frame_02.png")}
                className={`${classes.o_c_l3_img1} ${classes.margin_botton_18}`}
              />
              <img
                src={get(layout, "url[1]", "assets/images/Gray_Frame_02.png")}
                className={classes.o_c_l3_img2}
              />
            </div>
          </Row>
        );
      case "layout4":
        return (
          <Row className={classes.o_c_layout_wrapper}>
            <div>
              <img
                src={get(layout, "url[0]", "assets/images/Gray_Frame_02.png")}
                className={classes.o_c_l4_img1}
              />
            </div>
            <Row className={classes.layout_content}>
              <div className={classes.margin_left_25}>
                <Text
                  size={20}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_r"
                >
                  {get(layout, "title", "")}
                </Text>
                {/* <Text
                  size={14}
                  family="gillsans_light"
                  // fontWeight={300}
                  className={classes.o_c_content_right}
                >
                  {get(layout, "content", "")}
                </Text> */}
                {TextWithLineBreaks(
                  { text: get(layout, "content", "") },
                  classes.o_c_content_right
                )}
              </div>
            </Row>
          </Row>
        );
      case "layout5":
        return (
          <Row className={classes.o_c_layout_wrapper}>
            <Row className={classes.layout_content}>
              <div className={classes.margin_right_25}>
                <Text
                  size={20}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_r"
                  className={classes.o_c_title_right}
                >
                  {get(layout, "title", "")}
                </Text>
                {/* <Text
                  size={14}
                  family="gillsans_light"
                  fontWeight={300}
                  className={classes.o_c_content_left}
                >
                  {get(layout, "content", "")}
                </Text> */}
                {TextWithLineBreaks(
                  { text: get(layout, "content", "") },
                  classes.o_c_content_left
                )}
              </div>
            </Row>
            <div>
              <img
                src={get(layout, "url[0]", "assets/images/Gray_Frame_02.png")}
                className={classes.o_c_l5_img1}
              />
            </div>
          </Row>
        );

      default:
        break;
    }
  };

  const handleButtonClick = (step) => (e) => {
    history.push({ pathname: "/wizard", state: { step, edit: true } });
  };

  const { companyLayouts } = values;
  return (
    <div className={classes.our_culture_wrapper}>
      {/* {!isPublicProfile && get(companyLayouts, "length", 0) > 0 && ( */}
      {get(companyLayouts, "length", 0) === 0 && isPublicProfile ? (
        <></>
      ) : (
        <Text
          size={20}
          color={color.form_colors.royal_purple_1}
          family="gillsans_r"
          fontWeight={600}
          className={classes.title_underlined}
        >
          OUR CULTURE{" "}
          {!isPublicProfile && userRole !== "staff" && (
            // <img
            //   src="assets/icons/pencil.svg"
            //   className={classes.edit_company_pencil}
            //   onClick={handleButtonClick(5)}
            // />
            <SvgPencil
              className={classes.edit_company_pencil}
              onClick={handleButtonClick(5)}
            />
          )}
        </Text>
      )}
      {/* )} */}
      {get(companyLayouts, "length", 0) > 0 ? (
        <div className={classes.our_culture_content}>
          {companyLayouts &&
            companyLayouts.map((eachLayout) => {
              return getLayout(eachLayout);
            })}
        </div>
      ) : (
        <div className={classes.our_culture_content}>
          {!isPublicProfile &&
            userRole !== "staff" &&
            getDefaultLayout(defaultCulture)}
        </div>
      )}
    </div>
  );
}
export default StyleSheet(OurCulture);
