import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import { get, map } from "lodash";

import Text from "../common/ui_kit/text";
import Row from "../common/ui_kit/row";
import StyleSheet from "./styles/profile_container_styles";
import CompanyFeeds from "./sections/company_feeds";
import CurrentlyLoggedUser from "../modals/currentlyLoggedUser";
import { color, pxToRem } from "../../utilities/themes";
import { ProfileActions } from "../../redux/actions";
import {
  PROFILE_TYPES,
  PUBLIC_PROFILE,
  TOKEN_KEY,
  APP_URLS_JPACK,
  BASEURL,
} from "../../constants";
import { useHistory } from "react-router-dom";
import JobPreview from "./job_preview";
import SvgPencil from "../data_display/icons/Pencil";
import { CustomToggleButton } from "../common/ui_kit";
import CustomButton from "../navigations/custom_buttons";

function RightSections(props) {
  const { classes, handlePingAdmin, companyPitch, userRole } = props;
  const dispatch = useDispatch();
  const [clickedCopy, setClickedCopy] = useState(false);
  const [isPublicView, setIsPublic] = useState(false);

  const [values, setValues] = useState({
    activePannel: "panel1",
    currentlyLoggedUser: false,
    officeData: "",
    searchText: "",
    openJobPreviewModal: false,
  });
  const { activePannel, currentlyLoggedUser, searchText, openJobPreviewModal } =
    values;
  const history = useHistory();
  const isPublicProfile = localStorage.getItem("publicPage") === "true";
  const companyInfo = useSelector((state) => state.Profile.companyinfo);
  const businesscard = useSelector((state) => state.Profile.businesscard);
  const openPositions = useSelector((state) => state.Profile.openPositions);
  useEffect(() => {
    if (get(businesscard, "length", false)) {
      const { businessCard } = businesscard;
      setValues({ ...values, offices: [...businessCard] });
    }
  }, [businesscard, companyInfo]);

  useEffect(() => {
    if (companyInfo) {
      setIsPublic(companyInfo.isPublic);
      // setValues({ ...values, isPublicView: companyInfo.isPublic });
    }
  }, [companyInfo]);

  useEffect(() => {
    dispatch(ProfileActions.getOpenPositionData({ search: "" }));
    dispatch(ProfileActions.getCompanyMembersById());
  }, []);

  const handleExpandPanel = (index) => () => {
    if (activePannel !== `panel${index}`) {
      setValues({ ...values, activePannel: `panel${index}` });
    }
    return;
  };

  const handleButtonClick = (step) => (e) => {
    history.push({ pathname: "/wizard", state: { step, edit: true } });
  };

  const getPublicProfileLink = () => {
    if (!companyInfo) {
      return;
    }
    let host = get(window.location, "host", "");
    if (host) {
      if (host.includes("localhost")) {
        host = "localhost";
        return `${PUBLIC_PROFILE.local}${companyInfo.tradeWorkUrl}`;
      } else {
        if (host.includes("-dev-")) {
          return `${PUBLIC_PROFILE.dev}${companyInfo.tradeWorkUrl}`;
        }
        if (host.includes("-qa-")) {
          return `${PUBLIC_PROFILE.qa}${companyInfo.tradeWorkUrl}`;
        }
        if (host.includes("-stage-")) {
          return `${PUBLIC_PROFILE.stage}${companyInfo.tradeWorkUrl}`;
        }
        if (host.includes("twwstage")) {
          return `${BASEURL.URL}cpack/company/public/${companyInfo.tradeWorkUrl}`;
        } else {
          return `${BASEURL.URL}cpack/company/public/${companyInfo.tradeWorkUrl}`;
        }
      }
    }
    return null;
  };

  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let token = localStorage.getItem("tradeworks_user_token");
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}wpack`;
      // host = "twwstage.franklinreport.com/wpack";
      window.location.href = `${host}/${token}/${companyId}`;
    }
  };

  const returnContacts = (offices) => {
    if (!businesscard) {
      return;
    }
    const { businessCard } = businesscard;
    const { activePannel } = values;
    if (businessCard && businessCard.length > 0) {
      return map(businessCard, (office, index) => {
        return (
          <div className={classes.exp_pannel_wrap}>
            <ExpansionPanel
              onClick={handleExpandPanel(index + 1)}
              expanded={activePannel === `panel${index + 1}`}
              className={
                activePannel !== `panel${index + 1}` &&
                classes.exp_pannel_closed
              }
            >
              <ExpansionPanelSummary
                expandIcon={
                  activePannel !== `panel${index + 1}` && (
                    <img
                      className={classes.expand_icon}
                      src="assets/images/circle_plus_exp.png"
                    />
                  )
                }
                aria-controls="panel1a-content"
                id="panel1a-header"
              >
                <Text
                  size={20}
                  color={color.primary_palette.franklin_purple}
                  family={
                    activePannel === `panel${index + 1}`
                      ? "gillsans_sb"
                      : "gillsans_r"
                  }
                >
                  {get(office, "city", "")}
                </Text>
                {/* {get(office, "isHeadOffice", false) && ( */}
                {/* <img
                  src="assets/images/contact_email.png"
                  onClick={togglecurrentlyLoggedUser(office)}
                  className={classes.contact_email}
                /> */}
                <div
                  onClick={togglecurrentlyLoggedUser(office)}
                  className={classes.contact_email}
                ></div>
                {/* )} */}
              </ExpansionPanelSummary>
              <ExpansionPanelDetails style={{ display: "block" }}>
                <Text
                  size={15}
                  family="gillsans_light"
                  className={classes.contact_us_content}
                >
                  {get(office, "address", "")}{" "}
                  {get(office, "address1", "") && "-"}{" "}
                  {get(office, "address1", "")}
                </Text>
                <Text
                  size={15}
                  family="gillsans_light"
                  className={classes.contact_us_content}
                >
                  {get(office, "city", "")}, {get(office, "state", "")}
                  {""}
                  <span className={classes.padding_left_5}>
                    {get(office, "zip", "")}
                  </span>
                </Text>
                <Text
                  size={15}
                  family="gillsans_light"
                  className={classes.contact_us_content}
                >
                  {get(office, "phoneNumber", "")}
                </Text>
                <Text
                  size={15}
                  family="gillsans_light"
                  className={classes.contact_us_content}
                >
                  {get(office, "userName", "")}{" "}
                  {get(office, "userName", "") && " • "}
                  {get(office, "email", "")}
                </Text>
              </ExpansionPanelDetails>
            </ExpansionPanel>
          </div>
        );
      });
    }
  };

  // redirection to settings office locations page
  const redirectToOfficeLocations = (menuoption = "") => {
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let host = `${BASEURL.URL}settings/officeLocations`;
    window.location.href = `${host}/${token}/${companyId}`;
  };

  const copyText = () => {
    // navigator.clipboard.writeText(getPublicProfileLink());
    // return a promise
    // function copyToClipboard(textToCopy) {
    // navigator clipboard api needs a secure context (https)
    if (navigator.clipboard && window.isSecureContext) {
      // navigator clipboard api method'
      navigator.clipboard.writeText(getPublicProfileLink());
    } else {
      // text area method
      let textArea = document.createElement("textarea");
      textArea.value = getPublicProfileLink();
      // make the textarea out of viewport
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      new Promise((res, rej) => {
        // here the magic happens
        document.execCommand("copy") ? res() : rej();
        textArea.remove();
      });
    }
    // }F
    setClickedCopy(true);
    setTimeout(() => {
      setClickedCopy(false);
    }, 3000);
  };

  const togglecurrentlyLoggedUser = (officeData) => () => {
    setValues({
      ...values,
      currentlyLoggedUser: !currentlyLoggedUser,
      officeData: officeData,
    });
  };

  const getPreviewDate = (date) => {
    const NewDt = date ? new Date(date) : new Date();
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    }).format(NewDt);
  };

  const setSearchName = (e) => {
    const { name, value } = e.target;
    setValues({
      ...values,
      [name]: value,
    });
  };

  const handleSearchText = () => {
    dispatch(ProfileActions.getOpenPositionData({ search: searchText }));
  };

  const toggleJobPreviewModal = (jobData) => () => {
    if (!jobData) {
      return;
    }
    dispatch(ProfileActions.getJobData(jobData._id));
    setValues({
      ...values,
      openJobPreviewModal: !openJobPreviewModal,
    });
  };

  const closeJobPreviewModal = () => {
    setValues({
      ...values,
      openJobPreviewModal: false,
    });
  };

  const redirectToJpack = () => {
    const token = localStorage.getItem("tradeworks_user_token");
    let host = get(window, "location.host", "");
    let route = "";
    if (host) {
      if (host.includes("localhost")) {
        route = `${APP_URLS_JPACK.LOCAL}`;
      } else {
        if (host.includes("-dev-")) {
          route = `${APP_URLS_JPACK.DEV}`;
        }
        if (host.includes("-qa-")) {
          route = `${APP_URLS_JPACK.QA}`;
        }
        if (host.includes("-stage-")) {
          route = `${APP_URLS_JPACK.STAGE}`;
        } else {
          route = `${BASEURL.URL}jpack/`;
          // route = "http://twwstage.franklinreport.com/jpack/";
        }
      }
    }
    window.location.href = `${route}auth/${
      token || null
    }/${null}/${null}/false`;
  };

  const returnOpenPositionSection = () => {
    return (
      <>
        <div className={classes.input_happy_container}>
          <input
            placeholder="Job title, Company or Keywords"
            className={classes.open_pos_input}
            family="gillsans_r"
            name="searchText"
            style={{ fontSize: "16px" }}
            defaultValue={searchText}
            onBlur={setSearchName}
          />{" "}
          <img
            src="assets/icons/icon_happy_arrow.svg"
            className={classes.happy_icon}
            onClick={handleSearchText}
          />
        </div>
        <Row>
          <div className={`${classes.open_pos_left}`}>
            {/* <Text
              size={13}
              color={color.form_colors.royal_purple_1}
              fontWeight={600}
              family="gillsans_r"
            >
              New York |
              <span className={classes.london_paris}> London | Paris</span>
            </Text> */}
          </div>
          <div className={classes.open_pos_right}>
            <Text
              size={13}
              color={color.primary_palette.franklin_purple}
              family="gillsans_r"
            >
              <span className={classes.see_all} onClick={redirectToJpack}>
                See all Jobs
              </span>
            </Text>
          </div>{" "}
        </Row>
        <div className={classes.overflow_content}>
          {openPositions?.map((position, index) => {
            return (
              <Row className={classes.open_pos_row}>
                <div
                  className={classes.open_pos_left}
                  style={{ cursor: "pointer" }}
                  onClick={toggleJobPreviewModal(position)}
                >
                  <div className={classes.hoverUnderline}>
                    <Text
                      size={15}
                      color={color.form_colors.black}
                      family="gillsans_sb"
                      style={{ textTransform: "uppercase" }}
                      className={classes.hoverUnderline}
                    >
                      {get(position, "basicDetails.tradeName")}
                    </Text>
                    <Text
                      size={15}
                      color={color.form_colors.royal_purple_1}
                      fontWeight={600}
                      family="gillsans_sb"
                      style={{ cursor: "pointer" }}
                      className={classes.hoverUnderline}
                    >
                      {get(position, "basicDetails.jobTitle")}
                    </Text>
                  </div>
                  <Text
                    size={15}
                    color={color.primary_palette.black}
                    fontWeight={300}
                    family="gillsans_light"
                    className={classes.step1_subheading}
                  >
                    {get(position, "basicDetails.location.cityAndState")}
                  </Text>
                </div>
                <div className={classes.open_pos_right}>
                  <Text
                    size={13}
                    color={color.primary_palette.black}
                    fontWeight={300}
                    family="gillsans_light"
                  >
                    Posted
                  </Text>
                  <Text
                    size={13}
                    color={color.primary_palette.black}
                    fontWeight={300}
                    family="gillsans_light"
                    className={classes.step1_subheading}
                  >
                    {position.date}
                    {getPreviewDate(get(position, "createdAt"))}
                  </Text>
                </div>{" "}
              </Row>
            );
          })}
        </div>
      </>
    );
  };
  const sendFranklinRequest = () => {
    const profileUrl = getPublicProfileLink();
    dispatch(
      ProfileActions.sendEmailFranklinReport(
        { pageLink: profileUrl, companyId: localStorage.getItem("companyId") },
        () => {
          setValues({ ...values, franklinReq: true });
          dispatch(
            ProfileActions.getCompanyProfileData([PROFILE_TYPES.COMPANY_PITCH])
          );
        }
      )
    );
  };
  const onEditModeRadioClick = (checked) => () => {
    // setValues({
    //   ...values,
    //   isPublicView: checked,
    // });
    setIsPublic(checked);
    dispatch(
      ProfileActions.companyStatusUpdate(localStorage.getItem("companyId"), {
        status: checked,
      })
    );
  };
  const onEditModeChange = (e) => {
    // setValues({
    //   ...values,
    //   isPublicView: e.target.checked,
    // });
    setIsPublic(e.target.checked);
    dispatch(
      ProfileActions.companyStatusUpdate(localStorage.getItem("companyId"), {
        status: e.target.checked,
      })
    );
  };
  return (
    <div className={classes.right_section}>
      {localStorage.getItem("isInpersonate") === "true" && (
        <>
          <Row
            justify="space-between"
            align="center"
            className={classes.edit_mode_switch}
            style={{ width: pxToRem(250) }}
          >
            <Text
              family="gillsans_sb"
              size={20}
              color={color.primary_palette.franklin_purple}
              className={`${classes.cursorPointer} ${classes.hoverTxt}`}
            >
              Make Public:
            </Text>
            <Text
              family="gillsans_sb"
              size={18}
              color={color.primary_palette.black}
              onClick={onEditModeRadioClick(false)}
              className={`${classes.cursorPointer} ${classes.hoverTxt}`}
            >
              No
            </Text>
            <CustomToggleButton
              onChange={onEditModeChange}
              checked={isPublicView}
            />
            <Text
              family="gillsans_sb"
              size={18}
              color={color.primary_palette.pine_green}
              onClick={onEditModeRadioClick(true)}
              className={`${classes.cursorPointer} ${classes.hoverTxt}`}
            >
              Yes
            </Text>
          </Row>
        </>
      )}
      {!isPublicProfile && userRole !== "staff" && (
        <CustomButton
          className={classes.logo_btn_red}
          onClick={redirectToWpack}
          // style={{ position: "absolute" }}
        >
          + POST FREE JOB
        </CustomButton>
      )}
      <div className={`${classes.right_section_blk} ${classes.height_309}`}>
        <div className={`${classes.blk_content} ${classes.padding_res}`}>
          <Text
            size={25}
            fontWeight={600}
            color={color.primary_palette.franklin_purple}
            family="gillsans_sb"
            className={classes.right_blk_title}
          >
            Open Positions
          </Text>
          <div>{returnOpenPositionSection()}</div>
        </div>
      </div>
      {!isPublicProfile && userRole !== "staff" && (
        <div className={`${classes.right_section_blk} ${classes.height_171}`}>
          <div className={classes.blk_content}>
            <Text
              size={25}
              fontWeight={600}
              color={color.primary_palette.franklin_purple}
              family="gillsans_sb"
              className={`${classes.right_blk_title} ${classes.right_blk_title_top}`}
            >
              Franklin Report Card
            </Text>
            <div className={classes.franklinSection_data}>
              <img
                src={
                  values.franklinReq || companyPitch.isSentForReview
                    ? "assets/images/request_send_franklin.jpg"
                    : "assets/images/request_review.png"
                }
                className={`${!values.franklinReq && classes.cur_point} ${
                  classes.franklin_req
                }`}
                onClick={!values.franklinReq && sendFranklinRequest}
              />
              {companyPitch && companyPitch.isSentForReview && (
                <a onClick={sendFranklinRequest} className={classes.linkStyle}>
                  Request Again
                </a>
              )}
              {/* {returnFranklinReportSection()} */}
              {/* <div className={classes.textRight}>
              <CustomLink underline="always" className={classes.report_link}>
                <label>See Full Report</label>
              </CustomLink>
            </div> */}
            </div>
          </div>
        </div>
      )}
      <div className={`${classes.right_section_blk} ${classes.height_219}`}>
        <div className={classes.blk_content}>
          <Text
            size={25}
            fontWeight={600}
            color={color.primary_palette.franklin_purple}
            family="gillsans_sb"
            className={`${classes.right_blk_title} ${classes.right_blk_title_top}`}
          >
            Contact Us
            {!isPublicProfile && userRole !== "staff" && (
              // <img
              //   src="assets/icons/pencil.svg"
              //   className={classes.edit_company_pencil}
              //   style={{ position: "absolute", left: "242px", top: "22px" }}
              //   onClick={handleButtonClick(4)}
              // />
              <SvgPencil
                className={classes.edit_company_pencil}
                style={{ position: "absolute", left: "242px", top: "22px" }}
                onClick={redirectToOfficeLocations}
              />
            )}
          </Text>
          <div className={classes.contact_sec_wrap}>{returnContacts()}</div>
        </div>
      </div>
      {!isPublicProfile ? (
        <div className={`${classes.right_section_blk} ${classes.height_70}`}>
          <div className={classes.blk_content}>
            <Text
              size={25}
              fontWeight={600}
              color={color.primary_palette.franklin_purple}
              family="gillsans_sb"
              className={`${classes.right_blk_title} ${classes.right_blk_title_top}`}
            >
              Company Feeds
              {!isPublicProfile && userRole !== "staff" && (
                // <img
                //   src="assets/icons/pencil.svg"
                //   className={classes.edit_company_pencil}
                //   style={{ position: "absolute", left: "242px", top: "22px" }}
                //   onClick={handleButtonClick(2)}
                // />
                <SvgPencil
                  className={classes.edit_company_pencil}
                  style={{ position: "absolute", left: "242px", top: "22px" }}
                  onClick={handleButtonClick(2)}
                />
              )}
            </Text>
            <CompanyFeeds companyPitch={companyPitch ? companyPitch : {}} />
          </div>
        </div>
      ) : (
        Object.keys(get(companyPitch, "socialMediaFeeds", {})).length > 0 && (
          <div className={`${classes.right_section_blk} ${classes.height_70}`}>
            <div className={classes.blk_content}>
              <Text
                size={25}
                fontWeight={600}
                color={color.primary_palette.franklin_purple}
                family="gillsans_sb"
                className={`${classes.right_blk_title} ${classes.right_blk_title_top}`}
              >
                Company Feeds
                {!isPublicProfile && userRole !== "staff" && (
                  // <img
                  //   src="assets/icons/pencil.svg"
                  //   className={classes.edit_company_pencil}
                  //   style={{ position: "absolute", left: "242px", top: "22px" }}
                  //   onClick={handleButtonClick(2)}
                  // />
                  <SvgPencil
                    className={classes.edit_company_pencil}
                    style={{ position: "absolute", left: "242px", top: "22px" }}
                    onClick={handleButtonClick(2)}
                  />
                )}
              </Text>
              <CompanyFeeds companyPitch={companyPitch ? companyPitch : {}} />
            </div>
          </div>
        )
      )}
      {!isPublicProfile && userRole !== "staff" && (
        <div className={`${classes.right_section_blk} ${classes.height_70}`}>
          <div className={classes.blk_content}>
            <Text
              size={25}
              fontWeight={600}
              color={color.primary_palette.franklin_purple}
              family="gillsans_sb"
              className={`${classes.right_blk_title} ${classes.right_blk_title_top}`}
            >
              Your Public URL
              {/* {!isPublicProfile && (
                <img
                  src="assets/icons/pencil.svg"
                  className={classes.edit_company_pencil}
                  style={{ position: "absolute", left: "242px", top: "22px" }}
                  onClick={handleButtonClick(2)}
                />
              )} */}
            </Text>
            <Text className={classes.link}>
              <Row>
                <Row style={{ width: "320px" }}>
                  <a>{getPublicProfileLink()}</a>
                </Row>
                <Row style={{ width: "100px" }}>
                  <span onClick={copyText} className={classes.linkCopy}>
                    COPY
                  </span>
                  &nbsp;
                  {clickedCopy && (
                    <img
                      src="assets/icons/small_check_green.svg"
                      className={classes.greenCheck}
                    />
                  )}
                </Row>
              </Row>
            </Text>

            {/* {get(props, "displayFeeds", false) && ( */}

            {/* <CompanyFeeds companyPitch={companyPitch ? companyPitch : {}} /> */}
            {/* )} */}
          </div>
        </div>
      )}
      {isPublicProfile && userRole !== "staff" && (
        <Text
          size={16}
          color={color.form_colors.royal_purple_1}
          family="gillsans_sb"
          className={classes.pingAdmin}
          onClick={handlePingAdmin}
        >
          Ping the Administrator{" "}
          <img src="assets/images/ping_flag.png" className={classes.ping_img} />
        </Text>
      )}

      {currentlyLoggedUser && (
        <CurrentlyLoggedUser
          open={currentlyLoggedUser}
          onClose={togglecurrentlyLoggedUser()}
          officeData={values.officeData}
        />
      )}

      {openJobPreviewModal && (
        <JobPreview
          open={openJobPreviewModal}
          onClose={closeJobPreviewModal}
          // officeData={values.officeData}
        />
      )}
    </div>
  );
}
export default StyleSheet(RightSections);
