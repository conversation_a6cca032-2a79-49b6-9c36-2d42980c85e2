import React, { useState, useEffect } from "react";
import { map, get } from "lodash";
import Slider from "react-slick";
import { useSelector } from "react-redux";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import StyleSheet from "./styles/profile_container_styles";
import { color } from "../../utilities/themes";
import { useHistory } from "react-router-dom";
import SvgPencil from "../data_display/icons/Pencil";
import { TOKEN_KEY } from "../../constants";

function OurOffices(props) {
  const { classes } = props;
  const [values, setValues] = useState({
    activeLayoutIndex: 0,
    officeLayouts: [],
  });
  const officeshots = useSelector((state) => state.Profile.officeshots);
  const history = useHistory();
  const isPublicProfile = localStorage.getItem("publicPage") === "true";

  useEffect(() => {
    if (officeshots) {
      setValues({ ...values, officeLayouts: [...officeshots] });
    }
  }, [officeshots]);

  const getLayout = (index, layout) => {
    switch (index) {
      case 1:
        return (
          <Row className={classes.layout}>
            <div>
              <img
                src={
                  get(layout, "officeUrl[0]", "") ||
                  "assets/images/Office_01.png"
                }
                className={`${classes.l1_img1} ${classes.margin_r_9} ${classes.margin_l_9}`}
              />
            </div>
            <div>
              <img
                src={
                  get(layout, "officeUrl[1]", "") ||
                  "assets/images/Office_01.png"
                }
                className={classes.l1_img2}
              />
              <img
                src={
                  get(layout, "officeUrl[2]", "") ||
                  "assets/images/Office_03.png"
                }
                className={classes.l1_img3}
              />
            </div>
          </Row>
        );
      case 2:
        return (
          <Row className={classes.layout}>
            <div>
              <img
                src={
                  get(layout, "officeUrl[0]", "") ||
                  "assets/images/Office_03.png"
                }
                className={`${classes.l1_img2} ${classes.margin_l_9}`}
              />
              <img
                src={
                  get(layout, "officeUrl[1]", "") ||
                  "assets/images/Office_02.png"
                }
                className={`${classes.l1_img3} ${classes.margin_l_9}`}
              />
            </div>
            <div>
              <img
                src={
                  get(layout, "officeUrl[2]", "") ||
                  "assets/images/Office_01.png"
                }
                className={`${classes.l1_img1} ${classes.margin_l_9}`}
              />
            </div>
          </Row>
        );
      case 3:
        return (
          <Row className={classes.layout}>
            <img
              src={
                get(layout, "officeUrl[0]", "") || "assets/images/Office_03.png"
              }
              className={`${classes.margin_l_5} ${classes.l3_img}`}
            />
            <img
              src={
                get(layout, "officeUrl[1]", "") || "assets/images/Office_03.png"
              }
              className={`${classes.margin_l_5} ${classes.l3_img}`}
            />
            <img
              src={
                get(layout, "officeUrl[2]", "") || "assets/images/Office_03.png"
              }
              className={`${classes.margin_l_5} ${classes.l3_img}`}
            />
          </Row>
        );
      case 4:
        return (
          <Row className={classes.layout}>
            <img
              src={
                get(layout, "officeUrl[0]", "") || "assets/images/Office_03.png"
              }
              className={`${classes.l3_img} ${classes.margin_l_9}`}
            />
            <img
              src={
                get(layout, "officeUrl[1]", "") || "assets/images/Office_01.png"
              }
              className={`${classes.l1_img1} ${classes.margin_l_9}`}
            />
          </Row>
        );
      case 5:
        return (
          <Row className={classes.layout}>
            <img
              src={
                get(layout, "officeUrl[0]", "") || "assets/images/Office_01.png"
              }
              className={`${classes.l1_img1} ${classes.margin_l_9}`}
            />
            <img
              src={
                get(layout, "officeUrl[1]", "") || "assets/images/Office_03.png"
              }
              className={`${classes.l3_img} ${classes.margin_l_9}`}
            />
          </Row>
        );
      case 6:
        return (
          <Row className={classes.layout}>
            <img
              src={
                get(layout, "officeUrl[0]", "") || "assets/images/Office_03.png"
              }
              className={`${classes.l6_img} ${classes.margin_l_9}`}
            />
            <div>
              <img
                src={
                  get(layout, "officeUrl[1]", "") ||
                  "assets/images/Office_01.png"
                }
                className={`${classes.l6_img2} ${classes.margin_l_9}`}
              />
              <img
                src={
                  get(layout, "officeUrl[2]", "") ||
                  "assets/images/Office_03.png"
                }
                className={`${classes.l6_img2} ${classes.margin_l_9}`}
              />
            </div>
            <img
              src={
                get(layout, "officeUrl[3]", "") || "assets/images/Office_03.png"
              }
              className={`${classes.l6_img} ${classes.margin_l_9}`}
            />
          </Row>
        );

      default:
        break;
    }
  };
  const renderSlides = () => {
    const { officeLayouts } = values;
    return map(officeLayouts, (layout, index) => {
      let idx = index + 1 <= 6 ? index + 1 : index + 1 - 6;
      return getLayout(idx, layout);
    });
  };

  const beforeChange = (oldIdx, newIdx) => {
    setValues({ ...values, activeLayoutIndex: newIdx });
  };
  const handleButtonClick = (step) => (e) => {
    history.push({ pathname: "/wizard", state: { step, edit: true } });
  };
  // redirection to settings office locations page
  const redirectToOfficeLocations = (menuoption = "") => {
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let host = "http://twwstage.franklinreport.com/settings/officeLocations";
    window.location.href = `${host}/${token}/${companyId}`;
  };
  const { officeLayouts, activeLayoutIndex } = values;
  return (
    <div className={classes.our_offices_wrapper}>
      {/* {!isPublicProfile && get(officeLayouts, "length", 0) > 0 && ( */}
      {get(officeLayouts, "length", 0) === 0 && isPublicProfile ? (
        <></>
      ) : (
        <Text
          size={20}
          color={color.form_colors.royal_purple_1}
          family="gillsans_r"
          fontWeight={600}
          className={classes.title_underlined}
        >
          OUR OFFICES{" "}
          {!isPublicProfile && (
            // <img
            //   src="assets/icons/pencil.svg"
            //   className={classes.edit_company_pencil}
            //   onClick={handleButtonClick(4)}
            // />
            <SvgPencil
              className={classes.edit_company_pencil}
              onClick={redirectToOfficeLocations}
            />
          )}
        </Text>
      )}
      {/* )} */}
      {get(officeLayouts, "length", 0) > 0 && (
        <Row className={classes.carousel_tabs_wrapper}>
          {map(officeLayouts, (layout, index) => {
            return (
              <Text
                size={12}
                family="gillsans_r"
                fontWeight={activeLayoutIndex === index ? 600 : 300}
                className={classes.carousel_tab}
              >
                {layout.location}
              </Text>
            );
          })}
        </Row>
      )}
      <div className={classes.carousel_layout_wrapper}>
        <Slider
          dots
          beforeChange={beforeChange}
          infinite={true}
          speed={500}
          slidesToShow={1}
        >
          {renderSlides()}
        </Slider>
      </div>
      {get(officeLayouts, "length", 0) === 0 && !isPublicProfile && (
        <Row className={classes.layout}>
          <div>
            <img src={"assets/images/Shadow Our Office.jpg"} />
          </div>
        </Row>
      )}
    </div>
  );
}
export default StyleSheet(OurOffices);
