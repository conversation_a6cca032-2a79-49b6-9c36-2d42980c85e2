import React, { useState, useEffect } from "react";
import { useHistory } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { get, map, find, orderBy } from "lodash";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import StyleSheet from "./styles/profile_container_styles";
import RightSection from "./right_sections";
import AboutSection from "./about_section";
import OurCulture from "./our_culture";
import PingAdmin from "../modals/ping_admin_public";
import PingAdminSuccess from "../modals/pingAdminSuccessModal";
import CompanyNameChange from "../modals/companyNameChange";
import CompanyNameChangeSuccess from "../modals/companyNameChangeSuccess";
import Gamification from "../gamification/gamification";
import CustomButton from "../navigations/custom_buttons";

import { color, pxToRem } from "../../utilities/themes";
import { BASEURL, PROFILE_TYPES, TOKEN_KEY } from "../../constants";
import { ProfileActions, ConfigActions } from "../../redux/actions";
import PublicViewModal from "../modals/public_view_modal";
import SvgPencil from "../data_display/icons/Pencil";
import strings from "../../utilities/strings";
import moment from "moment";

function ProfileContainer(props) {
  const { classes } = props;
  const history = useHistory();
  const dispatch = useDispatch();
  const [interviewsRequested, setInterviewsReq] = useState([]);
  const [upcomingInterviews, setUpcomingInterviews] = useState([]);
  const [upcomingInterviewsData, setUpcomingData] = useState(null);
  const [userRole, setUserRole] = useState("admin");
  const [rescheduleRequestedData, setRescheduleRequest] = useState(null);
  const companyPitch =
    useSelector((state) => state.Profile.companypitch) || null;
  const businesscard = useSelector((state) => state.Profile.businesscard);
  const gamificationData = useSelector((state) => state.Profile.gamification);
  const interviewInfo = useSelector((state) => state.Profile.interviewInfo);
  const companyMembersList = useSelector(
    (state) => state.Profile.companyMembersList
  );
  const interviewBannersData = useSelector(
    (state) => state.Profile.interviewBannersData
  );
  const CompanyListByUser = useSelector(
    (state) => state.Profile.CompanyListByUser
  );
  const isPublicProfile = localStorage.getItem("publicPage") === "true";
  const isNewAdmin = localStorage.getItem("isNewAdmin") === "true";
  const defaultCollegeAllegiances = [
    {
      logo: "assets/images/Shadow School 1.jpg",
    },
    {
      logo: "assets/images/Shadow School 2.jpg",
    },
  ];

  const fetchData = (code) => {
    dispatch(ProfileActions.fetchAccolades());
    dispatch(
      ProfileActions.getCompanyBanners(localStorage.getItem("companyId"))
    );
    dispatch(ProfileActions.getCompanyMembersById());
    if (code) {
      dispatch(
        ProfileActions.getInstagramVerificationCode({ code }, (res) => {
          dispatch(ProfileActions.getInstaFeeds());
        })
      );
    }
    dispatch(
      ProfileActions.getCompanyProfileData([
        PROFILE_TYPES.COMPANY_PITCH,
        PROFILE_TYPES.WATER_COOLER,
        PROFILE_TYPES.COMPANY_HIGHLIGHTS,
        PROFILE_TYPES.BUSINESS_CARD,
        PROFILE_TYPES.OFFICE_SHOTS,
        PROFILE_TYPES.COMPANY_INFO,
      ])
    );
    dispatch(ProfileActions.getGamificationData());
    dispatch(ProfileActions.getInstaFeeds());
    dispatch(ConfigActions.getConfigs());
    dispatch(
      ProfileActions.getCompanyProfileData([PROFILE_TYPES.COMPANY_INFO])
    );
    dispatch(ProfileActions.getCompaniesListByUser());
    dispatch(ProfileActions.getProfileDataOfInfo("COMPANYINFO"));
    // dispatch(ProfileActions.fetchJobListings());
    dispatch(ProfileActions.fetchAllTrades());
    dispatch(ProfileActions.getInterviewInfo());
    dispatch(ProfileActions.fetchTopLocations({ name: "" }));
  };

  const fetchDataPublicView = () => {
    dispatch(ProfileActions.fetchAccolades());
    dispatch(ProfileActions.getGamificationData());
    dispatch(ProfileActions.getInstaFeeds());
    dispatch(
      ProfileActions.getProfileDataOf([
        PROFILE_TYPES.COMPANY_PITCH,
        PROFILE_TYPES.WATER_COOLER,
        PROFILE_TYPES.COMPANY_HIGHLIGHTS,
        PROFILE_TYPES.BUSINESS_CARD,
        PROFILE_TYPES.OFFICE_SHOTS,
      ])
    );
  };
  const filterCompany =
    CompanyListByUser &&
    CompanyListByUser.filter(
      (each) => each._id == localStorage.getItem("companyId")
    );
  useEffect(() => {
    const authCode = get(props, "location.search", "").substring(6);
    const publicProfileUserName = get(props, "match.params.username", "");
    if (publicProfileUserName) {
      localStorage.setItem("publicPage", true);
      localStorage.setItem("tradeworksurl", publicProfileUserName);
      fetchDataPublicView();
    } else {
      localStorage.setItem("publicPage", false);
      fetchData(authCode);
    }
  }, []);

  useEffect(() => {
    if (interviewBannersData) {
      getUpcomingInterview(interviewBannersData);
    }
  }, [interviewBannersData]);

  useEffect(() => {
    if (CompanyListByUser && CompanyListByUser.length) {
      const foundRecord = find(CompanyListByUser, {
        _id: localStorage.getItem("companyId"),
      });
      if (foundRecord && foundRecord.userRole === "") {
        setUserRole("staff");
      } else {
        setUserRole("admin");
      }
    }
  }, [CompanyListByUser, localStorage.getItem("companyId")]);

  const getUpcomingInterview = (interviewInfo) => {
    let scheduledInterviewArray = [];
    let requestedInterviewArray = [];
    interviewInfo.forEach((each) => {
      get(each, "sinterviews", []).forEach((interview) => {
        if (
          interview.isInterviewScheduled &&
          !interview.isInterviewRescheduled &&
          interview.date
        ) {
          let dateTime = `${moment(get(interview, "date")).format(
            "YYYY-MM-DD"
          )} ${moment(get(interview, "time"), ["HH.mm"]).format(
            "hh:mm a"
          )}  ${get(interview, "timezone", "")}`;
          scheduledInterviewArray.push({ obj: interview, dateTime: dateTime });
        }
      });
    });
    interviewInfo.forEach((each) => {
      get(each, "rinterviews", []).forEach((interview) => {
        if (interview.isInterviewRescheduledRequested) {
          requestedInterviewArray.push({ obj: interview });
        }
      });
    });
    let result = orderBy(
      scheduledInterviewArray,
      (dt) => {
        return new moment(dt.dateTime);
      },
      ["asc"]
    );
    let Rescheduleresult = requestedInterviewArray;
    if (result && result.length) {
      setUpcomingData([result[0]]);
    }
    if (Rescheduleresult && Rescheduleresult.length) {
      setRescheduleRequest([Rescheduleresult[0]]);
    }
  };

  useEffect(() => {
    if (gamificationData) {
      const data = {
        logo: find(gamificationData.mainSection, { activity: "Logo" }),
        profile: find(gamificationData.mainSection, {
          activity: "Cover Picture",
        }),
        cooler: find(gamificationData.mainSection, {
          activity: "Water Cooler (videos)",
        }),
        officeShot: find(gamificationData.mainSection, {
          activity: "Our Offices shots (pictures)",
        }),
        highlight: find(gamificationData.mainSection, {
          activity: "Our Culture-Highlights",
        }),
      };
      const gamificationPreview = {
        logo: get(data.logo, "currentlyPublished", false) > 0,
        profile: get(data.profile, "currentlyPublished", false) > 0,
        cooler: get(data.cooler, "currentlyPublished", false) >= 3,
        officeShot: get(data.officeShot, "currentlyPublished", false) >= 3,
        highlight: get(data.highlight, "currentlyPublished", false) >= 2,
      };
      setValues({ ...values, gamificationPreview });
    }
  }, [gamificationData]);

  useEffect(() => {
    if (businesscard) {
      const { businessCard } = businesscard;
      if (!businessCard) {
        return;
      }
      const headQuarter = find(businessCard, { isHeadOffice: true });
      setValues({
        ...values,
        offices: [...businessCard],
        profileName:
          localStorage.getItem("isInpersonate") === "true"
            ? get(CompanyListByUser, "[0].name", "") ||
              get(headQuarter, "companyName", "") ||
              get(companyPitch, "name", "")
            : get(headQuarter, "companyName", "") ||
              get(companyPitch, "name", ""),
        headQuarterEmail: get(headQuarter, "email", ""),
      });
    }
  }, [businesscard]);

  // To get Requested and Upcoming interviews list from the API response
  useEffect(() => {
    if (interviewInfo?.length > 0) {
      let reqResultArray = [],
        upcomingResultArray = [];
      map(interviewInfo, (item) => {
        map(item.interviewing, (subItem) => {
          if (subItem.isInterviewScheduled) {
            if (subItem.isInterviewAccept) {
              let upcomingList = {};
              upcomingList.interview = subItem;
              upcomingList.job = item.job;
              upcomingResultArray.push(upcomingList);
            } else {
              if (!subItem.isInterviewCancelled) {
                let interviwingList = {};
                interviwingList.interview = subItem;
                interviwingList.job = item.job;
                reqResultArray.push(interviwingList);
              }
            }
          }
        });
      });
      setInterviewsReq(reqResultArray);
      setUpcomingInterviews(upcomingResultArray);
    }
  }, [interviewInfo]);

  const [values, setValues] = useState({
    offices: [],
    showPingAdmin: false,
    showPingAdminSuccess: false,
    showGamification: false,
    showCompanyNameChange: false,
    showCompanyNameChangeSuccess: false,
  });
  const [displayError, setDisplayError] = useState(false);
  const [memberDetails, setMember] = useState(false);

  // To redirect to Ats
  const redirectToCats = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId =
      localStorage.getItem("newCompanyId") || localStorage.getItem("companyId");
    localStorage.removeItem("isSetNewCompany");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/cats";
      host = `${BASEURL.URL}cats`;
      window.location.href = `${host}/${token}/${companyId}`;
    }
  };

  const routeToProfile = () => {
    history.push("edit_profile");
  };

  const handleButtonClick = (step) => (e) => {
    history.push({ pathname: "/wizard", state: { step, edit: true } });
  };

  const redirectToProfile = (url) => () => {
    if (url) {
      window.open(`${BASEURL.URL}lpack/profile/public/${url}`, "_blank");
    }
  };

  const showErrorMessage = (member) => () => {
    setDisplayError(true);
    setMember(member);
    setTimeout(() => {
      setDisplayError(false);
    }, 3000);
  };

  const returnConnection = () => {
    // STATIC FOR NOW
    const currentTeam = get(companyMembersList, "members", []);

    return (
      <>
        <Row className={classes.wrapContent}>
          {map(currentTeam, (member, index) => {
            return (
              <div className={classes.connect_block}>
                <div className={classes.connect_image_wrap}>
                  <img
                    className={classes.connect_image}
                    src={
                      get(member, "profileImg.circle") ||
                      get(member, "profileUrl") ||
                      "assets/images/default_personal.png"
                    }
                    onClick={
                      member.existingLpackProfileExists
                        ? redirectToProfile(member.tradeWorkUrl)
                        : showErrorMessage
                    }
                  />
                </div>
                <Text
                  size={14}
                  color={color.primary_palette.black}
                  fontWeight={600}
                  family="gillsans_sb"
                  className={classes.connect_txt}
                  onClick={
                    member.existingLpackProfileExists
                      ? redirectToProfile(member.tradeWorkUrl)
                      : showErrorMessage(member)
                  }
                >
                  {get(member, "firstName", "")} {get(member, "lastName", "")}
                </Text>
                <Text
                  size={13}
                  color={color.form_colors.textfield_color}
                  className={classes.connect_txt}
                  fontWeight={300}
                  family="gillsans_light"
                >
                  {member.role}
                </Text>
              </div>
            );
          })}
        </Row>
        {displayError && (
          <Text
            size={14}
            color={color.primary_palette.franklin_purple}
            family="gillsans_sb"
            style={{ textAlign: "center", marginTop: "10px" }}
          >
            {get(memberDetails, "firstName", "")}{" "}
            {get(memberDetails, "lastName", "")} doesn't have a TW profile yet
          </Text>
        )}
      </>
    );
  };

  const returnAlumni = () => {
    // STATIC FOR NOW
    const alumniTeam = [
      { image: "", name: "Fred Nicolaus", role: "Editor" },
      { image: "", name: "Thomas Klay", role: "President" },
      { image: "", name: "Sabri Altel", role: "Vice president" },
      { image: "", name: "Gina Sanchez", role: "Project Manager" },
    ];
    return (
      <Row>
        {map(alumniTeam, (member, index) => {
          return (
            <div className={classes.connect_block}>
              <div className={classes.connect_image_wrap}>
                <img
                  className={classes.connect_image}
                  src={`assets/images/image${index + 1}.png`}
                />
              </div>
              <Text
                size={14}
                fontWeight={600}
                family="gillsans_sb"
                color={color.form_colors.black}
                className={classes.connect_txt}
              >
                {member.name}
              </Text>
              <Text
                size={13}
                fontWeight={300}
                family="gillsans_light"
                color={color.form_colors.textfield_color}
                className={classes.connect_txt}
              >
                {member.role}
              </Text>
            </div>
          );
        })}
      </Row>
    );
  };

  const returnTradesData = (trades) => {
    if (!trades) {
      return;
    }
    // LOOPING TRADES ARRAY AND RETURNING TRADES NAME STRING
    let trade = "";
    for (let i = 0; i < trades.length; i++) {
      trade += trades[i].name;
      if (i !== trades.length - 1) {
        trade += ", ";
      }
    }
    return trade;
  };

  const returnLocation = (offices) => {
    if (!offices) {
      return;
    }
    let location = "";
    for (let i = 0; i < offices.length; i++) {
      location += offices[i].city;
      if (offices.length > 1 && i !== offices.length - 1) {
        location += " | ";
      }
    }
    return location;
  };

  const autoScroll = (section) => () => {
    switch (section) {
      case "people":
        return window.scrollTo(0, 1000);
      case "offices":
        return window.scrollTo(0, 1400);
      case "culture":
        return window.scrollTo(0, 2000);
      case "connect":
        return window.scrollTo(0, 4000);
      default:
        break;
    }
  };
  const handlePingAdmin = () => {
    localStorage.setItem("isNewAdmin", false);
    setValues({ ...values, showPingAdmin: !values.showPingAdmin });
  };

  const redirectToLpack = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    let token = localStorage.getItem("tradeworks_user_token");
    if (host.includes("localhost")) {
      host = "localhost:3001";
      window.location.href = `http://${host}/lpack/profile/${token}`;
    } else {
      host = BASEURL.URL;
      window.location.href = `${host}lpack/lpack/profile/${token}`;
    }
  };

  const closeNewAdminModal = () => {
    localStorage.setItem("isNewAdmin", false);
    window.location.reload();
    setValues({ ...values });
  };

  const handleCompanyNameChange = () => {
    setValues({
      ...values,
      showCompanyNameChange: !values.showCompanyNameChange,
    });
  };

  const redirectToTeam = () => {
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let host = `${BASEURL.URL}settings/adminAccess`;
    // let host = "http://twwstage.franklinreport.com/settings/adminAccess";
    window.location.href = `${host}/${token}/${companyId}`;
  };

  const handleCompanyNameChangeSuccess = () => {
    setValues({
      ...values,
      showCompanyNameChangeSuccess: !values.showCompanyNameChangeSuccess,
    });
  };

  const handleGamification = () => {
    setValues({ ...values, showGamification: !values.showGamification });
  };

  const handleToggleModal = (closeModal, openModal) => {
    if (!openModal) {
      setValues({ ...values, [closeModal]: false });
      return;
    }
    setValues({ ...values, [openModal]: true, [closeModal]: false });
  };

  const formattedInterviewDate = (date) => {
    let originalDate = new Date(date);
    return originalDate.toLocaleDateString();
  };

  const formattedInterviewTime = (time) => {
    // Check correct time format and split into components
    if (!time) {
      return;
    }
    time = time
      .toString()
      .match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];
    if (time.length > 1) {
      // If time format correct
      time = time.slice(1); // Remove full string match value
      time[5] = +time[0] < 12 ? "AM" : "PM"; // Set AM/PM
      time[0] = +time[0] % 12 || 12; // Adjust hours
    }
    return time.join(""); // return adjusted time or original string
  };

  const renderRibbons = (ribbons) => {
    if (!ribbons) return null;
    return ribbons.split(",").map((ribbon, idx) => (
      <img
        key={ribbon.trim() + idx}
        src={`assets/images/${ribbon.trim()}.png`}
        alt={ribbon.trim()}
        title={ribbon.trim()}
        style={{
          width: 50,
          height: 50,
        }}
      />
    ));
  };

  const handleDisplayAboutSection = () => {
    if (!companyPitch) {
      return;
    }
    if (companyPitch) {
      const trades = get(companyPitch, "trades.length", 0);
      const companypitchinp = get(companyPitch, "companyPitch", false);
      const companySnapshot = get(companyPitch, "companySnapShot", false);
      return trades && companySnapshot && companypitchinp;
    }
    return false;
  };

  const handleDisplayFeedsSection = () => {
    if (!companyPitch) {
      return;
    }
    if (get(companyPitch, "length", 0) > 0) {
      const socialMediaData = get(companyPitch, "socialMediaFeeds", false);
      const twFeed = get(socialMediaData, "twitter", false);
      const fbFeed = get(socialMediaData, "facebook", false);
      return twFeed || fbFeed;
    }
    return false;
  };

  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let token = localStorage.getItem("tradeworks_user_token");
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}wpack`;
      // host = "twwstage.franklinreport.com/wpack";
      window.location.href = `${host}/${token}/${companyId}`;
    }
  };

  const {
    offices,
    showPingAdmin,
    showPingAdminSuccess,
    showCompanyNameChange,
    showCompanyNameChangeSuccess,
    showGamification,
    gamificationPreview,
    headQuarterEmail,
    profileName,
  } = values;
  const displayAboutSection = companyPitch
    ? handleDisplayAboutSection()
    : false;
  const displayFeedsSection = companyPitch
    ? handleDisplayFeedsSection()
    : false;
  return (
    <>
      {/* {!isPublicProfile && (
        <Row className={classes.edit_title_wrapper}>
          <Text
            size={18}
            color={color.secondary_palette.purples.ed_purple}
            family="gillsans_sb"
            fontWeight={600}
            className={classes.edit_title}
            onClick={routeToProfile}
          >
            EDIT COMPANY PROFILE
            <img
              src="assets/icons/pencil.svg"
              className={classes.edit_company_pencil}
            />
          </Text>
        </Row>
      )} */}
      <div>
        {/* {(interviewsRequested?.length > 0 ||
          upcomingInterviews?.length > 0) && (
          <div
            style={{
              paddingTop: "20px",
              paddingBottom: "50px",
              marginLeft: "240px",
            }}
          >
            <div>
              {interviewsRequested?.length > 0 && (
                <Text
                  size={14}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_sb"
                  className={classes.interviewTagText}
                >
                  <span className={classes.margin_right_10}>
                    {strings.interviewTags.requested}
                  </span>
                  <div style={{ display: "inline-block", marginRight: "10px" }}>
                    <Text
                      size={14}
                      color={color.primary_palette.christmas_red}
                      family="gillsans_sb"
                      className={classes.interviewsList}
                    >
                      <span>
                        {
                          interviewsRequested[0]?.job?.primaryPoints
                            ?.companyName
                        }
                      </span>
                      <span>
                        {`, ${formattedInterviewDate(
                          interviewsRequested[0]?.interview.date
                        )}`}
                      </span>
                      <span>
                        {moment(
                          interviewsRequested[0]?.interview.time,
                          "HH:mm"
                        ).format("hh:mm A")}
                      </span>
                    </Text>
                  </div>
                  <span
                    onClick={redirectToAts}
                    style={{ cursor: "pointer", textDecoration: "underline" }}
                  >
                    {strings.interviewTags.seeAll}
                  </span>
                </Text>
              )}
            </div>
            <div>
              {upcomingInterviews?.length > 0 && (
                <Text
                  size={14}
                  color={color.primary_palette.highlight_purple}
                  family="gillsans_sb"
                  className={classes.interviewTagText}
                >
                  <span className={classes.margin_right_10}>
                    {strings.interviewTags.upcoming}
                  </span>
                  <div style={{ display: "inline-block", marginRight: "10px" }}>
                    <Text
                      size={14}
                      color={color.primary_palette.highlight_purple}
                      family="gillsans_sb"
                      className={classes.interviewsList}
                    >
                      <span>
                        {upcomingInterviews[0]?.job?.primaryPoints?.companyName}
                      </span>
                      <span>
                        {`, ${formattedInterviewDate(
                          upcomingInterviews[0]?.interview.date
                        )}`}
                      </span>
                      <span>
                        {` ${formattedInterviewTime(
                          upcomingInterviews[0]?.interview.time
                        )}`}
                      </span>
                    </Text>
                  </div>
                  <span
                    onClick={redirectToAts}
                    style={{ cursor: "pointer", textDecoration: "underline" }}
                  >
                    {strings.interviewTags.seeAll}
                  </span>
                </Text>
              )}
            </div>
          </div>
        )} */}
        <div
          // className={classes.interviewsNotifications}
          style={{
            // paddingTop: "20px",
            paddingBottom: "50px",
            marginLeft: "100px",
          }}
        >
          {upcomingInterviewsData?.map((interview, idx) => (
            <Text
              key={idx}
              size={14}
              color={color.primary_palette.highlight_purple}
              family="gillsans_sb"
              className={classes.notificationTxt}
            >{`The next Interview is with ${get(
              interview,
              "obj.applicantName"
            )}, ${moment
              .utc(get(interview, "obj.date", ""))
              .format("MM/DD/YYYY")} at ${moment(
              get(interview, "obj.time", ""),
              "HH:mm"
            ).format("hh:mmA")} ${get(interview, "obj.timezone", "")} `}</Text>
          ))}
          {rescheduleRequestedData?.map((interview, idx) => (
            <Text
              key={idx}
              className={classes.notificationTxt}
              size={14}
              color={color.primary_palette.highlight_purple}
              family="gillsans_sb"
            >
              {interview &&
                `RESCHEDULING REQUESTED, ${get(
                  interview,
                  "obj.applicantName"
                )}, from ${moment
                  .utc(get(interview, "obj.date", ""))
                  .format("MM/DD/YYYY")} ${moment(
                  get(interview, "obj.time", ""),
                  "HH:mm"
                ).format("hh:mmA")} ${get(interview, "obj.timezone", "")} `}
              <span
                onClick={redirectToCats}
                style={{ cursor: "pointer", textDecoration: "underline" }}
                className={classes.hoverGreen}
              >
                {strings.interviewTags.seeAll}
              </span>
            </Text>
          ))}{" "}
        </div>
        {filterCompany &&
          filterCompany.length > 0 &&
          filterCompany[0].userRole !== "Admin" &&
          !window.location.href.includes("public-display") && (
            <>
              <div
                style={{
                  width: "600px",
                  margin: "0 auto",
                  textAlign: "center",
                  marginBottom: "10px",
                }}
              >
                <Text
                  size={14}
                  family="gillsans_sb"
                  color={color.primary_palette.franklin_purple}
                  // className={classes.current_mission}
                >
                  You are a Staff Member for this company. We invite you to
                  build your{" "}
                  <span
                    style={{ cursor: "pointer", textDecoration: "underline" }}
                    onClick={redirectToLpack}
                  >
                    Personal Online Profile
                  </span>
                  .<br /> As a Staff Member, you can Find & Manage Applicants,
                  but you cannot change the Company Profile. To request an
                  upgrade to Admin, ping{" "}
                  <span
                    style={{
                      cursor: "pointer",
                      textDecoration: "underline",
                      textTransform: "capitalize",
                    }}
                    onClick={handlePingAdmin}
                  >
                    {" "}
                    {get(
                      filterCompany,
                      "[0].companyAdmins[0].firstName",
                      ""
                    )}{" "}
                    {get(filterCompany, "[0].companyAdmins[0].lastName", "")}
                  </span>
                  .
                </Text>
              </div>
            </>
          )}
        <div
          className={`${isPublicProfile && classes.public_profile_container} ${
            classes.container
          }`}
        >
          {/* COVER IMAGE */}
          {/*  */}
          {!isPublicProfile && (
            <div className={classes.gamification_banner_text}>
              <Text
                size={14}
                family="gillsans_sb"
                color={color.primary_palette.black}
                className={classes.current_mission}
              >
                Current Mission
              </Text>
              <Row className={classes.gamification_banner}>
                <div className={classes.pos_relative}>
                  <img
                    src="assets/images/Scroll.png"
                    className={classes.gamification_banner_img}
                    onClick={handleGamification}
                  />
                  <div
                    className={classes.gamification_innertext}
                    onClick={handleGamification}
                  >
                    <Text family="gillsans_sb" size={12}>
                      Company Profile
                    </Text>
                    <Text
                      size={26}
                      className={classes.percentage}
                      style={{ fontWeight: "bold", marginTop: pxToRem(7) }}
                    >
                      {get(gamificationData, "precentage", 10)}%
                    </Text>
                    <Text family="gillsans_sb" size={12}>
                      Complete
                    </Text>
                  </div>
                </div>
                <div className={classes.masked_bck}>
                  <Row className={classes.pearl_row}>
                    <div className={classes.peral_spacing}>
                      <div
                        className={classes.peral_img}
                        onClick={handleButtonClick(2)}
                      >
                        {/* {_.get(gamificationData, "experience", "")} */}
                        {get(gamificationPreview, "logo", false) && (
                          <img src="assets/icons/Green Checkmark.svg" />
                        )}
                      </div>
                      <Text className={classes.gamification_section_label}>
                        Logo
                      </Text>
                    </div>
                    <div className={classes.peral_spacing}>
                      <div
                        className={classes.peral_img}
                        onClick={handleButtonClick(2)}
                      >
                        {get(gamificationPreview, "profile", false) && (
                          <img src="assets/icons/Green Checkmark.svg" />
                        )}
                      </div>
                      <Text className={classes.gamification_section_label}>
                        Profile
                      </Text>
                    </div>
                    <div className={classes.peral_spacing}>
                      <div
                        className={classes.peral_img}
                        onClick={handleButtonClick(3)}
                      >
                        {get(gamificationPreview, "cooler", false) && (
                          <img src="assets/icons/Green Checkmark.svg" />
                        )}
                        {/* {_.get(gamificationData, "education", "")} */}
                      </div>
                      <Text className={classes.gamification_section_label}>
                        People
                      </Text>
                    </div>
                  </Row>
                  <Row className={classes.pearl_row}>
                    <div className={classes.peral_spacing}>
                      <div
                        className={classes.peral_img}
                        onClick={handleButtonClick(4)}
                      >
                        {get(gamificationPreview, "officeShot", false) && (
                          <img src="assets/icons/Green Checkmark.svg" />
                        )}
                      </div>
                      <Text className={classes.gamification_section_label}>
                        Office
                      </Text>
                    </div>
                    <div className={classes.peral_spacing}>
                      <div
                        className={classes.peral_img}
                        onClick={handleButtonClick(5)}
                      >
                        {get(gamificationPreview, "highlight", false) && (
                          <img src="assets/icons/Green Checkmark.svg" />
                        )}
                      </div>
                      <Text className={classes.gamification_section_label}>
                        Highlights
                      </Text>
                    </div>
                    <div className={classes.peral_spacing}>
                      <div
                        className={classes.peral_img}
                        onClick={handleButtonClick(5)}
                      >
                        {/* {_.get(gamificationData, "image", "")} */}
                        <img src="assets/icons/Green Checkmark.svg" />
                      </div>
                      <Text className={classes.gamification_section_label}>
                        Team
                      </Text>
                    </div>
                  </Row>
                </div>
              </Row>
            </div>
          )}

          <div className={classes.cover_container}>
            <img
              className={classes.cover_image}
              src={
                companyPitch
                  ? get(
                      companyPitch,
                      "coverImage",
                      "assets/images/Starry Sky - Blank Cover.png"
                    )
                  : "assets/images/Starry Sky - Blank Cover.png"
              }
            />
            {!isPublicProfile && userRole !== "staff" && (
              // <img
              //   src="assets/images/pencil_icon.png"
              //   className={classes.edit_company_pencil_round}
              //   style={{ position: "absolute", right: "10px", top: "224px" }}
              //   onClick={handleButtonClick(2)}
              // />
              <SvgPencil
                className={classes.edit_company_pencil_round}
                style={{ position: "absolute", right: "10px", top: "224px" }}
                onClick={handleButtonClick(2)}
              />
            )}
          </div>

          {/* TITLE AND LOCATION SECTION */}

          <Row className={classes.title_section}>
            {isPublicProfile && !get(companyPitch, "companyLogo") ? (
              <div style={{ marginLeft: "20px" }}></div>
            ) : (
              <div className={classes.profile_img_wrapper}>
                <img
                  className={classes.profile_image}
                  src={
                    companyPitch
                      ? get(
                          companyPitch,
                          "companyLogo",
                          "assets/images/Shadow TW Logo.svg"
                        )
                      : "assets/images/Shadow TW Logo.svg"
                  }
                />
                {!isPublicProfile && userRole !== "staff" && (
                  // <img
                  //   src="assets/images/pencil_icon.png"
                  //   className={classes.edit_company_pencil_round}
                  //   onClick={handleButtonClick(2)}
                  //   style={{ position: "absolute", right: "20px", top: "30px" }}
                  // />
                  <SvgPencil
                    className={classes.edit_company_pencil_round}
                    style={{
                      position: "absolute",
                      right: "20px",
                      top: "30px",
                    }}
                    onClick={handleButtonClick(2)}
                  />
                )}
              </div>
            )}
            <Row className={classes.title_wrapper}>
              <div className={classes.title_subtitle}>
                <Text
                  size={30}
                  color={color.form_colors.royal_purple_1}
                  family="gillsans_sb"
                  fontWeight={600}
                  style={{ display: "flex" }}
                >
                  {get(values, "profileName", "")}
                  {!isPublicProfile && userRole !== "staff" && (
                    // <img
                    //   src="assets/icons/pencil.svg"
                    //   className={classes.edit_company_pencil}
                    //   onClick={() => {
                    //     handleCompanyNameChange();
                    //   }}
                    // />
                    <SvgPencil
                      className={classes.edit_company_pencil}
                      onClick={() => {
                        handleCompanyNameChange();
                      }}
                    />
                  )}
                  <div style={{ marginLeft: pxToRem(20) }}>
                    {renderRibbons(get(companyPitch, "ribbons", ""))}
                  </div>
                </Text>
                <Text
                  size={18}
                  color={color.form_colors.textfield_color}
                  family="gillsans_light"
                  fontWeight={300}
                >
                  {companyPitch && get(companyPitch, "companySnapShot", "")}
                  {!isPublicProfile && userRole !== "staff" && (
                    // <img
                    //   src="assets/icons/pencil.svg"
                    //   className={classes.edit_company_pencil}
                    //   onClick={handleButtonClick(2)}
                    // />
                    <SvgPencil
                      className={classes.edit_company_pencil}
                      onClick={handleButtonClick(2)}
                    />
                  )}
                </Text>
              </div>
              {!isPublicProfile && userRole !== "staff" && (
                <div className={classes.title_location}>
                  <Text
                    size={15}
                    fontWeight={300}
                    color={color.primary_palette.black}
                    family="gillsans_light"
                    className={classes.title_location_text}
                  >
                    {companyPitch &&
                      returnTradesData(get(companyPitch, "trades", []))}
                  </Text>
                  <Text
                    size={15}
                    fontWeight={300}
                    color={color.primary_palette.black}
                    family="gillsans_light"
                    className={classes.title_location_text}
                  >
                    {returnLocation(offices)}
                  </Text>
                </div>
              )}
            </Row>
          </Row>

          {/* {!isPublicProfile && userRole !== "staff" && (
            <CustomButton
              className={classes.logo_btn_red}
              onClick={redirectToWpack}
              style={{ position: "absolute" }}
            >
              + POST FREE JOB
            </CustomButton>
          )} */}

          {/* MIDDLE SECTION */}
          <div
            className={classes.middle_section_wrapper}
            style={{ marginTop: "20px" }}
          >
            <Row>
              {
                <AboutSection
                  autoScroll={autoScroll}
                  displayContent={displayAboutSection}
                  userRole={userRole}
                  accolades={
                    companyPitch
                      ? get(companyPitch, "companyAccolades", [])
                      : []
                  }
                  companyPitch={companyPitch ? companyPitch : {}}
                />
              }
              {/* {!isPublicProfile && ( */}
              <RightSection
                handlePingAdmin={handlePingAdmin}
                displayFeeds={displayFeedsSection}
                companyPitch={companyPitch ? companyPitch : {}}
                isPublicProfile={isPublicProfile}
                userRole={userRole}
              />
              {/* )} */}
            </Row>
          </div>

          {/* COMPANY VIBES */}
          {get(companyPitch, "length", 0) > 0 && (
            <div className={classes.company_vibe_wrapper}>
              <Text
                size={20}
                color={color.secondary_palette.purples.franklin_purple}
                family="gillsans_r"
                className={classes.company_vibe_title}
              >
                COMPANY VIBE
              </Text>
              <div className={classes.company_vibe_content}>
                {get(companyPitch, "length", 0) > 0 &&
                  map(companyPitch.companyVibe, (vibe) => {
                    return (
                      <Text
                        size={20}
                        color={color.secondary_palette.grays.sonic_silver}
                        family="gillsans_sb"
                        fontWeight={600}
                        className={classes.company_vibes}
                      >
                        {get(vibe, "name", "")}
                      </Text>
                    );
                  })}
              </div>
            </div>
          )}

          {/* LAST SECTION */}
          <div className={classes.last_section_wrapper}>
            {/* OUR CULTURE */}
            <OurCulture userRole={userRole} />
            {/* CONNECT */}
            {/* {!isPublicProfile && ( */}
            {get(companyMembersList, "members.length", []) > 0 && (
              <div className={classes.connect_section_wrapper}>
                <Row
                  className={classes.title_underlined}
                  style={{ justifyContent: "space-between" }}
                >
                  <Text
                    size={20}
                    color={color.form_colors.royal_purple_1}
                    family="gillsans_r"
                    fontWeight={600}
                  >
                    CONNECT{"  "}
                  </Text>
                  {!isPublicProfile && userRole !== "staff" && (
                    <Text
                      size={18}
                      color={color.primary_palette.black}
                      family="gillsans_r"
                      style={{ display: "inline" }}
                    >
                      To add or delete{" "}
                      <span
                        className={classes.teamMemberStyle}
                        onClick={redirectToTeam}
                      >
                        Team Members
                      </span>
                    </Text>
                  )}
                </Row>

                {/* <img
                src="assets/icons/pencil.svg"
                className={classes.edit_company_pencil}
                onClick={handleButtonClick(2)}
              /> */}
                {/* <Text
                  size={15}
                  color={color.form_colors.royal_purple_1}
                  family="gillsans_sb"
                >
                  FEATURE COMING SOON
                </Text> */}
                {/* <img
                src="assets/icons/pencil.svg"
                className={classes.edit_company_pencil}
                onClick={handleButtonClick(2)}
              /> */}
                <div className={classes.connect_content}>
                  <div className={classes.current_team_wrap}>
                    <Text
                      size={18}
                      color={color.primary_palette.franklin_purple}
                      family="gillsans_sb"
                      fontWeight={600}
                    >
                      Current Team
                    </Text>
                    <div className={classes.img_block}>
                      {returnConnection()}
                    </div>
                  </div>
                  {/* <div className={classes.alumni_team_wrap}>
                <Text
                  size={18}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_sb"
                  fontWeight={600}
                >
                  Ames Adams Architects Alumni
                </Text>
                <div className={classes.img_block}>{returnAlumni()}</div>
              </div> */}
                </div>
              </div>
            )}
            {/* )} */}
            <div className={classes.connect_section_wrapper}>
              {get(companyPitch, "collegeAllegiances.length") === 0 &&
              isPublicProfile ? (
                <></>
              ) : (
                <Text
                  size={20}
                  color={color.form_colors.royal_purple_1}
                  family="gillsans_r"
                  fontWeight={600}
                  className={classes.title_underlined}
                >
                  {!companyPitch?.isPublicDisplay && isPublicProfile
                    ? ""
                    : "SCHOOL AFFILIATIONS"}
                  {!isPublicProfile && userRole !== "staff" && (
                    // <img
                    //   src="assets/icons/pencil.svg"
                    //   className={classes.edit_company_pencil}
                    //   onClick={handleButtonClick(2)}
                    // />
                    <SvgPencil
                      className={classes.edit_company_pencil}
                      onClick={handleButtonClick(2)}
                    />
                  )}
                  {!get(companyPitch, "isPublicDisplay") &&
                    !isPublicProfile &&
                    userRole !== "staff" && (
                      <>
                        {get(companyPitch, "collegeAllegiances.length") !==
                        0 ? (
                          <Text
                            size={16}
                            color={color.secondary_palette.grays.shadow_gray}
                            family="gillsans_r"
                            className={classes.pitchTxtAlign}
                          >
                            Schools will Not Show in Public Company Profile as
                            Requested
                          </Text>
                        ) : (
                          <Text
                            size={16}
                            color={color.primary_palette.black}
                            family="gillsans_r"
                            className={classes.pitchTxtAlign}
                            style={{ float: "right" }}
                          >
                            Chose Schools to build & foster Relationships
                          </Text>
                        )}
                      </>
                    )}
                </Text>
              )}
              {get(companyPitch, "collegeAllegiances.length") !== 0 ? (
                <Row style={{ flexWrap: "wrap" }}>
                  {!companyPitch?.isPublicDisplay && isPublicProfile ? (
                    <></>
                  ) : (
                    map(
                      companyPitch && companyPitch.collegeAllegiances,
                      (college) => {
                        return (
                          <div
                            className={
                              college.logo && classes.connect_block_college
                            }
                          >
                            {college.logo && (
                              <div className={classes.connect_image_wrap}>
                                <img
                                  className={classes.connect_image_college}
                                  src={college.logo}
                                />
                                <Text
                                  size={14}
                                  color={color.primary_palette.black}
                                  fontWeight={600}
                                  family="gillsans_sb"
                                  className={classes.connect_txt}
                                >
                                  {get(college, "name", "")}
                                </Text>
                              </div>
                            )}
                          </div>
                        );
                      }
                    )
                  )}
                </Row>
              ) : (
                <Row style={{ flexWrap: "wrap" }}>
                  {!isPublicProfile &&
                    userRole !== "staff" &&
                    map(defaultCollegeAllegiances, (college) => {
                      return (
                        <div
                          className={
                            college.logo && classes.connect_block_college
                          }
                        >
                          {college.logo && (
                            <div className={classes.connect_image_wrap}>
                              <img
                                className={classes.connect_image_college}
                                src={college.logo}
                              />
                              <Text
                                size={14}
                                color={color.primary_palette.black}
                                fontWeight={600}
                                family="gillsans_sb"
                                className={classes.connect_txt}
                              >
                                {get(college, "name", "")}
                              </Text>
                            </div>
                          )}
                        </div>
                      );
                    })}
                </Row>
              )}
            </div>
          </div>
          {showPingAdmin && (
            <PingAdmin
              handlePingAdmin={handlePingAdmin}
              handleToggleModal={handleToggleModal}
              adminEmail={headQuarterEmail}
            />
          )}
          {showPingAdminSuccess && (
            <PingAdminSuccess
              handleToggleModal={handleToggleModal}
              type="public"
            />
          )}
          {showGamification && (
            <Gamification
              showGamification={showGamification}
              handleGamification={handleGamification}
              onClick={handleGamification}
            />
          )}
          {showCompanyNameChange && (
            <CompanyNameChange
              companyName={profileName}
              handleCompanyNameChange={handleCompanyNameChange}
              handleToggleModal={handleToggleModal}
            />
          )}
          {showCompanyNameChangeSuccess && (
            <CompanyNameChangeSuccess
              handleCompanyNameChangeSuccess={handleCompanyNameChangeSuccess}
            />
          )}
        </div>
        {/* commented below footerAlign div to replace with common footer component */}

        {/* <div className={classes.footerAlign}>
          <Text size={12} family="avenir_light" color={color.big_tar}>
            If you have any questions, please contact Customer Service at
          </Text>
          <Text size={12} family="avenir_light" color={color.big_tar}>
            866.960.9100 <NAME_EMAIL>
          </Text>
          <div>
            <img
              src="assets/images/tw_logo.png"
              style={{ height: "33px", display: "inline-block" }}
            />
            <Text
              size={12}
              family="gillsans_sb"
              color={color.primary_palette.franklin_purple}
              style={{
                display: "inline-block",
                position: "relative",
                top: "-8px",
              }}
            >
              TradeWorks LLC
            </Text>
          </div>
        </div> */}
      </div>
      {isNewAdmin && (
        <PublicViewModal
          handlePingAdmin={handlePingAdmin}
          onCloseClick={closeNewAdminModal}
        />
      )}
    </>
  );
}
export default StyleSheet(ProfileContainer);
