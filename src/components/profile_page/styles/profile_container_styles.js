import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  franklin_req: {
    width: "100%",
  },
  edit_title_wrapper: {
    width: pxToRem(1075),
    margin: "0 auto",
  },
  interviewsNotifications: {
    minHeight: pxToRem(50),
    padding: "6px 0 6px 35px",
    boxSizing: "border-box",
  },
  see_all: {
    cursor: "pointer",
  },
  connect_txt: {
    textTransform: "capitalize",
    cursor: "pointer",
    "&:hover": {
      color: color.warpstone_glow,
    },
  },
  edit_company_pencil: {
    // height: pxToRem(16),
    // width: pxToRem(16),
    cursor: "pointer",
    paddingLeft: pxToRem(8),
  },
  edit_company_pencil_round: {
    border: "0.5px solid #4a4a4a",
    position: "absolute",
    bottom: "5px",
    top: "150px",
    right: "-18px",
    borderRadius: "50%",
    width: "25px",
    height: "25px",
    textAlign: "center",
    paddingLeft: "6px",
    paddingTop: "6px",
    background: "white",
    cursor: "pointer",
    "&:hover": {
      backgroundColor: color.primary_palette.white,
      "& g": {
        "& path": {
          fill: "#5E94E0",
        },
        "& g": {
          "& path": {
            fill: "rgb(64, 64, 65) !important",
          },
        },
      },
    },
  },
  edit_title: {
    textDecoration: "underline",
    textAlign: "end",
    cursor: "pointer",
    width: "100%",
    margin: "0 auto",
  },
  container: {
    border: `solid ${pxToRem(2)} ${color.primary_palette.black}`,
    // margin: `${pxToRem(5)} ${pxToRem(136)}`,
    width: pxToRem(1024),
    margin: "0 auto",
    position: "relative",
  },
  footerAlign: {
    width: pxToRem(1024),
    margin: "0 auto",
    textAlign: "center",
    marginTop: "17px",
    marginBottom: "20px",
  },
  cover_container: {
    height: pxToRem(250),
  },
  cover_image: {
    width: "100%",
    height: "100%",
  },
  title_section: {
    marginTop: pxToRem(20),
    minHeight: pxToRem(50),
  },
  profile_img_wrapper: {
    width: "15%",
    position: "relative",
  },
  logo_btn_red: {
    background: `${color.primary_palette.pine_green}`,
    borderRadius: pxToRem(27),
    fontSize: pxToRem(14),
    left: pxToRem(60),
    marginBottom: pxToRem(16),
    marginTop: pxToRem(16),
    ...tradework_theme.typography.styles.gillsans_bold,
    color: `${color.primary_palette.white} !important`,
    textTransform: "uppercase",
    padding: `${pxToRem(1)} ${pxToRem(20)}`,
    // height: pxToRem(25),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
      verticalAlign: "middle",
      marginRight: pxToRem(10),
    },
    "& .MuiButton-label": {
      fontWeight: "bold",
    },
    "&:hover": {
      color: `${color.primary_palette.christmas_red} !important`,
      backgroundColor: "#FFFFFF !important",
      border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    },
  },
  profile_image: {
    width: pxToRem(123),
    height: pxToRem(123),
    border: `${pxToRem(1)} solid ${color.primary_palette.black}`,
    borderRadius: "50%",
    position: "absolute",
    top: pxToRem(-70),
    left: pxToRem(20),
  },
  title_wrapper: {
    width: "85%",
  },
  title_subtitle: {
    width: "70%",
  },
  title_location: {
    width: "30%",
    textAlign: "right",
    marginRight: pxToRem(33),
    marginTop: pxToRem(5),
  },
  title_location_text: {
    lineHeight: "1.67",
  },
  middle_section_wrapper: {
    margin: pxToRem(30),
  },
  last_section_wrapper: {
    margin: pxToRem(30),
    marginTop: pxToRem(16),
  },

  left_section: {
    width: "70%",
    // border: `${pxToRem(1)} solid ${color.primary_palette.black}`,
    marginRight: pxToRem(5),
  },
  right_section: {
    width: "30%",
  },
  right_section_blk: {
    minHeight: pxToRem(200),
    border: `${pxToRem(1)} solid ${color.primary_palette.black}`,
    width: "100%",
    marginBottom: pxToRem(32),
  },
  blk_content: {
    padding: pxToRem(5),
    position: "relative",
  },
  padding_res: {
    padding: `${pxToRem(0)} ${pxToRem(5)}`,
  },
  right_blk_title: {
    marginLeft: pxToRem(10),
    display: "inline-block",
  },
  right_blk_title_top: {
    position: "absolute",
    background: "white",
    top: pxToRem(-15),
    padding: `${pxToRem(0)} ${pxToRem(5)}`,
  },
  company_vibe_wrapper: {
    width: "100%",
  },
  company_vibe_title: {
    textAlign: "center",
  },
  company_vibe_content: {
    margin: "0 auto",
    width: "50%",
    textAlign: "center",
  },
  company_vibes: {
    display: "inline-block",
    padding: `${pxToRem(0)} ${pxToRem(15)}`,
  },
  tab_name: {
    width: "30%",
    textAlign: "center",
  },
  cur_point: {
    cursor: "pointer",
  },
  tab_highlight: {
    borderBottom: `${pxToRem(2)} solid ${
      color.primary_palette.franklin_purple
    }`,
  },
  about_section_row: { width: "100%", marginTop: pxToRem(20) },
  about_img_sec: { width: "45%", position: "relative" },
  about_desc_sec: {
    width: "55%",
    padding: `${pxToRem(0)} ${pxToRem(20)} ${pxToRem(0)} ${pxToRem(40)}`,
    lineHeight: "1.47",
  },
  about_desc_content: {
    whiteSpace: "pre-line",
    wordBreak: "break-word",
  },
  company_keywords_row: {
    marginTop: pxToRem(5),
    flexWrap: "wrap",
  },
  company_keywords: {
    marginTop: pxToRem(20),
  },
  about_accolade_container: {
    margin: `${pxToRem(20)} ${pxToRem(0)}`,
    display: "flex",
    width: "330px",
    flexWrap: "wrap",
  },
  about_accolade_img: {
    height: pxToRem(50),
    width: pxToRem(48),
    padding: `${pxToRem(0)} ${pxToRem(10)}`,
  },
  accolade_row: {
    borderBottom: `${pxToRem(1)} solid ${
      color.secondary_palette.grays.cape_hope
    }`,
  },
  keyword_name: {
    background: color.form_colors.chip_color,
    padding: `${pxToRem(2)} ${pxToRem(5)}`,
    borderRadius: pxToRem(8),
    margin: `${pxToRem(3)} ${pxToRem(5)}`,
    "&:first-child": {
      marginLeft: pxToRem(0),
    },
  },
  keyval_row: { marginTop: pxToRem(5) },
  about_keyvalue_container: {
    width: "50%",
  },
  about_keyvalue: {
    display: "inline-block",
  },
  p_l_5: { paddingLeft: pxToRem(5) },
  p_t_5: { paddingTop: pxToRem(5) },
  about_img: { width: "100%", height: pxToRem(228) },
  title_underlined: {
    width: "96%",
    paddingBottom: pxToRem(2),
    borderBottom: `${pxToRem(1)} solid ${
      color.secondary_palette.grays.cape_hope
    }`,
  },
  water_cooler_wrapper: {
    marginTop: pxToRem(16),
    marginRight: pxToRem(20),
    width: "100%",
  },
  our_offices_wrapper: {
    marginTop: pxToRem(16),
  },
  carousel_tabs_wrapper: {
    justifyContent: "flex-end",
    marginTop: pxToRem(5),
    width: "96%",
  },
  carousel_layout_wrapper: {
    marginTop: pxToRem(20),
    width: "88%",
    margin: "0 auto",
    "& .slick-dots": {
      display: "none !important",
    },
    "& .slick-next": {
      "&:before": {
        content: "url(assets/images/carousel_arrow.png)",
        transform: "translateY(-30px)",
        display: " inline-block",
      },
    },
    "& .slick-prev": {
      "&:before": {
        content: "url(assets/images/carousel_arrow.png)",
        transform: "rotate(180deg) translateY(30px)",
        display: " inline-block",
        width: pxToRem(25),
      },
    },
  },
  carousel_tab: {
    padding: `${pxToRem(0)} ${pxToRem(5)}`,
  },
  layout: { display: "flex !important", justifyContent: "center" },
  margin_r_9: {
    marginRight: pxToRem(9),
  },
  margin_l_9: {
    marginLeft: pxToRem(9),
  },
  margin_l_5: {
    marginLeft: pxToRem(5),
  },
  l1_img1: {
    width: pxToRem(340),
    height: pxToRem(229),
  },
  l1_img2: {
    width: pxToRem(228),
    height: pxToRem(117),
    marginBottom: pxToRem(6),
  },
  l1_img3: { width: pxToRem(228), height: pxToRem(107) },
  l6_img2: {
    width: pxToRem(200),
    height: pxToRem(111),
    marginBottom: pxToRem(6),
  },
  l3_img: { width: pxToRem(192), height: pxToRem(229) },
  l6_img: { width: pxToRem(182), height: pxToRem(229) },

  water_cooler_block: {
    width: "33.33%",
    display: "inline-block",
  },
  water_cooler_content: {
    display: "block",
    textAlign: "center",
    margin: "0 auto",
    width: pxToRem(209),
    marginTop: pxToRem(30),
  },
  water_cooler_p2: {
    marginTop: pxToRem(10),
  },
  pos_rel: {
    position: "relative",
  },
  circle_img_border: {
    width: pxToRem(138),
    height: pxToRem(138),
    borderRadius: "50%",
    border: `solid ${pxToRem(2)} ${color.primary_palette.black}`,
  },
  circle_img: {
    width: pxToRem(138),
    height: pxToRem(138),
    borderRadius: "50%",
  },
  player_icon: {
    position: "absolute",
    bottom: pxToRem(10),
    right: pxToRem(32),
    height: pxToRem(35),
    width: pxToRem(35),
    borderRadius: "100%",
    cursor: "pointer",
  },
  show_more_wrap: {
    textAlign: "center",
    marginTop: pxToRem(15),
    cursor: "pointer",
  },
  showmore_add_icon: {
    fontSize: pxToRem(16),
    marginBottom: pxToRem(-3),
    "& path": {
      "&:nth-child(1)": {
        fill: color.secondary_palette.grays.background_gray,
      },
    },
  },
  water_cooler_more: {
    display: "inline-block",
    marginLeft: pxToRem(10),
  },
  our_culture_wrapper: {
    marginTop: pxToRem(20),
  },
  our_culture_content: {
    margin: "40px 163px",
    wordBreak: "break-word",
  },
  o_c_l1_img: {
    height: pxToRem(315),
    width: pxToRem(225),
  },
  o_c_default_img: {
    height: pxToRem(235),
    width: pxToRem(320),
  },
  o_c_l2_img: {
    height: pxToRem(226),
    width: pxToRem(342),
  },
  o_c_l3_img1: {
    height: pxToRem(223),
    width: pxToRem(342),
  },
  o_c_l3_img2: {
    height: pxToRem(223),
    width: pxToRem(342),
  },
  o_c_l4_img1: {
    height: pxToRem(231),
    width: pxToRem(357),
  },
  o_c_l5_img1: {
    height: pxToRem(231),
    width: pxToRem(336),
  },
  margin_left_25: { marginLeft: pxToRem(25) },
  margin_right_25: { marginRight: pxToRem(25) },
  margin_botton_18: { marginBottom: pxToRem(18) },
  layout_content: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  o_c_layout_wrapper: {
    margin: `${pxToRem(40)} ${pxToRem(0)}`,
  },
  o_c_title_right: {
    margin: `${pxToRem(0)} ${pxToRem(20)} ${pxToRem(0)} ${pxToRem(0)}`,
  },
  o_c_title_left: {
    margin: `${pxToRem(0)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(20)}`,
  },
  o_c_content_right: {
    margin: `${pxToRem(10)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(0)}`,
    lineHeight: pxToRem(18),
  },
  o_c2_content_left: {
    margin: `${pxToRem(0)} ${pxToRem(20)} ${pxToRem(0)} ${pxToRem(0)}`,
    lineHeight: pxToRem(18),
  },
  o_c_content_left: {
    margin: `${pxToRem(0)} ${pxToRem(10)} ${pxToRem(0)} ${pxToRem(0)}`,
    lineHeight: pxToRem(18),
  },
  teamMemberStyle: {
    textDecoration: "underline",
    cursor: "pointer",
  },
  connect_section_wrapper: {
    marginTop: pxToRem(16),
  },
  connect_content: {
    width: "70%",
    margin: "0 auto",
    marginTop: pxToRem(30),
  },
  img_block: {
    marginBottom: pxToRem(30),
  },
  wrapContent: {
    flexWrap: "wrap",
  },
  alumni_team_wrap: {
    marginTop: pxToRem(26),
  },
  connect_image_wrap: {
    height: pxToRem(134),
  },
  connect_image: {
    height: pxToRem(105),
    width: pxToRem(105),
    borderRadius: "50%",
    marginTop: pxToRem(20),
    cursor: "pointer",
  },
  connect_image_college: {
    height: pxToRem(70),
    width: pxToRem(70),
    // borderRadius: "50%",
    marginTop: pxToRem(20),
  },
  connect_block: {
    textAlign: "center",
    width: "25%",
  },
  connect_block_college: {
    textAlign: "center",
    width: pxToRem(150),
  },
  height_309: {
    height: pxToRem(309),
  },
  height_434: {
    height: pxToRem(434),
    marginBottom: pxToRem(20),
  },
  height_171: {
    height: pxToRem(171),
  },
  height_70: {
    height: pxToRem(70),
    minHeight: pxToRem(80),
  },
  exp_pannel_wrap: {
    "& .MuiExpansionPanel-root": {
      margin: `${pxToRem(10)} ${pxToRem(10)} !important`,
    },
    "& .MuiExpansionPanel-rounded": {
      margin: `${pxToRem(10)} ${pxToRem(10)} !important`,
      "&:last-child": {
        borderBottomLeftRadius: "0 !important",
        borderBottomRightRadius: "0 !important",
      },
    },
    "& .MuiExpansionPanelDetails-root": {
      padding: `${pxToRem(8)} ${pxToRem(20)} !important`,
    },
    "& .MuiPaper-elevation1": {
      boxShadow: "none !important",
      borderBottom: `solid ${pxToRem(1)} ${
        color.secondary_palette.grays.shadow_gray
      }`,
    },
    "& .MuiExpansionPanelSummary-root": {
      minHeight: "auto !important",
    },
    "& .MuiExpansionPanelSummary-content": {
      margin: "0 !important",
    },
  },
  contact_us_content: {
    lineHeight: "1.5",
  },
  exp_pannel_closed: {
    paddingBottom: pxToRem(10),
  },
  clientele_inp: {
    // width: pxToRem(100),
    verticalAlign: "middle",
    display: "inline-block",
    paddingLeft: pxToRem(5),
    wordBreak: "break-all",
  },
  proc_inp: {
    // width: pxToRem(100),
    verticalAlign: "middle",
    display: "inline-block",
    paddingLeft: pxToRem(5),
    // wordBreak: "break-all",
  },
  contact_email: {
    // width: pxToRem(21),
    // height: pxToRem(13),
    // margin: `${pxToRem(5)} ${pxToRem(5)}`,
    position: "relative",
    top: pxToRem(-5),
    backgroundImage: `url("assets/images/purple_mail_icon.svg")`,
    content: `url('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7')`,
    backgroundSize: "cover",
    width: pxToRem(21),
    // height: pxToRem(13),
    margin: `${pxToRem(5)} ${pxToRem(5)}`,
    display: "inline-block",
    cursor: "pointer",
    "&:hover": {
      backgroundImage: `url("assets/images/green_mail_icon.svg")`,
      content: `url('data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7')`,
      backgroundSize: "cover",
      width: pxToRem(21),
      // height: pxToRem(13),
      margin: `${pxToRem(5)} ${pxToRem(5)}`,
      display: "inline-block",
      cursor: "pointer",
    },
  },
  contact_sec_wrap: {
    marginTop: pxToRem(20),
  },
  padding_left_5: {
    paddingLeft: pxToRem(5),
  },
  expand_icon: {
    height: pxToRem(19),
    width: pxToRem(19),
    cursor: "pointer",
  },
  pingAdmin: {
    cursor: "pointer",
    textAlign: "right",
  },
  gamification_banner_text: {
    position: "absolute",
    top: pxToRem(-41),
    left: pxToRem(20),
  },
  masked_bck: {
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: pxToRem(11),
    maxHeight: pxToRem(110),
  },
  current_mission: {
    textTransform: "uppercase",
    // textAlign: "center",
    position: "relative",
    paddingBottom: pxToRem(7),
  },
  gamification_banner: {
    cursor: "pointer",
  },
  gamification_banner_img: {
    width: pxToRem(102),
    height: pxToRem(130),
  },
  gamification_innertext: {
    position: "absolute",
    top: pxToRem(42),
    width: pxToRem(70),
    left: pxToRem(16),
    "& p": {
      textAlign: "center",
    },
  },
  pearl_row: {
    marginTop: pxToRem(8),
  },
  peral_spacing: {
    width: pxToRem(50),
    textAlign: "center",
  },
  peral_img: {
    backgroundImage: `url("assets/images/Cream Pearl.png")`,
    backgroundSize: "cover",
    textAlign: "center",
    width: pxToRem(30),
    height: pxToRem(30),
    margin: "0 auto",
    ...tradework_theme.typography.styles.Helvetica,
    fontWeight: "bold",
    fontSize: pxToRem(14),
    lineHeight: pxToRem(30),
  },
  gamification_section_label: {
    marginTop: pxToRem(5),
    ...tradework_theme.typography.styles.gillsans_sb,
    fontSize: pxToRem(8),
    color: "black",
    textTransform: "uppercase",
  },
  percentage: {
    ...tradework_theme.typography.styles.Helvetica,
  },
  gamification_width: {
    width: pxToRem(848),
    height: "90vh",
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.secondary_palette.blues.modal_blue,
    position: "relative",
    padding: pxToRem(5),
    "&:focus": {
      outline: "none !important",
    },
  },
  ping_img: {
    marginBottom: pxToRem(-10),
  },
  franklinSection_data: {
    marginTop: pxToRem(30),
  },
  franklin_rep_icon_wrap: {
    "& img": {
      width: pxToRem(20),
      height: pxToRem(20),
      padding: `${pxToRem(0)} ${pxToRem(3)}`,
    },
  },
  open_pos_left: {
    width: "70%",
    textAlign: "left",
    paddingLeft: pxToRem(5),
  },
  open_pos_right: {
    width: "30%",
    textAlign: "right",
    paddingRight: pxToRem(5),
  },
  overflow_content: {
    overflowY: "scroll",
    marginRight: pxToRem(7),
    marginTop: pxToRem(5),
    maxHeight: pxToRem(220),
    "&::-webkit-scrollbar": {
      width: pxToRem(10),
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: color.secondary_palette.grays.background_gray,
      borderRadius: pxToRem(20),
    },
  },
  open_pos_row: {
    marginBottom: pxToRem(10),
  },
  input_happy_container: {
    position: "relative",
    margin: `${pxToRem(5)} ${pxToRem(0)}`,
    padding: `${pxToRem(0)} ${pxToRem(4)}`,
  },
  open_pos_input: {
    width: "98%",
    fontSize: pxToRem(20),
    height: pxToRem(20),
    ...tradework_theme.typography.styles.gillsans_light,
  },
  happy_icon: {
    height: pxToRem(19),
    width: pxToRem(19),
    position: "absolute",
    right: pxToRem(7),
    top: pxToRem(4),
    cursor: "pointer",
  },
  open_pos_left_franklin: {
    paddingBottom: pxToRem(15),
    width: "50%",
  },
  textRight: {
    textAlign: "right",
  },
  report_link: {
    color: color.primary_palette.franklin_purple,
    "& label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
    },
  },
  padding_top_10: {
    paddingTop: pxToRem(10),
  },
  public_profile_container: {
    marginTop: pxToRem(60),
  },
  minus_showless: {
    width: pxToRem(16),
    height: pxToRem(16),
    position: "relative",
    top: pxToRem(2.5),
    left: pxToRem(3),
  },
  linkStyle: {
    textDecoration: "underline",
    fontSize: pxToRem(14),
    textAlign: "right",
    display: "block",
    color: color.primary_palette.franklin_purple,
    cursor: "pointer",
  },
  link: {
    fontSize: pxToRem(10),
    color: color.secondary_palette.grays.medium_gray,
    ...tradework_theme.typography.styles.avenir_book_r,
    textAlign: "left",
    marginTop: "18px",
    // width: "280px",
    wordBreak: "break-all",
  },
  linkCopy: {
    fontSize: pxToRem(14),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.gillsans_sb,
    // marginLeft: pxToRem(20),
    padding: `0px ${pxToRem(10)}`,
    borderRadius: pxToRem(2),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    cursor: "pointer",
    height: pxToRem(25),
  },
  greenCheck: {
    position: "absolute",
    top: pxToRem(58),
    right: pxToRem(36),
  },
  hoverUnderline: {
    "&:hover": {
      textDecoration: "underline",
    },
  },
  pitchTxtAlign: {
    display: "inline-block",
    marginLeft: pxToRem(10),
  },
  margin_right_10: {
    marginRight: pxToRem(10),
  },
  interviewTagText: {
    display: "inline-block",
  },
  hoverGreen: {
    "&:hover": {
      color: color.warpstone_glow,
    },
  },
  cursor_pointer: {
    cursor: "pointer",
  },
});

export default styles;
