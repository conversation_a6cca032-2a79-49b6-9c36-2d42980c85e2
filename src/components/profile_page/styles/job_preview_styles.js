import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { pxToRem, color } from "../../../utilities/themes";

const styles = withStyles({
  wrapper: {
    width: pxToRem(1050),
    border: `${pxToRem(2)} solid ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.white,
    // maxHeight: pxToRem(720),
    "&:focus": {
      outline: "none",
    },
    padding: pxToRem(10),
    // overflowY: "scroll",
  },
  close_styles: {
    fontSize: pxToRem(16),
    cursor: "pointer",
  },
  edit_space: {
    marginTop: pxToRem(39),
    border: `solid ${pxToRem(3)} transparent`,
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.gilded_mint}`,
    },
  },
  postedBlock: {
    border: `solid ${pxToRem(3)} transparent`,
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.gilded_mint}`,
    },
  },
  listingHover: {
    border: `solid ${pxToRem(3)} transparent`,
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.primary_palette.tricks_red}`,
    },
  },
  summaryHover: {
    border: `solid ${pxToRem(3)} transparent`,
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.primary_palette.tricks_red}`,
    },
  },
  preview_width: {
    width: pxToRem(855),
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    ...tradework_theme.typography.styles.gillsans_r,
    margin: "0 auto",
    padding: `${pxToRem(20)} ${pxToRem(38)}`,
    position: "relative",
  },
  margin_b_20: {
    marginBottom: pxToRem(20),
  },
  cheq_flag: {
    position: "absolute",
    height: pxToRem(50),
    width: pxToRem(43),
    right: pxToRem(20),
    top: pxToRem(0),
  },
  mask: { opacity: 0.5 },
  width_post: {
    width: pxToRem(919),
    padding: `${pxToRem(20)} ${pxToRem(38)}`,
    margin: "0 auto",
    position: "relative",
  },
  border_edit_section: {
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    width: pxToRem(931),
    margin: "0 auto",
    marginTop: pxToRem(40),
  },
  border_bottom_none: {
    borderBottom: "0 !important",
  },
  border_top_none: {
    borderTop: 0,
  },
  txtTransform: {
    textTransform: "uppercase",
  },
  padding_top_11: {
    paddingTop: pxToRem(11),
  },
  easy_apply: {
    width: pxToRem(134),
    height: pxToRem(28),
    borderRadius: pxToRem(4),
    backgroundColor: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.gillsans_sb,
    fontSize: pxToRem(15),
    color: color.primary_palette.white,
    textAlign: "right",
    paddingRight: pxToRem(10),
    textTransform: "uppercase",
  },
  padding_top_17: {
    paddingTop: pxToRem(17),
  },
  padding_right_37: {
    paddingRight: pxToRem(37),
  },
  gray_font: {
    color: color.secondary_palette.grays.light_gray,
  },
  padding_top_34: {
    paddingTop: pxToRem(34),
  },
  padding_5: {
    padding: `${pxToRem(5)} ${pxToRem(0)}`,
  },
  padding_R_5: {
    paddingRight: pxToRem(5),
  },
  padding_top_16: {
    paddingTop: pxToRem(16),
  },
  alignSelfCenter: {
    alignSelf: "center",
  },
  padding_left_3: {
    paddingLeft: pxToRem(3),
  },
  application_process: {
    paddingTop: pxToRem(7),
    paddingBottom: pxToRem(27),
    borderBottom: `solid ${pxToRem(1)} ${color.tenuous_coffee}`,
  },
  process_ind: {
    paddingTop: pxToRem(11),
    paddingBottom: pxToRem(21),
  },
  key_responsibilities_blk: {
    paddingTop: pxToRem(19),
    "& ul": {
      margin: 0,
      paddingLeft: pxToRem(35),
      "& li": {
        fontSize: pxToRem(14),
        ...tradework_theme.typography.styles.gillsans_r,
      },
    },
  },
  padding_bottom_20: {
    paddingBottom: pxToRem(30),
  },
  applyBtn: {
    width: pxToRem(222),
    height: pxToRem(28),
    borderRadius: pxToRem(4),
    backgroundColor: `${color.primary_palette.franklin_purple} !important`,
    color: `${color.primary_palette.white} !important`,
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.gillsans_sb,
    marginTop: pxToRem(28),
    marginBottom: pxToRem(40),
    padding: 0,
    "&:hover": {
      color: `${color.primary_palette.white} !important`,
    },
  },
  postBtn: {
    width: pxToRem(112),
    height: pxToRem(28),
    borderRadius: pxToRem(30),
    backgroundColor: color.primary_palette.white,
    color: `${color.gilded_mint} !important`,
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.gillsans_sb,
    marginTop: pxToRem(28),
    padding: 0,
    border: `solid ${pxToRem(1.5)} ${color.gilded_mint}`,
  },
  txtCenter: {
    textAlign: "center",
  },
  txtCapital: {
    textTransform: "capitalize",
  },
  companyLogo: {
    width: pxToRem(106),
    height: pxToRem(106),
    margin: "0 auto",
    textAlign: "center",
    "& img": {
      width: " 100%",
      height: "100%",
    },
  },
  padding_top_30: {
    paddingTop: pxToRem(30),
  },
  descriptionData: {
    color: color.primary_palette.black,
    fontSize: pxToRem(17),
    // paddingTop: `${pxToRem(17)} !important`,
  },
  descriptionData_summary: {
    color: color.primary_palette.black,
    fontSize: pxToRem(14),
  },
  middle_block: {
    width: pxToRem(918),
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    backgroundColor: color.primary_palette.white,
    margin: "0 auto",
    padding: `${pxToRem(25)} ${pxToRem(50)}`,
    boxShadow: "2px 2px 4px 0 rgba(0, 0, 0, 0.5)",
  },
  company_logos: {
    paddingRight: pxToRem(15),
    "& img": {
      width: pxToRem(75),
      height: pxToRem(75),
    },
  },
  company_section: {
    flexWrap: "wrap",
  },
  company_links: {
    flexGrow: 1,
    width: "30%",
  },
  company_snaps: {
    flexGrow: 1,
    width: "25%",
  },
  company_details: {
    flexGrow: 1,
    width: "45%",
  },
  company_snaps_imgs: {
    width: pxToRem(187),
    height: pxToRem(156),
  },
  circle_img: {
    width: pxToRem(90),
    margin: "0 auto",
    borderRadius: "100%",
    border: `solid ${pxToRem(2)} ${color.primary_palette.black}`,
  },
  marginBottom12: {
    marginBottom: pxToRem(12),
  },
  pos_rel: {
    position: "relative",
    width: pxToRem(105),
  },
  player_icon: {
    width: pxToRem(20),
    height: pxToRem(20),
    position: "absolute",
    bottom: pxToRem(5),
    right: pxToRem(18),
  },
  person_info: {
    padding: `${pxToRem(15)} ${pxToRem(0)}`,
    // flexWrap: "wrap",
    justifyContent: "space-between",
  },
  txtRight: {
    textAlign: "right",
  },
  txtDecoration: {
    textDecoration: "underline",
  },
  padding_bottom_15: {
    paddingBottom: pxToRem(15),
  },
  position_relative: {
    position: "relative",
  },
  edit_icon: {
    fontSize: pxToRem(30),
    position: "absolute",
    right: pxToRem(15),
    cursor: "pointer",
  },
  edit_icon_center: {
    position: "absolute",
    fontSize: pxToRem(65),
    left: "46%",
    top: pxToRem(-30),
  },
  edit_spacing: {
    paddingLeft: pxToRem(20),
    cursor: "pointer",
  },
  hover_green: {
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.gilded_mint}`,
    },
  },
  companyName_hover: {
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.wizard_box_colors.wizard_blue}`,
    },
  },
  hover_red: {
    paddingTop: `${pxToRem(0)} !important`,
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.primary_palette.tricks_red}`,
    },
  },
  border_transparent: {
    border: `solid ${pxToRem(3)} transparent`,
  },
  paddingSides: {
    padding: `${pxToRem(0)} ${pxToRem(5)}`,
  },
  hover_purple: {
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.primary_palette.franklin_purple}`,
    },
  },
  hover_blue: {
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.derisive_cobalt}`,
    },
  },
  hover_yellow: {
    "&:hover": {
      border: `solid ${pxToRem(3)} ${color.technical_buff}`,
    },
  },
  logo_wrapper: {
    textAlign: "center",
    width: pxToRem(130),
  },
  edit_company_details: {
    width: pxToRem(550),
    border: `solid ${pxToRem(3)} transparent`,
  },
  red_bolt: {
    paddingRight: pxToRem(10),
  },
  padding_left_50: {
    paddingLeft: pxToRem(50),
  },
  border_none: {
    border: "0 !important",
  },
  edit_spacing_details: {
    paddingLeft: pxToRem(13),
    marginLeft: pxToRem(39),
    marginTop: pxToRem(10),
  },
  padding_left_12: {
    paddingLeft: pxToRem(12),
  },
  elevator_pitch_icon: {
    fontSize: pxToRem(26),
    paddingRight: pxToRem(5),
    verticalAlign: "middle",
    "& path": {
      fill: color.primary_palette.black,
    },
  },
  processimg: {
    paddingRight: pxToRem(5),
    width: pxToRem(26),
    verticalAlign: "middle",
    height: pxToRem(25),
  },
  elevator_spacing: {
    paddingRight: `${pxToRem(15)} !important`,
  },
  processimg_edit: {
    paddingRight: pxToRem(15),
    width: pxToRem(26),
    verticalAlign: "middle",
    height: pxToRem(25),
  },
  padding_bottom_5: {
    paddingBottom: pxToRem(5),
  },
  apply_nowBtn: {
    width: pxToRem(112),
    height: pxToRem(28),
    borderRadius: pxToRem(30),
    border: `solid ${pxToRem(1)} ${color.form_colors.blueberry_purple}`,
    backgroundColor: color.primary_palette.white,
    ...tradework_theme.typography.styles.gillsans_sb,
    color: `${color.form_colors.blueberry_purple} !important`,
    fontSize: pxToRem(15),
    marginTop: pxToRem(20),
    marginBottom: pxToRem(20),
  },
  doneBtn: {
    width: pxToRem(112),
    height: pxToRem(28),
    borderRadius: pxToRem(30),
    border: `solid ${pxToRem(1)} ${color.form_colors.blueberry_purple}`,
    backgroundColor: color.primary_palette.white,
    ...tradework_theme.typography.styles.gillsans_sb,
    color: `${color.form_colors.blueberry_purple} !important`,
    fontSize: pxToRem(15),
    marginTop: pxToRem(25),
    marginBottom: pxToRem(40),
  },
  doneBtnWidth: {
    width: pxToRem(931),
    margin: "0 auto",
  },
  alignCenter: {
    alignSelf: "center",
  },
});

export default styles;
