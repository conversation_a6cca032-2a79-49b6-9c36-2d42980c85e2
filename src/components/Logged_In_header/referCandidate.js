import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { get } from "lodash";
// import uuidv4 from "uuid/v4";

import { color, pxToRem } from "../../utilities/themes";
import Row from "../common/ui_kit/row";
import SharePortfolioStyles from "./styles/referCandidateStyles";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CloseIcon from "../data_display/icons/Close";
import CustomButton from "../navigations/custom_buttons";
// import Add from "../data_display/icons/add_default_hover";
// import CustomScrollbars from "../data_display/custom_scroll";
// import CustomModal from "../inputs/custom_modal";
// import ConfirmInvite from "./confirmModal";
import CustomInputCount from "../inputs/custom_input_count";
import { BASEURL, EMAIL_REGEX } from "../../constants";
import CustomTextArea from "../inputs/custom_text_area";
// import CustomCheckbox from "../inputs/custom_checkbox";

import { CatsActions } from "../../redux/actions";

function SharePortfolio(props) {
  const { classes, onClose } = props;
  const textAreaRef = useRef(null);
  const [copySuccess, setCopySuccess] = useState("");
  const [state, setState] = useState({
    recieverEmail: "",
    message:
      "I thought that you might be interested in this potential candidate, who applied to our firm.",
    successMessage: "",
    recieverName: "",
    userId: "",
    emailError: false,
    candidateName: "",
  });

  const {
    recieverEmail,
    userEmail,
    successMessage,
    message,
    userName,
    profileLink,
    recieverName,
    emailError,
  } = state;

  const dispatch = useDispatch();
  const userInfoData = useSelector((state) => {
    return state.LookupReducer.userInfoData;
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setState({ ...state, [name]: value });
  };

  //   const validateEmail = (value) => {
  //     if (value.match(EMAIL_REGEX)) {
  //       setErrors({ ...errors, email: false });
  //       return;
  //     }
  //     setErrors({ ...errors, email: true });
  //   };

  //API
  useEffect(() => {
    setState({
      ...state,
      userName: `${userInfoData?.firstName} ${userInfoData?.lastName}`,
      userEmail: userInfoData?.email,
      
    });
    // eslint-disable-next-line
  }, [userInfoData]);

  const copyToClipboard = (e) => {
    textAreaRef.current.select();
    document.execCommand("copy");
    // This is just personal preference.
    // I prefer to not show the whole text area selected.
    e.target.focus();
    setCopySuccess("Copied!");
  };

  const sendInvite = () => {
    if (recieverEmail === "" || !recieverEmail.match(EMAIL_REGEX)) {
      setState({
        ...state,
        emailError: true,
      });
      return;
    } else {
      setState({
        ...state,
        emailError: false,
      });
    }
    const data = {
      ...state,
      companyId: localStorage.getItem("companyId"),
    };
    dispatch(
      CatsActions.sendInviteMails(data, () => {
        setState({
          ...state,
          successMessage: "Referral Sent Successfully",
        });
        setTimeout(() => {
          onClose();
        }, 3000);
      })
    );
  };

  return (
    <>
      <div className={classes.congratsModalStyles2}>
        <div className={`${classes.btnRight}`}>
          <CustomButton className={classes.crossBtn} isX>
            <CloseIcon onClick={onClose} />
          </CustomButton>
        </div>
        <div>
          <Text
            size={23}
            family="avenir_light"
            color={color.primary_palette.black}
            className={`${classes.textCenter} ${classes.headerTxtAlign}`}
          >
            {strings.modal.titles.beautiful_portfolio}
          </Text>
          <div className={classes.boxAlign}>
            <div className={classes.inputsAlignDiv}>
              <Row className={classes.alignBaseline}>
                <Row>
                  <Text
                    size={16}
                    family="gillsans_sb"
                    color={color.primary_palette.black}
                    style={{ width: "84px" }}
                  >
                    Your Name
                  </Text>
                </Row>
                <Row>
                  {/* <Text
                    size={16}
                    family="avenir_sb"
                    color={color.primary_palette.black}
                    className={classes.marinLeft5}
                  >
                    {userName}
                  </Text> */}
                  <CustomInputCount
                    className={`${classes.input_field_email} ${classes.marinLeft5}`}
                    // placeholder={strings.modal.titles.inputplaceholder}
                    onBlur={handleInputChange}
                    defaultValue={userName}
                    name="userName"
                  />
                </Row>
              </Row>
              <Row className={classes.alignBaseline}>
                <Row>
                  <Text
                    size={16}
                    color={color.primary_palette.black}
                    style={{ width: "84px" }}
                    family="gillsans_sb"
                  >
                    Your Email
                  </Text>
                </Row>
                <Row>
                  {/* <Text
                    size={16}
                    family="avenir_sb"
                    color={color.primary_palette.black}
                    className={classes.marinLeft5}
                  >
                    {userEmail}
                  </Text> */}
                  <CustomInputCount
                    className={`${classes.input_field_email} ${classes.marinLeft5}`}
                    // placeholder={strings.modal.titles.inputplaceholder}
                    onBlur={handleInputChange}
                    defaultValue={userEmail}
                    name="userEmail"
                  />
                </Row>
              </Row>
              <img
                alt=""
                // src="assets/images/Invite_Envelope.png"
                src="assets/images/The-Pilar-Stamp.png"
                className={classes.invite_Envelope}
              />
            </div>
            <div className={classes.messageBoxAlign}>
              <Text
                size={16}
                family="gillsans_sb"
                color={color.primary_palette.black}
              >
                Your Message
              </Text>
              <CustomTextArea
                defaultValue={message}
                name="message"
                className={classes.msgTxtarea}
                onBlur={handleInputChange}
                // ref={messageVal}
              ></CustomTextArea>
              <Text
                size={16}
                family="gillsans_sb"
                color={color.primary_palette.black}
              >
                Signatory:
              </Text>
              <Text
                size={16}
                family="avenir_sb"
                color={color.primary_palette.black}
                className={classes.marinLeft5}
              >
                All best, <br />
                {userName}
              </Text>
            </div>
            <Row className={classes.alignBaseline}>
              <Row style={{ marginLeft: pxToRem(26) }}>
                <Text
                  size={16}
                  family="gillsans_sb"
                  color={color.primary_palette.black}
                  style={{
                    marginRight: pxToRem(10),
                    position: "relative",
                    width: "148px",
                    textAlign: "right",
                  }}
                >
                  Friend First Name
                </Text>
              </Row>
              <Row>
                <CustomInputCount
                  className={classes.input_field_email}
                  // placeholder={strings.modal.titles.inputplaceholder}
                  onBlur={handleInputChange}
                  defaultValue={recieverName}
                  name="recieverName"
                />
              </Row>
            </Row>
            <Row className={classes.alignBaseline}>
              <Row style={{ marginLeft: pxToRem(26) }}>
                <Text
                  size={16}
                  family="gillsans_sb"
                  color={
                    emailError
                      ? color.primary_palette.christmas_red
                      : color.primary_palette.black
                  }
                  style={{
                    marginRight: pxToRem(10),
                    position: "relative",
                    width: "148px",
                    textAlign: "right",
                  }}
                >
                  Friend Email
                </Text>
              </Row>
              <Row>
                <CustomInputCount
                  className={classes.input_field_email}
                  // placeholder={strings.modal.titles.inputplaceholder}
                  onBlur={handleInputChange}
                  defaultValue={recieverEmail}
                  name="recieverEmail"
                />
              </Row>
            </Row>
            <CustomButton
              className={classes.invitesBtn1}
              onMouseUp={sendInvite}
            >
              {strings.modal.titles.send_invites}
            </CustomButton>
          </div>
          {/* {successMessage && ( */}
          <Text
            size={14}
            family="gillsans_sb"
            color={color.warpstone_glow}
            style={{
              textAlign: "center",
              marginTop: pxToRem(6),
              position: "absolute",
              right: "52px",
            }}
          >
            {successMessage && (
              <img
                alt=""
                src="assets/icons/Green Checkmark.svg"
                // style={{ marginRight: pxToRem(6) }}
              />
            )}{" "}
            {successMessage}
          </Text>
          {/* )} */}
          <div>
            <div className={classes.copyBlock}>
              <Text
                size={14}
                family="avenir_bold"
                color={color.primary_palette.black}
                className={classes.displayInline}
              >
                Candidate Profile Link
              </Text>
              <input
                ref={textAreaRef}
                name="profileLink"
                defaultValue={profileLink}
                className={classes.messageBoxStyle}
              />
              <Text
                size={12}
                family="avenir_sb"
                color={color.primary_palette.franklin_purple}
                className={classes.copyAlign}
                onClick={copyToClipboard}
              >
                COPY
              </Text>
              {copySuccess && (
                <img
                  alt=""
                  src="assets/icons/Green Checkmark.svg"
                  style={{ marginLeft: pxToRem(6) }}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default SharePortfolioStyles(SharePortfolio);
