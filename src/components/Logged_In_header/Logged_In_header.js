import React, { useEffect } from "react";
import { get } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { map } from "lodash";
import { useHistory } from "react-router-dom";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";

import LoggedInHeaderStyles from "./styles/Logged_header_styles";
import strings from "../../utilities/strings";
import LoginActions from "../../redux/actions/login_actions";
import { ClickAwayListener } from "@material-ui/core";
import {
  PROFILE_TYPES,
  TOKEN_KEY,
  SPACK_DEV_URL,
  SPACK_QA_URL,
  SPACK_STAGE_URL,
  TDW_URL,
  BASEURL,
} from "../../constants";
import { ProfileActions } from "../../redux/actions";
import { color } from "../../utilities/themes";
import _ from "lodash";

function LoggedInHeader(props) {
  const { classes, toggleVisitCommunityModal } = props;
  const dispatch = useDispatch();
  const history = useHistory();
  const [isToggle, setIsToggle] = React.useState(false);
  const [openFeedBack, setOpenFeedback] = React.useState(true);
  const [companyData, setCompanyData] = React.useState(null);
  const CompanyListByUser = useSelector(
    (state) => state.Profile.CompanyListByUser
  );

  const fetchData = () => {
    dispatch(
      ProfileActions.getCompanyProfileData([PROFILE_TYPES.COMPANY_INFO])
    );
    dispatch(ProfileActions.getCompaniesListByUser());
    dispatch(ProfileActions.getProfileDataOfInfo("COMPANYINFO"));
    // dispatch(ProfileActions.fetchJobListings());
    dispatch(ProfileActions.fetchAllTrades());
    dispatch(ProfileActions.getInterviewInfo());
    dispatch(ProfileActions.fetchTopLocations({ name: "" }));
  };
  const userData = useSelector((state) => state.Profile.companyinfo);
  const firstImpression = useSelector((state) => state.Profile.userinfo);
  const userInfo = useSelector((state) => state.Profile.profileUserData);
  // const isPublicProfile = window.location.pathname.includes("/public/");
  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    dispatch(ProfileActions.fetchJobListings());
    if (CompanyListByUser && CompanyListByUser.length > 0) {
      if (!localStorage.getItem("companyId")) {
        setCompany(CompanyListByUser[0], false);
      } else {
        let companyId = localStorage.getItem("companyId");
        let companyData = _.find(CompanyListByUser, { _id: companyId });
        setCompany(companyData, false);
        setCompanyData(companyData);
      }
    }
  }, [CompanyListByUser]);

  const toggle = React.useCallback(() => setIsToggle(!isToggle));

  const logOut = () => {
    dispatch(LoginActions.logOut());
    toggle();
  };

  const redirectToLpack = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    let token = localStorage.getItem("tradeworks_user_token");
    if (host.includes("localhost")) {
      host = "localhost:3001";
      window.location.href = `http://${host}/lpack/profile/${token}`;
    } else {
      host = BASEURL.URL;
      window.location.href = `${host}lpack/lpack/profile/${token}`;
    }
  };

  const handleMenuClick = (selectedItem) => () => {
    if (selectedItem === "Logout") {
      dispatch(LoginActions.logOut());
      let host = get(window, "location.host", "");
      if (host) {
        if (host.includes("localhost")) {
          host = "localhost:3000";
          window.location.href = `http://${host}/logout`;
        } else {
          if (host.includes("-dev-")) {
            host = SPACK_DEV_URL;
          }
          if (host.includes("-qa-")) {
            host = SPACK_QA_URL;
          }
          if (host.includes("-stage-")) {
            host = SPACK_STAGE_URL;
          } else {
            host = BASEURL.URL;
          }
          window.location.href = `${host}logout`;
        }
      }
    }
    if (selectedItem === "Item1") {
      return;
    }
  };

  const handleImpersonateLogout = () => {
    let userData = localStorage.getItem("userData");
    if (userData !== "undefined") {
      userData = JSON.parse(userData);
    } else {
      userData = {};
    }
    const dataToSubmit = {
      userId: get(userData, "user.id"),
      email: get(userData, "user.email"),
    };
    dispatch(
      ProfileActions.logoutImpersonate(dataToSubmit, (res) => {
        const token = res.token;
        localStorage.setItem("tradeworks_user_token", token);
        localStorage.removeItem("userData");
        localStorage.removeItem("isInpersonate");
        let host = get(window, "location.host", "");
        let route = "";
        if (host) {
          route = `${BASEURL.URL}settings`;
        }
        window.location.href = `${route}/${token}`;
      })
    );
  };

  const settings = () => {
    // history.push("/settings");
    toggle();
  };

  const toggleFeedBackModal = () => {
    setOpenFeedback(false);
  };

  const openFeedBackModal = () => {
    setOpenFeedback(true);
  };

  const setDefaultCompany = (company) => () => {
    localStorage.setItem("companyId", get(company, "_id", ""));
    localStorage.setItem("newCompanyId", get(company, "_id", ""));
    localStorage.setItem("companyName", get(company, "name", ""));
    localStorage.setItem("active", "company");
    toggle();
  };

  const setCompany = (company, isOpenToggle) => () => {
    if (company && company.userRole !== "Admin") {
      localStorage.setItem("companyId", get(company, "_id", ""));
      localStorage.setItem("newCompanyId", get(company, "_id", ""));
      localStorage.setItem("companyLogo", get(company, "companyLogo", ""));
      localStorage.setItem("companyName", get(company, "name", ""));
      localStorage.setItem("active", "company");
      window.location.href = `${BASEURL.URL}cpack/company/public/${company.tradeWorkUrl}`;
    } else {
      window.location.href = `${BASEURL.URL}cpack/twc/profile`;
      localStorage.setItem("companyId", get(company, "_id", ""));
      localStorage.setItem("newCompanyId", get(company, "_id", ""));
      localStorage.setItem("companyLogo", get(company, "companyLogo", ""));
      localStorage.setItem("companyName", get(company, "name", ""));
      localStorage.setItem("active", "company");
      localStorage.removeItem("isSetNewCompany");
      isOpenToggle && toggle();
      setCompanyData(company);
      dispatch(
        ProfileActions.getCompanyProfileData([
          PROFILE_TYPES.COMPANY_PITCH,
          PROFILE_TYPES.WATER_COOLER,
          PROFILE_TYPES.COMPANY_HIGHLIGHTS,
          PROFILE_TYPES.BUSINESS_CARD,
          PROFILE_TYPES.OFFICE_SHOTS,
          PROFILE_TYPES.COMPANY_INFO,
        ])
      );
      dispatch(ProfileActions.fetchJobListings());
      dispatch(ProfileActions.getOpenPositionData({ search: "" }));
      dispatch(ProfileActions.getCompanyMembersById());
    }
  };

  const redirectTo = (type) => () => {
    // redirectToSettings();
    if (type == "personal") {
      localStorage.setItem("active", "personal");
    }
    toggle();
    switch (type) {
      case "personal":
        return;
      case "company":
        return;
      case "portfolios":
        return;
      case "locations":
        return;
      default:
        break;
    }
  };

  const redirectToLpackProfile = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("companyId");
    localStorage.removeItem("newCompanyId");
    localStorage.removeItem("companyName");
    let token = localStorage.getItem("tradeworks_user_token");
    if (host.includes("localhost")) {
      host = "localhost:3001";
      window.location.href = `http://${host}/lpack/profile/${token}`;
    } else {
      host = `${BASEURL.URL}`;
      // host = "twwstage.franklinreport.com";
      window.location.href = `${host}lpack/lpack/profile/${token}`;
    }
  };

  const redirectToCompanySettings = () => {
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let host = `${BASEURL.URL}settings/company`;
    // let host = "http://twwstage.franklinreport.com/settings/company";
    window.location.href = `${host}/${token}/${companyId}`;
  };

  const redirectToMemberSettings = () => {
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let host = `${BASEURL.URL}settings/adminAccess`;
    // let host = "http://twwstage.franklinreport.com/settings/adminAccess";
    window.location.href = `${host}/${token}/${companyId}`;
  };

  const redirectToSettings = () => {
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let host = `${BASEURL.URL}settings/company`;
    // let host = "http://twwstage.franklinreport.com/settings/company";
    window.location.href = `${host}/${token}/${companyId}`;
  };

  const returnPoints = () => {
    if (userData && userData.goodCitizenStatus) {
      return get(userData, "goodCitizenStatus.points", 0);
    }
  };

  const returnStatus = () => {
    if (userData && userData.goodCitizenStatus) {
      let pointsVal = get(userData, "goodCitizenStatus.points", 0);
      if (pointsVal >= 0 && pointsVal < 150) {
        return "Just Beginning";
      } else if (pointsVal >= 150 && pointsVal < 300) {
        return "Getting There";
      } else if (pointsVal >= 300 && pointsVal < 500) {
        return "Making Progress";
      } else if (pointsVal >= 500 && pointsVal < 700) {
        return "Good";
      } else {
        return "Excellent";
      }
    }
  };

  const returnStatusIcon = () => {
    if (userData && userData.goodCitizenStatus) {
      let pointsVal = get(userData, "goodCitizenStatus.points", 0);
      if (pointsVal >= 0 && pointsVal < 150) {
        return "assets/images/Just Beggining.svg";
      } else if (pointsVal >= 150 && pointsVal < 300) {
        return "assets/images/Getting there.svg";
      } else if (pointsVal >= 300 && pointsVal < 500) {
        return "assets/images/Making Progress.svg";
      } else if (pointsVal >= 500 && pointsVal < 700) {
        return "assets/images/Good.svg";
      } else {
        return "assets/images/Excellent.svg";
      }
    }
  };

  const navigateToSuperAdmin = (menuoption = "") => {
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    // let host = get(window, "location.host", "");
    let hostUrl = "";
    hostUrl = `${BASEURL.URL}settings/approvalList`;
    window.location.href = `${hostUrl}/${token}/${companyId}`;
  };

  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/wpack";
      // window.location.href = `http://${host}/${token}/${companyId}`;
      if (host.includes("localhost")) {
        host = "localhost:3003";
        window.location.href = `http://${host}/${token}/${companyId}`;
      } else {
        if (host.includes("-dev-")) {
          host = TDW_URL.WPACK.DEV;
        }
        if (host.includes("-qa-")) {
          host = TDW_URL.WPACK.QA;
        }
        if (host.includes("-stage-")) {
          host = TDW_URL.WPACK.STAGE;
        }
        if (host.includes("-twwstage-")) {
          host = `${BASEURL.URL}wpack`;
          // window.location.href = `http://${host}/${token}/${companyId}`;
        } else {
          host = `${BASEURL.URL}wpack`;
        }
        window.location.href = `${host}/${token}/${companyId}`;
      }
    }
  };

  const redirectToCats = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}cats`;
      window.location.href = `${host}/${token}/${companyId}`;
    }
  };

  const navigateToNewCompany = () => {
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    localStorage.setItem("isSetNewCompany", true);
    dispatch(
      ProfileActions.getCompanyProfileData([
        PROFILE_TYPES.COMPANY_PITCH,
        PROFILE_TYPES.WATER_COOLER,
        PROFILE_TYPES.COMPANY_HIGHLIGHTS,
        PROFILE_TYPES.OFFICE_SHOTS,
      ])
    );
    history.push("/wizard");
    toggle();
  };

  return (
    <>
      <div className={classes.profile_spacing}>
        <img
          src={
            localStorage.getItem("isSetNewCompany") !== "true" &&
            localStorage.getItem("companyId") !== "" &&
            localStorage.getItem("companyId") !== null &&
            localStorage.getItem("companyName") !== ""
              ? get(companyData, "companyLogo") || "assets/images/Gray Star.svg"
              : get(userData, "profileImg.square") ||
                get(userData, "profileImg.circle") ||
                get(userData, "profileUrl") ||
                get(userInfo, "profileUrl") ||
                "assets/images/Gray Star.svg"
          }
          alt="coin_icon"
          className={classes.profile_icon}
          onClick={toggle}
        />
        <div className={classes.logoutBtn}>
          <div className={classes.btnPos}>
            <img
              src="assets/icons/filled_down_arrow.svg"
              className={classes.menuIcon}
              alt="coin_icon"
              onClick={toggle}
            />
            {isToggle && (
              <ClickAwayListener onClickAway={toggle}>
                <div className={classes.settings}>
                  <ul>
                    <Row
                      className={
                        "personal" === localStorage.getItem("active")
                          ? classes.flexCenter
                          : classes.flexCenterInactive
                      }
                    >
                      <Text
                        size={15}
                        color={color.primary_palette.black}
                        family="gillsans_sb"
                        // className={classes.txtRight}
                        onClick={() => {
                          redirectToLpackProfile();
                        }}
                        style={{ width: "200px", wordWrap: "break-word" }}
                      >
                        {get(userInfo, "firstName", "")
                          ? `${get(userInfo, "firstName", "")} ${get(
                              userInfo,
                              "lastName",
                              ""
                            )}`
                          : `${get(userData, "firstName", "")} ${get(
                              userData,
                              "lastName",
                              ""
                            )}`}
                      </Text>
                      <img
                        src={
                          get(userData, "profileImg.square") ||
                          get(userData, "profileImg.circle") ||
                          get(userData, "profileUrl") ||
                          "assets/images/Gray Star.svg"
                        }
                        className={classes.nav_logo}
                      />
                    </Row>
                    {CompanyListByUser &&
                      map(CompanyListByUser, (company) => {
                        return (
                          <Row
                            key={company._id}
                            className={
                              company._id ===
                                localStorage.getItem("companyId") &&
                              "company" == localStorage.getItem("active")
                                ? classes.flexCenter
                                : classes.flexCenterInactive
                            }
                            onClick={setCompany(company, true)}
                          >
                            <img
                              alt="star"
                              src={"assets/icons/star.png"}
                              className="star"
                            />
                            <Text
                              size={15}
                              color={color.primary_palette.black}
                              family="gillsans_sb"
                              // className={classes.txtRight}
                              onClick={setCompany(company, true)}
                              style={{ width: "200px", wordWrap: "break-word" }}
                            >
                              {get(company, "name", "")} <br />
                            </Text>
                            <img
                              src={
                                get(
                                  company,
                                  "companyLogo",
                                  "assets/images/Shadow TW Logo.svg"
                                ) || "assets/images/Shadow TW Logo.svg"
                              }
                              className={classes.nav_logo}
                            />
                          </Row>
                        );
                      })}
                    <Row
                      className={
                        "personal" === localStorage.getItem("active")
                          ? classes.flexCenter
                          : classes.flexCenterInactive
                      }
                    >
                      <Text
                        size={12}
                        family="gillsans_sb"
                        color={color.primary_palette.franklin_purple}
                        className={`${classes.txtRight} ${classes.spacing_plus}`}
                        onClick={navigateToNewCompany}
                      >
                        {strings.settings.titles.plus_company}
                      </Text>
                      <img
                        src={"assets/images/Circle Plus 1.svg"}
                        className={classes.nav_logo_profile}
                      />
                    </Row>
                    <div
                      className={`${classes.secondaryMenu}`}
                      style={{ marginBottom: "7px" }}
                    >
                      <div onClick={toggleVisitCommunityModal}>
                        <Row
                          className={`${classes.secondaryOption} ${classes.margin_bottom_0}`}
                          // onClick={redirectToCats}
                        >
                          <img src="assets/icons/visittw.png" />
                          <Text
                            size={15}
                            color={color.primary_palette.black}
                            family="gillsans_sb"
                          >
                            {strings.settings.titles.visit_tw_community}
                          </Text>
                        </Row>
                      </div>
                      {(get(userData, "email") === "<EMAIL>" ||
                        get(userData, "email") === "<EMAIL>" ||
                        get(userData, "email") === "<EMAIL>" ||
                        get(userData, "email") === "<EMAIL>" ||
                        get(userData, "email") === "<EMAIL>") && (
                        <div onClick={navigateToSuperAdmin}>
                          <Row
                            className={`${classes.secondaryOption} ${classes.margin_bottom_0}`}
                          >
                            <img
                              src="assets/images/super_admin_logo.png"
                              style={{
                                width: "fit-content",
                                marginRight: "6px",
                              }}
                            />
                            <Text
                              size={15}
                              color={color.super_admin}
                              family="gillsans_sb"
                            >
                              SUPER ADMIN
                            </Text>
                          </Row>
                        </div>
                      )}
                    </div>
                    <div
                      className={`${classes.secondaryMenu} ${classes.settingsMenu}`}
                    >
                      {localStorage.getItem("active") === "company" && (
                        <>
                          <Row className={classes.secondaryOption}>
                            <img src="assets/icons/icon-settings.svg" />
                            <Text
                              size={15}
                              color={color.primary_palette.black}
                              family="gillsans_sb"
                              onClick={redirectToCompanySettings}
                            >
                              {strings.settings.titles.our_company_info}
                            </Text>
                          </Row>
                          <Row className={classes.secondaryOption}>
                            <img src="assets/icons/company-member.svg" />
                            <Text
                              size={15}
                              color={color.primary_palette.black}
                              family="gillsans_sb"
                              onClick={redirectToMemberSettings}
                            >
                              {strings.settings.titles.company_members}
                            </Text>
                          </Row>
                        </>
                      )}
                      {/* <Row className={classes.secondaryOption}>
                      <img src="assets/icons/portfolio.svg" />
                      <Text
                        size={15}
                        color={color.primary_palette.black}
                        family="gillsans_sb"
                        onClick={redirectToSettings}
                      >
                        {strings.settings.titles.franklin_report_portfolios}
                      </Text>
                    </Row> */}
                      <Row className={classes.secondaryOption}>
                        <img src="assets/icons/icon-notications.svg" />
                        <Text
                          size={15}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          onClick={redirectToSettings}
                        >
                          {/* {localStorage.getItem("active") === "company" && */}
                          {strings.settings.titles.notifications}
                          {/* is admin */}
                          {/* {localStorage.getItem("active") === "company" && (strings.settings.titles.comments_notifications)} */}
                        </Text>
                      </Row>
                      <Row className={classes.secondaryOption}>
                        <img src="assets/images/get_found.png" />
                        <Text
                          size={15}
                          color={color.primary_palette.black}
                          family="gillsans_sb"
                          onClick={redirectToSettings}
                        >
                          {/* {localStorage.getItem("active") === "company" && */}
                          OFFICE LOCATIONS
                          {/* is admin */}
                          {/* {localStorage.getItem("active") === "company" && (strings.settings.titles.comments_notifications)} */}
                        </Text>
                      </Row>
                    </div>
                    <Row
                      className={`${classes.logoutMenu} ${classes.border_top_gray}`}
                    >
                      <img src="assets/icons/icon-log-out.svg" />
                      <Text
                        size={15}
                        color={color.primary_palette.black}
                        family="gillsans_sb"
                        className={classes.logoutTxt}
                        onClick={
                          localStorage.getItem("isInpersonate") === "true"
                            ? handleImpersonateLogout
                            : handleMenuClick("Logout")
                        }
                      >
                        {strings.settings.titles.log_out}{" "}
                        {localStorage.getItem("isInpersonate") === "true" &&
                          "(Impersonate)"}
                      </Text>
                    </Row>
                  </ul>
                </div>
              </ClickAwayListener>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default LoggedInHeaderStyles(LoggedInHeader);
