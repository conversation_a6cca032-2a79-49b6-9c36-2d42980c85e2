import React, { Component } from "react";
import _, { get } from "lodash";
import { color, pxToRem } from "../../utilities/themes";
import RequestStyles from "./styles/requestStyles";
import Text from "../common/ui_kit/text";
import Row from "../common/ui_kit/row";
import strings from "../../utilities/strings";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import CustomSelect from "../inputs/custom_select";
import Chevron_Down_Icon from "../data_display/icons/Arrow_Chevron_Down";
import { MenuItem } from "@material-ui/core";
import { BASEURL, TOKEN_KEY } from "../../constants";
import CustomScrollbars from "../data_display/custom_scroll";

class TalentSearch extends Component {
  constructor(props) {
    super(props);
    this.state = {
      jobs: "",
      jobsError: false,
    };
  }

  handleDropdownSelect = (name, value) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    this.setState({ [name]: value, jobsError: false });
  };

  sendRequest = () => {
    let host = get(window, "location.host", "");
    if (host.includes("localhost")) {
      host = "localhost:3009";
    } else {
      host = `${BASEURL.URL}cats`;
      // host = "twwstage.franklinreport.com/cats";
    }
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let jobId = this.state.jobs ? this.state.jobs._id : "empty";
    if (jobId === "empty") {
      this.setState({ jobsError: true });
      return;
    }
    window.location.href = `${host}/${token}/${companyId}/DiscoverCandidate/${jobId}`;
    this.props.onClose();
  };

  render() {
    const { classes, onClose, jobsListOptions } = this.props;
    const { jobs, jobsError } = this.state;
    return (
      <>
        <div className={classes.talentModal_width}>
          <div className={classes.btnRight}>
            <CustomButton className={classes.crossBtn}>
              <Close_Icon onClick={onClose} />
            </CustomButton>
          </div>
          <div className={classes.textCenter}>
            <img src="assets/images/alert_icon.PNG" />
          </div>
          <div className={classes.spacing_from}>
            <Text
              size={18}
              color={color.primary_palette.black}
              family="gillsans_sb"
              className={classes.textCenter}
              style={{ marginBottom: "15px" }}
            >
              FINDING TALENT FOR WHICH JOB LISTING?
            </Text>
            <Row className={classes.margin20}>
              <Text
                size={14}
                color={color.primary_palette.black}
                family="gillsans_sb"
                className={classes.text_width}
                style={{ marginTop: "6px" }}
              >
                Your Job Listing
              </Text>
              <CustomSelect
                className={classes.job_listing_dropdown}
                IconComponent={Chevron_Down_Icon}
                name="jobs"
                value={jobs}
                renderValue={(value) =>
                  get(value, "jobTitle", "Select listing")
                }
              >
                <CustomScrollbars
                  style={{
                    minHeight:
                      get(jobsListOptions, "jobs", []).length > 3 &&
                      pxToRem(280),
                  }}
                >
                  <MenuItem
                    key="999"
                    onClick={this.handleDropdownSelect("jobs", "")}
                  >
                    Select listing
                  </MenuItem>
                  {jobsListOptions &&
                    get(jobsListOptions, "jobs", []).map((jobs, idx) => {
                      return (
                        <MenuItem
                          key={idx}
                          onClick={this.handleDropdownSelect("jobs", jobs)}
                        >
                          {jobs.jobTitle}
                        </MenuItem>
                      );
                    })}
                </CustomScrollbars>
              </CustomSelect>
            </Row>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="gillsans_r"
              className={classes.textCenter}
              style={{ marginBottom: "15px" }}
            >
              A specific job is required for this function. <br />
              Please select one of your jobs from the list or create a new job.
            </Text>
            {jobsError && (
              <Text
                size={16}
                color={color.primary_palette.christmas_red}
                family="gillsans_r"
                style={{ position: "absolute", marginLeft: "140px" }}
              >
                Please select Job Listing
              </Text>
            )}
            <div className={classes.textCenter}>
              <CustomButton
                className={classes.reqBtn}
                onClick={this.sendRequest}
                style={{ width: "100px" }}
              >
                SEEK
              </CustomButton>
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default RequestStyles(TalentSearch);
