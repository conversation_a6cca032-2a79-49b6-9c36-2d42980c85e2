import React, { use<PERSON><PERSON>back, useEffect } from "react";
import { LinearProgress, Divider, Tab, Tabs, Grid } from "@material-ui/core";
import { get } from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { map } from "lodash";
import { useHistory } from "react-router-dom";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import AnimationHeader from "./animationArch";

import LoggedInHeaderStyles from "./styles/Logged_header_styles";
import strings from "../../utilities/strings";
import LoginActions from "../../redux/actions/login_actions";
import { ClickAwayListener } from "@material-ui/core";
import {
  PROFILE_TYPES,
  TOKEN_KEY,
  SPACK_DEV_URL,
  SPACK_QA_URL,
  SPACK_STAGE_URL,
  TDW_URL,
  CATS_URL,
  LLAMA_DEV_URL,
  LLAMA_STAGE_URL,
  LLAMA_QA_URL,
  BASEURL,
} from "../../constants";
import { ProfileActions } from "../../redux/actions";
import { color } from "../../utilities/themes";
import FeedbackModal from "../modals/feedbacks/FeedBackModal";

function CitizenPoints(props) {
  const { classes } = props;
  const dispatch = useDispatch();
  const history = useHistory();
  const [isToggle, setIsToggle] = React.useState(false);
  const [openFeedBack, setOpenFeedback] = React.useState(true);

  const CompanyListByUser = useSelector(
    (state) => state.Profile.CompanyListByUser
  );

  const fetchData = () => {
    dispatch(
      ProfileActions.getCompanyProfileData([PROFILE_TYPES.COMPANY_INFO])
    );
    dispatch(ProfileActions.getCompaniesListByUser());
    dispatch(ProfileActions.getProfileDataOfInfo("COMPANYINFO"));
  };
  const userData = useSelector((state) => state.Profile.companyinfo);
  const userInfo = useSelector((state) => state.Profile.profileUserData);
  const isPublicProfile = window.location.pathname.includes("/public/");
  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (CompanyListByUser && CompanyListByUser.length > 0) {
      if (!localStorage.getItem("companyId")) {
        if (CompanyListByUser[0].userRole === "Admin") {
          setCompany(CompanyListByUser[0], false)();
        }
      }
    }
  }, [CompanyListByUser]);
  const toggle = React.useCallback(() => setIsToggle(!isToggle));

  const logOut = () => {
    dispatch(LoginActions.logOut());
    toggle();
  };

  const redirectToLpack = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    let token = localStorage.getItem("tradeworks_user_token");
    if (host.includes("localhost")) {
      host = "localhost:3001";
      window.location.href = `http://${host}/lpack/profile/${token}`;
    } else {
      host = BASEURL.URL;
      window.location.href = `${host}lpack/lpack/profile/${token}`;
    }
  };

  const handleMenuClick = (selectedItem) => () => {
    if (selectedItem === "Logout") {
      dispatch(LoginActions.logOut());
      let host = get(window, "location.host", "");
      if (host) {
        if (host.includes("localhost")) {
          host = "localhost:3000";
          window.location.href = `http://${host}/logout`;
        } else {
          if (host.includes("-dev-")) {
            host = SPACK_DEV_URL;
          }
          if (host.includes("-qa-")) {
            host = SPACK_QA_URL;
          }
          if (host.includes("-stage-")) {
            host = SPACK_STAGE_URL;
          } else {
            host = BASEURL.URL;
          }
          window.location.href = `${host}logout`;
        }
      }
    }
    if (selectedItem === "Item1") {
      return;
    }
  };

  const settings = () => {
    // history.push("/settings");
    toggle();
  };

  const toggleFeedBackModal = () => {
    setOpenFeedback(false);
  };

  const openFeedBackModal = () => {
    setOpenFeedback(true);
  };

  const setDefaultCompany = (company) => () => {
    localStorage.setItem("companyId", get(company, "_id", ""));
    localStorage.setItem("newCompanyId", get(company, "_id", ""));
    localStorage.setItem("companyName", get(company, "name", ""));
    localStorage.setItem("companyLogo", get(company, "companyLogo", ""));
    localStorage.setItem("active", "company");
    toggle();
  };

  const setCompany = (company, isOpenToggle) => () => {
    localStorage.setItem("companyId", get(company, "_id", ""));
    localStorage.setItem("newCompanyId", get(company, "_id", ""));
    localStorage.setItem("companyLogo", get(company, "companyLogo", ""));
    localStorage.setItem("companyName", get(company, "name", ""));
    localStorage.setItem("active", "company");
    isOpenToggle && toggle();
    dispatch(
      ProfileActions.getCompanyProfileData([
        PROFILE_TYPES.COMPANY_PITCH,
        PROFILE_TYPES.WATER_COOLER,
        PROFILE_TYPES.COMPANY_HIGHLIGHTS,
        PROFILE_TYPES.BUSINESS_CARD,
        PROFILE_TYPES.OFFICE_SHOTS,
        PROFILE_TYPES.COMPANY_INFO,
      ])
    );
  };

  const redirectTo = (type) => () => {
    redirectToSettings();
    if (type == "personal") {
      localStorage.setItem("active", "personal");
    }
    toggle();
    switch (type) {
      case "personal":
        return;
      case "company":
        return;
      case "portfolios":
        return;
      case "locations":
        return;
      default:
        break;
    }
  };

  const redirectToSettings = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/wpack";
      // window.location.href = `http://${host}/${token}/${companyId}`;
      if (host.includes("localhost")) {
        host = "localhost:3011";
        window.location.href = `http://${host}/${token}/${companyId}`;
      } else {
        if (host.includes("-dev-")) {
          host = TDW_URL.WPACK.DEV;
        }
        if (host.includes("-qa-")) {
          host = TDW_URL.WPACK.QA;
        }
        if (host.includes("-stage-")) {
          host = TDW_URL.WPACK.STAGE;
        }
        if (host.includes("-twwstage-")) {
          host = `${BASEURL.URL}settings`;
        } else {
          host = `${BASEURL.URL}settings`;
        }
        window.location.href = `${host}/${token}/${companyId}`;
      }
    }
  };

  const returnPoints = () => {
    if (userData && userData.goodCitizenStatus) {
      return get(userData, "goodCitizenStatus.points", 0);
    }
  };

  const returnStatus = () => {
    if (userData && userData.goodCitizenStatus) {
      let pointsVal = get(userData, "goodCitizenStatus.points", 0);
      if (pointsVal >= 0 && pointsVal < 150) {
        return "Just Beginning";
      } else if (pointsVal >= 150 && pointsVal < 300) {
        return "Getting There";
      } else if (pointsVal >= 300 && pointsVal < 500) {
        return "Making Progress";
      } else if (pointsVal >= 500 && pointsVal < 700) {
        return "Good";
      } else {
        return "Excellent";
      }
    }
  };

  const returnStatusIcon = () => {
    if (userData && userData.goodCitizenStatus) {
      let pointsVal = get(userData, "goodCitizenStatus.points", 0);
      if (pointsVal >= 0 && pointsVal < 150) {
        return "assets/images/Just Beggining.svg";
      } else if (pointsVal >= 150 && pointsVal < 300) {
        return "assets/images/Getting there.svg";
      } else if (pointsVal >= 300 && pointsVal < 500) {
        return "assets/images/Making Progress.svg";
      } else if (pointsVal >= 500 && pointsVal < 700) {
        return "assets/images/Good.svg";
      } else {
        return "assets/images/Excellent.svg";
      }
    }
  };

  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/wpack";
      // window.location.href = `http://${host}/${token}/${companyId}`;
      if (host.includes("localhost")) {
        host = "localhost:3003";
        window.location.href = `http://${host}/${token}/${companyId}`;
      } else {
        if (host.includes("-dev-")) {
          host = TDW_URL.WPACK.DEV;
        }
        if (host.includes("-qa-")) {
          host = TDW_URL.WPACK.QA;
        }
        if (host.includes("-stage-")) {
          host = TDW_URL.WPACK.STAGE;
        }
        if (host.includes("-twwstage-")) {
          host = `${BASEURL.URL}wpack`;
        } else {
          host = `${BASEURL.URL}wpack`;
        }
        window.location.href = `${host}/${token}/${companyId}`;
      }
    }
  };

  const redirectToCats = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}cats`;
      window.location.href = `${host}/${token}/${companyId}`;
      // if (host.includes("localhost")) {
      //   host = "localhost:3009";
      //   window.location.href = `http://${host}/${token}/${companyId}`;
      // } else if (host.includes("-dev-")) {
      //   host = CATS_URL.CATS.DEV;
      // } else if (host.includes("-qa-")) {
      //   host = CATS_URL.CATS.QA;
      // } else if (host.includes("-stage-")) {
      //   host = CATS_URL.CATS.STAGE;
      // } else {
      //   host = "http://twwstage.franklinreport.com/cats";
      //   window.location.href = `http://${host}/${token}/${companyId}`;
      // }
      // window.location.href = `${host}/${token}/${companyId}`;
    }
  };

  const navigateToNewCompany = () => {
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    localStorage.setItem("isSetNewCompany", true);
    history.push("/wizard");
    toggle();
  };

  return (
    <>
      <div className={classes.left_section_align}>
        <div className={classes.benjamin_logo}>
          <img src={returnStatusIcon()} style={{ width: "72px" }} />
        </div>
        <div>
          <Text size={12} family="Avenir_black_r" fontWeight={900}>
            GOOD CITIZEN STATUS
          </Text>
          <Row className={classes.points_wrap}>
            <Text size={12} family="avenir_roman" className={classes.points}>
              {returnPoints()}
            </Text>
            <Text
              size={12}
              family="Avenir_black_r"
              fontWeight={900}
              className={classes.very_good}
            >
              {returnStatus()}
            </Text>
          </Row>
        </div>
      </div>
    </>
  );
}

export default LoggedInHeaderStyles(CitizenPoints);
