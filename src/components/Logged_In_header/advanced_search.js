import React from "react";
import { Divider } from "@material-ui/core";

import Row from "../common/ui_kit/row";
import AdvancedSearchStyles from "./styles/advanced_search_styles";
import strings from "../../utilities/strings";
import Chevron_Down_Icon from "../data_display/icons/Arrow_Chevron_Down";
import CustomTextField from "../inputs/custom_text_area";
import CustomInputLabel from "../data_display/custom_label";
import CustomSelect from "../inputs/custom_select";
import { pxToRem } from "../../utilities/themes";
import { ProfileActions } from "../../redux/actions";
import { useHistory } from "react-router-dom";
import { useDispatch } from "react-redux";

function AdvancedSearch(props) {
  const { classes } = props;
  const history = useHistory();
  const dispatch = useDispatch();

  // useState for input fields
  const [searchInput, setSearchInput] = React.useState({
    titleNameCompany: "",
    cityStateZip: "",
  });

  // handle change for job title/company input
  const handleTitleNameCompanyChange = (e) => {
    const { value } = e.target;
    setSearchInput((prev) => ({
      ...prev,
      titleNameCompany: value,
    }));
  };

  // handle change for city/state/zip input
  const handleCityStateZipChange = (e) => {
    const { value } = e.target;
    setSearchInput((prev) => ({
      ...prev,
      cityStateZip: value,
    }));
  };

  // redirection to All Search
  const redirectToSearch = () => {
    if (
      searchInput &&
      (searchInput.titleNameCompany || searchInput.cityStateZip)
    ) {
      const dataToSubmit = {
        search_key: searchInput.titleNameCompany || "",
        search_location: searchInput.cityStateZip || "",
        category: "ALL",
      };
      dispatch(
        ProfileActions.searchInAll(dataToSubmit, () => {
          history.push("/global-search");
        })
      );
    }
  };

  return (
    <>
      <Row className={`${classes.people_search} ${classes.input_svg}`}>
        <img
          src="assets/images/search.png"
          className={classes.search_icon}
          alt="icon hat"
        />
        <CustomSelect
          IconComponent={Chevron_Down_Icon}
          className={classes.select_input}
          renderValue={(value) => value || "ALL"}
          value={"" || "ALL"}
          MenuProps={{
            getContentAnchorEl: null,
            disableScrollLock: true,
            anchorOrigin: {
              vertical: "bottom",
              horizontal: "left",
            },
          }}
        >
          {" "}
        </CustomSelect>
        <Row className={classes.people_search_block}>
          <CustomTextField
            className={classes.textarea_header}
            placeholder={strings.loggin_header.titles.job_title_name_company}
            value={searchInput.titleNameCompany}
            onBlur={handleTitleNameCompanyChange}
          />
        </Row>
        <Divider
          orientation="vertical"
          className={classes.second_people_search_divider}
        />
        <Row className={classes.people_search_block}>
          <CustomTextField
            className={classes.textarea_header}
            placeholder={strings.loggin_header.titles.city_state_zip}
            value={searchInput.cityStateZip}
            onBlur={handleCityStateZipChange}
          />
        </Row>
        <Row className={classes.advanced_search_block}>
          <Row className={classes.happy_arrow}>
            <img
              src="assets/icons/icon_happy_arrow.svg"
              alt={strings.loggin_header.titles.happy_arrow}
              onClick={redirectToSearch}
              style={{ cursor: "pointer" }}
            />
          </Row>
          {/* <CustomInputLabel
            className={classes.adv_search_label}
            is_input_label="true"
            label_name={strings.loggin_header.titles.advanced_search}
          /> */}
          <span
            style={{
              color: "#6297E1",
              fontSize: "10px",
              position: "relative",
              // top: "16px",
              fontFamily: "avenir_bold",
            }}
          >
            TO COME
          </span>
        </Row>
      </Row>
    </>
  );
}

export default AdvancedSearchStyles(AdvancedSearch);
