import React, { useState } from "react";
import Styles from "./styles/ArchStyles";
import { Button } from "@material-ui/core";

function AnimationArch(props) {
  const { classes } = props;
  const [values, setValues] = useState({
    currentPoint: 0,
  });
  const { currentPoint } = values;

  const triggerAnimation = (type) => (e) => {
    switch (type) {
      case "forward":
        if (values.currentPoint < 270) {
          setValues({ ...values, currentPoint: values.currentPoint + 30 });
        }
        break;
      case "backward":
        if (values.currentPoint > 0) {
          setValues({ ...values, currentPoint: values.currentPoint - 30 });
        }
        break;

      default:
        break;
    }
  };

  const getStrokeColor = () => {
    const { currentPoint } = values;
    if (Number(currentPoint) < 60) {
      return "#d03c43";
    }
    if (Number(currentPoint) < 120) {
      return "#99bf5b";
    }
    if (Number(currentPoint) < 180) {
      return "#fbd626";
    }
    if (Number(currentPoint) < 240) {
      return "#f58e4d";
    }
    if (Number(currentPoint) <= 300) {
      return "#128040";
    }
    return "#d03c43";
  };

  return (
    <>
      <div style={{ position: "relative" }}>
        {/* <h4 className={classes.points}>{values.currentPoint}</h4> */}
        <img src="assets/images/header_arch.png" style={{ width: "70px" }} />
        <div
          className={classes.parentDiv}
          style={{
            transform: `rotate(${currentPoint}deg`,
            transition: "transform 0.7s linear",
          }}
        >
          <div
            className={classes.child}
            style={{
              transform: `rotate(-${currentPoint}deg`,
              transition: "transform 0.7s linear",
            }}
          >
            <svg
              width="110"
              height="100"
              viewBox="0 0 650 600"
              style={{ position: "absolute" }}
            >
              <path
                d="M 50.000 70.000 L 74.511 82.361 L 69.021 56.180 L 88.042 37.639
                L 61.756 33.820 L 50.000 9.000 L 38.244 33.820 L 11.958 37.639
                L 30.979 56.180 L 26.489 82.361 L 50.000 70.000"
                stroke={getStrokeColor()}
                stroke-width="10"
                fill="white"
                style={{
                  // transform: `rotate(${currentPoint}deg`,
                  transition: "2.0s",
                }}
              />
            </svg>
          </div>
        </div>
        {/* <div style={{ position: "absolute", top: "60px", display: "flex" }}>
          <Button onClick={triggerAnimation("backward")}>backward</Button>
          <Button onClick={triggerAnimation("forward")}>Forward</Button>
        </div> */}
      </div>
    </>
  );
}

export default Styles(AnimationArch);
