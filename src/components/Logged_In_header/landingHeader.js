import React, { Component } from "react";
import { connect } from "react-redux";
import { <PERSON>rid, LinearProgress, Divider, Tabs, Tab } from "@material-ui/core";
import { withStyles } from "@material-ui/core/styles";
import _, { get } from "lodash";
import { color, font_sizes, pxToRem } from "../../utilities/themes";
import strings from "../../utilities/strings";
import { withRouter } from "react-router-dom";
// import MenuComponent from "./MenuComponent";
// import { LoginActions } from "../../redux/actions";
import Menu from "../layouts/Menu";
import { Row } from "../common/ui_kit";

import { ATS_URLS, BASEURL, TDW_URL, TOKEN_KEY } from "../../constants";
import MyCustomTooltip from "../inputs/custom_tooltip";
import Header from "./Logged_In_header";
import CitizenPoints from "./citizenPoints";
import FeedbackModal from "../modals/feedbacks/FeedBackModal";
import SelectCompanyModal from "../modals/feedbacks/selectCompanyModal";
import RedirectProfileModal from "../modals/feedbacks/redirectProfileModal";
import CustomModal from "../../components/inputs/custom_modal";
import SearchJobModal from "../modals/feedbacks/search_job_modal";
import TalentSearch from "../Logged_In_header/talent_search";
import VisitCommunity from "../Logged_In_header/visit_community";
import VisitCompany from "../Logged_In_header/visit_company";

// import { Row } from "../common/ui_kit";
/**
 * Common Header component across the application to be displayed to the user once the user logged in to the application
 */
const MenuItems = [
  {
    key: "myProfile",
    caption: "MY PROFILE",
    renderItem: null,
    redirect: "redirectToLpack",
    subMenuOptions: [{ name: "My Dream Job" }, { name: "My Skills" }],
  },
  {
    key: "share",
    caption: "Share Your Profile",
    renderItem: null,
    borderTop: true,
    redirect: "redirectToLpack",
  },
  {
    key: "resume",
    caption: "My Inventory",
    renderItem: null,
    redirect: "redirectToLpack",
  },
  {
    key: "wizard",
    caption: "Manage Wizard Boxes",
    renderItem: null,
    redirect: "redirectToLpack",
  },
  {
    key: "tricks",
    caption: "Tricks of the Trade",
    renderItem: null,
    redirect: "redirectToLpack",
  },
];

const searchItems = [
  {
    key: "allJobs",
    caption: "SEE ALL JOB LISTINGS",
    renderItem: null,
    redirect: "redirectJpack",
  },
  {
    key: "savedJobs",
    caption: "Saved Jobs",
    redirect: "redirectAts",
    renderItem: null,
    borderTop: true,
    subMenuOptions: [
      { name: "Job Searches & Job Alerts", noPadding: true },
      { name: "Recently Visited Jobs", noPadding: true },
    ],
  },
  {
    key: "searchJobs",
    caption: "KEYWORD JOB SEARCH",
    renderItem: null,
    borderTop: true,
    // redirect: "redirectJpack",
  },
  {
    key: "activeEmployers",
    caption: "ACTIVE EMPLOYERS",
    renderItem: null,
    borderTop: true,
  },
];

const journeyItems = [
  {
    key: "jobProgression",
    caption: "JOB PROGRESSION",
    renderItem: null,
    redirect: "redirectAts",
    subMenuOptions: [
      { name: "Now What?" },
      { name: "Applied Jobs" },
      { name: "Jobs Interviewing" },
      { name: "Offers Outstanding" },
      { name: "Offers Accepted" },
    ],
  },
  {
    key: "theGame",
    caption: "THE GAME",
    renderItem: null,
    redirect: "redirectAts",
    borderTop: true,
    subMenuOptions: [
      { name: "Job Challenges" },
      { name: "Power Play Badges" },
      { name: "My Coin Bank" },
    ],
  },
];

const postJobItems = [
  {
    key: "postJob",
    caption: "Post a Job",
    renderItem: null,
    redirect: "redirectWpack",
  },
  {
    key: "jobListings",
    caption: "Company Job Listings",
    renderItem: null,
    redirect: "redirectCats",
  },
  {
    key: "myCompanyProfile",
    caption: "MY COMPANY PROFILE",
    renderItem: null,
    redirect: "redirectCpack",
    borderTop: true,
  },
  {
    key: "myCompanyProfile",
    caption: "View Company Profile",
    renderItem: null,
    redirect: "redirectCpack",
    isOption: true,
  },
  {
    key: "goodCitizenStatus",
    caption: "Good Citizen Status",
    renderItem: null,
    redirect: "redirectCpack",
    isOption: true,
  },
  {
    key: "teamMembers",
    caption: "Team Members",
    renderItem: null,
    isOption: true,
    // redirect: "redirectCpack",
  },
  {
    key: "registerNewCompany",
    caption: "Register New Company",
    renderItem: null,
    isOption: true,
    // redirect: "redirectCpack",
  },
  // {
  //   key: "unfinishedDrafts",
  //   caption: "Unfinished Drafts",
  //   renderItem: null,
  //   redirect: "redirectWpack",
  // },
  // {
  //   key: "billingLog",
  //   caption: "Billing Log",
  //   renderItem: null,
  //   redirect: "redirectWpack",
  // },
  // {
  //   key: "jobMetrics",
  //   caption: "Job Metrics",
  //   renderItem: null,
  //   redirect: "redirectWpack",
  // },
];

const manageApplicantsItems = [
  {
    key: "manageApplicants",
    caption: "MANAGE APPLICANTS",
    renderItem: null,
    redirect: "redirectCats",
    subMenuOptions: [
      { name: "Dashboard - Your To Do List!" },
      { name: "Current Job Listings" },
      { name: "Your Applicants" },
      { name: "Applicants Messages" },
      { name: "Interview Schedule" },
    ],
  },
  {
    key: "discoverCandidates",
    caption: "DISCOVER CANDIDATES",
    renderItem: null,
    // redirect: "redirectCats",
    borderTop: true,
    // subMenuOptions: [{ name: "My Talent Alerts" }],
  },
  {
    key: "referCandidate",
    caption: "Refer a CANDIDATE",
    renderItem: null,
    // redirect: "redirectCats",
    borderTop: true,
    // subMenuOptions: [{ name: "My Talent Alerts" }],
  },
];

const companyProfileiItems = [
  {
    key: "viewPeople",
    caption: "VIEW OUR PEOPLE",
    renderItem: null,
    redirect: null,
  },
  {
    key: "viewCompanies",
    caption: "VIEW OUR COMPANIES",
    renderItem: null,
    redirect: null,
    borderTop: true,
  },
  // {
  //   key: "myCompanyProfile",
  //   caption: "View Company Profile",
  //   renderItem: null,
  //   redirect: "redirectCpack",
  // },
  // {
  //   key: "goodCitizenStatus",
  //   caption: "Good Citizen Status",
  //   renderItem: null,
  //   redirect: "redirectCpack",
  // },
  // {
  //   key: "teamMembers",
  //   caption: "Team Members",
  //   renderItem: null,
  //   // redirect: "redirectCpack",
  // },
  // {
  //   key: "registerNewCompany",
  //   caption: "Register New Company",
  //   renderItem: null,
  //   redirect: "redirectCpack",
  // },
];

const useStyles = {
  search_bar: {
    minWidth: pxToRem(100),
    marginTop: pxToRem(5),
    borderTop: pxToRem(0),
    borderLeft: pxToRem(0),
    height: pxToRem(24),
    textTransform: "uppercase",
    "& svg": {
      fontSize: pxToRem(10),
    },
  },
  search_pannel: {
    marginTop: pxToRem(5),
  },
  title_field_title: {
    "& input": {
      paddingLeft: pxToRem(2),
      width: pxToRem(200),
      textAlign: "center",
      // borderRight: `${pxToRem(0.5)} solid ${color.primary_palette.black}`,
    },
  },
  title_field_loc: {
    "& input": {
      paddingLeft: pxToRem(2),
      textAlign: "center",
    },
  },
  disable: {
    cursor: "not-allowed !important",
    opacity: "0.8 !important",
  },
  header_layout: {
    marginTop: "1rem",
    // padding: "0 20px",
    position: "relative",
    width: "100%",
    display: "flex",
    outline: "inherit",
    flexWrap: "wrap",
    boxSizing: "border-box",
    zIndex: 1,
  },
  header_big: (props) => ({
    position: "relative",
    top: 0,
    // borderBottom: `2px solid ${color.secondary_palette.grays.shadow_gray}`,
  }),
  header_mini: {
    top: 0,
    backgroundColor: "rgb(255, 255, 255)",
    opacity: 0,
    height: pxToRem(100),
    marginTop: 0,
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: `${pxToRem(2)} solid #D1D1D1`,
    position: "fixed",
    display: "none",
  },
  modal_popup: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  benjamin_logo: {
    width: "58.4px",
    height: "47.4px",
  },
  flip_logo: {
    width: "90px",
    height: "60px",
  },
  progressbar: {
    height: "4px",
  },
  linearProgess: {
    backgroundColor: "#979797" + "! important",
  },
  current_position: {
    fontFamily: "avenir_black_r",
    fontSize: font_sizes.font_12,
    fontWeight: "900",
    color: color.primary_palette.black,
    textTransform: "uppercase",
  },
  current_welcome: {
    fontFamily: "avenir_black_r",
    fontSize: font_sizes.font_13,
    fontWeight: "900",
    color: color.primary_palette.black,
    textTransform: "uppercase",
  },
  current_position_value: {
    marginLeft: "16px",
  },
  coin_icon: {
    width: "18px",
    height: "18px",
    VerticalAlign: "middle",
    marginTop: "16px",
  },
  homeIcon: {
    width: "15px",
    height: "12px",
  },
  homeIcon_number: {
    // position: "absolute",
    fontFamily: "avenir_black_r",
    fontSize: "12px",
    fontWeight: 900,
    left: "6px",
  },
  homeIconSection: {
    display: "flex",
    position: "relative",
    top: "26px",
    left: "-180px",
  },
  space: {
    margin: "0 10px",
    marginLeft: 0,
    marginTop: "12px",
  },
  header_right_block: {
    justifyContent: "flex-end",
    marginBottom: "4px",
  },
  iconAlign: {
    marginLeft: "0.3rem",
  },
  tw_icon: {
    width: "20.1px",
    height: "24px",
  },
  icon_hat: {
    width: "30px",
    height: "29px",
    opacity: "0.2",
  },
  flipbook_icon: {
    width: "35px",
    height: "35px",
    paddingTop: "5px",
  },
  fr_stamp: {
    width: "35px",
    height: "35px",
    paddingTop: "5px",
  },
  iconAlign: {
    marginLeft: "0.3rem",
    "& :hover": {
      background: "#c8dbf1",
      borderRadius: "7px",
    },
  },
  cart_icon: {
    opacity: "0.5",
    marginLeft: "3px",
  },
  img_cart: {
    width: "35px",
    height: "29px",
  },
  headings: {
    fontFamily: "gillsans_sb",
    fontSize: font_sizes.font_30,
    fontWeight: "600",
    color: color.primary_palette.franklin_purple,
  },
  divider: {
    width: "424px",
    height: "2px",
    backgroundColor: color.secondary_palette.grays.shadow_gray,
    marginTop: "10px",
  },
  divider1: {
    width: "540px",
    height: "2px",
    backgroundColor: color.secondary_palette.grays.shadow_gray,
    marginTop: "10px",
    marginLeft: "10px",
  },
  marign_top_tw_logo: {
    marginTop: `${pxToRem(35)} !important`,
    display: "flex",
    justifyContent: "center",
    cursor: "pointer",
  },
  tw_icon_center: {
    display: "flex",
    justifyContent: "center",
    borderBottom: `2px solid ${color.secondary_palette.grays.shadow_gray}`,
    cursor: "pointer",
  },
  left_section: {
    "& .MuiTabs-indicator": {
      height: "6px",
      // left: "190px !important",
      // width: "100px !important",
    },
  },
  left_section_align: {
    display: "flex",
    alignItems: "center",
    //marginBottom: '23.3px',
    height: "55px",
    marginBottom: "16px",
    paddingLeft: pxToRem(16),
  },
  tabs: {
    "& .MuiTab-wrapper": {
      color: color.primary_palette.black,
      fontSize: "14px",
      fontFamily: "gillsans_r",
    },
    "& .MuiButtonBase-root.Mui-disabled": {
      opacity: "0.2",
    },
    "& .MuiTabs-indicator": {
      height: "6px",
      // left: '190px !important',
      // width: '100px !important'
    },
  },
  tabs_mini: {
    "& .MuiTab-root": {
      minWidth: 0,
    },
  },
  profile_icon: {
    width: "40px",
    height: "40px",
    borderRadius: "5px !important",
  },
  people_search: {
    display: "flex",
    justifyContent: "center",
  },
  people_search_margin: {
    marginTop: pxToRem(165),
  },
  people_search_icon: {
    display: "flex",
    justifyContent: "flex-end",
    alignItems: "center",
    margin: "18px 2px 0 0",
  },
  people_search_block: {
    "& .MuiSelect-root": {
      paddingBottom: 0,
    },
    "label + .MuiInput-formControl": {
      marginTop: "0",
    },
    "& .MuiFormControl-root": {
      width: "100%",
    },
    "& .MuiInputLabel-root": {
      marginLeft: "15px",
      fontSize: "13px",
      color: "black",
      padding: "5px 0",
      fontWeight: "600",
    },
    "& .MuiInputBase-inputSelect": {
      padding: "3px",
    },
    "& .MuiSvgIcon-root": {
      padding: "4px",
      width: "9.5px",
    },
    "& .MuiTextField-root": {
      "& .MuiInputBase-input": {
        padding: "6px 0 0",
      },
      "& .MuiInputLabel-root": {
        fontFamily: "gillsans_light",
        fontSize: font_sizes.font_13,
        fontWeight: "300",
        color: color.secondary_palette.grays.shadow_gray,
      },
    },
  },
  first_people_search_divider: {
    marginTop: "30px",
    width: "1px",
    height: "11px",
    backgroundColor: color.primary_palette.black,
    opacity: "0.2",
  },
  second_people_search_divider: {
    margin: "25px 8px 0 9px",
    width: "1px",
    height: "25px",
    backgroundColor: color.primary_palette.black,
  },
  adv_search_label: {
    fontFamily: "gillsans_r",
    fontSize: font_sizes.font_14,
    color: color.primary_palette.franklin_purple,
  },
  advanced_search_block: {
    margin: "15px 0 0 10px",
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  happy_arrow: {
    marginRight: "5px",
    cursor: "pointer",
  },
  imgAlign: {
    paddingBottom: "15px",
  },
  activeUnderline: {
    backgroundColor: "red",
  },
  beta_logo: {
    // position: 'relative',
    position: "absolute",
    marginTop: "20px",
    right: "10px",
    // top: "0",
    width: "110px",
    cursor: "pointer",
  },
  beta_logo_tw: {
    // position: 'relative',
    position: "absolute",
    right: "59px",
    top: "88%",
    width: "150px",
    cursor: "pointer",
  },
  beta_pos: {
    // position: 'absolute'
    width: "100px",
    background: "white",
    borderRadius: "50%",
    cursor: "pointer",
    zIndex: 1,
  },

  tricksMenuTitle: {
    color: color.primary_palette.black,
    fontSize: font_sizes.font_18,
    margin: 0,
    padding: "5px 0",
    textTransform: "capitalize",
    borderBottom: `2px solid ${color.primary_palette.black}`,
    fontFamily: "American_Typewriter_Medium_BT",
  },
  shareMenuTitle: {
    color: color.primary_palette.franklin_purple,
    fontSize: font_sizes.font_20,
    margin: 0,
    padding: "5px 0",
    textTransform: "capitalize",
    borderBottom: `2px solid ${color.primary_palette.black}`,
  },
  chkbxLabelStyle: {
    marginLeft: "10px",
    fontSize: font_sizes.font_14,
    color: color.primary_palette.black,
    activeUnderline: {
      backgroundColor: "red",
    },
    checkBoxStyle: {
      "& .MuiButtonBase-root": {
        paddingTop: 0,
        paddingBottom: 0,
      },
    },
  },
  paperContainerStyle: {
    // top: "200px !important",
    marginTop: "35px",
    border: `1px solid ${color.form_colors.blueberry_purple}`,
    width: "200px",
    zIndex: 1,
    borderRadius: 0,
    "& .MuiList-root": {
      padding: 0,
      height: "100%",
    },
    "& .MuiListItem-root": {
      padding: "2px 4px 2px 12px",
      fontSize: font_sizes.font_14,
    },
    "& .MuiCheckbox-root": {
      paddingBottom: 0,
      paddingTop: 0,
    },
    "& .MuiFormControlLabel-root": {
      marginLeft: "-5px",
    },
    "& .MuiSvgIcon-root": {
      fontSize: "17px",
    },
    // "& p": {
    //   borderBottom: `1px solid ` + color.primary_palette.black,
    //   paddingBottom: "6px"
    // }
  },
  paperContainerStyleProfile: {
    // top: "200px !important",
    marginTop: "60px",
    border: `1px solid ${color.form_colors.blueberry_purple}`,
    width: "200px",
    zIndex: 1,
    borderRadius: 0,
    "& .MuiList-root": {
      padding: 0,
      height: "100%",
    },
    "& .MuiListItem-root": {
      padding: "2px 4px 2px 12px",
      fontSize: font_sizes.font_14,
    },
    "& .MuiCheckbox-root": {
      paddingBottom: 0,
      paddingTop: 0,
    },
    "& .MuiFormControlLabel-root": {
      marginLeft: "-5px",
    },
    "& .MuiSvgIcon-root": {
      fontSize: "17px",
    },
    "& .MuiListItem-button": {
      "&:hover": {
        backgroundColor: "#F4F0FF !important",
      },
    },
    // "& p": {
    //   borderBottom: `1px solid ` + color.primary_palette.black,
    //   paddingBottom: "6px"
    // }
  },
  navigationItems: {
    "& Span": {
      "&:hover": {
        color: color.warpstone_glow,
      },
    },
  },
  paperContainerStyleSearch: {
    // top: "200px !important",
    marginTop: "40px",
    border: `1px solid ${color.form_colors.blueberry_purple}`,
    width: "200px",
    zIndex: 1,
    borderRadius: 0,
    "& .MuiList-root": {
      padding: 0,
      height: "100%",
    },
    "& .MuiListItem-root": {
      padding: "2px 4px 2px 12px",
      fontSize: font_sizes.font_14,
    },
    "& .MuiCheckbox-root": {
      paddingBottom: 0,
      paddingTop: 0,
    },
    "& .MuiFormControlLabel-root": {
      marginLeft: "-5px",
    },
    "& .MuiSvgIcon-root": {
      fontSize: "17px",
    },
    "& .MuiListItem-button": {
      "&:hover": {
        backgroundColor: "#F4F0FF !important",
      },
    },
    // "& p": {
    //   borderBottom: `1px solid ` + color.primary_palette.black,
    //   paddingBottom: "6px"
    // }
  },
  paperContainerStyleJourney: {
    // top: "200px !important",
    marginTop: "88px",
    border: `1px solid ${color.form_colors.blueberry_purple}`,
    width: "200px",
    zIndex: 1,
    borderRadius: 0,
    "& .MuiList-root": {
      padding: 0,
      height: "100%",
    },
    "& .MuiListItem-root": {
      padding: "2px 4px 2px 12px",
      fontSize: font_sizes.font_14,
    },
    "& .MuiCheckbox-root": {
      paddingBottom: 0,
      paddingTop: 0,
    },
    "& .MuiFormControlLabel-root": {
      marginLeft: "-5px",
    },
    "& .MuiSvgIcon-root": {
      fontSize: "17px",
    },
    "& .MuiListItem-button": {
      "&:hover": {
        backgroundColor: "#F4F0FF !important",
      },
    },
    // "& p": {
    //   borderBottom: `1px solid ` + color.primary_palette.black,
    //   paddingBottom: "6px"
    // }
  },
  trickItemStyle: {
    fontSize: font_sizes.font_14,
    padding: "3px 0",
    cursor: "pointer",
  },
  wizardMenuTitle: {
    fontFamily: "ACaslonPro",
    fontSize: font_sizes.font_22,
    fontStyle: "italic",
    paddingBottom: "0 !important",
    paddingTop: "5px",
  },
  copy_clipboard: {
    marginRight: pxToRem(15),
    width: pxToRem(20),
    height: pxToRem(20),
    background: 'url("assets/images/copy.PNG")',
    backgroundSize: "cover",
    "&:hover": {
      background: 'url("assets/images/copy_hover.PNG")',
      backgroundSize: "cover",
    },
  },
  pos_relative: {
    position: "relative",
  },
  fixed: {
    position: "fixed",
  },
  relative: {
    position: "relative",
  },
  dragon_position: {
    position: "absolute",
    top: 0,
    left: 0,
  },
  logout_position: {
    position: "absolute",
    top: 0,
    right: 0,
    width: "auto !important",
  },
  animateItem: {
    "&:after": {
      content: "2000",
      animation: "$animateCoin 3000ms infinite forwards",
    },
  },
  span_item: {
    width: pxToRem(2),
    height: pxToRem(30),
    backgroundColor: "black",
  },
  blk_div: {
    display: "flex",
    alignItems: "center",
  },
  "@keyframes animateCoin": {
    "0%": { bottom: "0px", opacity: 0.8 },
    "50%": { bottom: "50px", opacity: 0.5 },
    "75%": {
      bottom: "20px",
      opacity: 0.2,
      content: (props) => _.get(props.gamification, "totalEarned", 0),
    },
    "100%": {
      bottom: "0px",
      content: (props) => _.get(props.gamification, "totalEarned", 0),
    },
  },
};

const isProfilePage = (props) => {
  return _.includes(_.get(props.location, "pathname"), "profile");
};

class LoggedInHeader extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: null,
      selected: 0,
      anchorEl: null,
      searchAnchorEl: null,
      JourneyAnchorEl: null,
      postJobAnchorEl: null,
      manageApplicantAnchorEl: null,
      companyProfileAnchorEl: null,
      openModal: false,
      showTalentSearchModal: false,
      imgSrcPhoto: "assets/images/Lil-beta-stamp.png",
      showDreamJobModal: false,
      showVisitCommunityModal: false,
      showVisitCompanyModal: false,
      showreferCandidate: false,
      showSkillsDashboard: false,
      companyListModal: false,
      redirectProfileModal: false,
      showSearchJobModal: false,
      showAddCompanyModal: false,
      showAddJobCompanyModal: false,
      showRequestRecommendationModal: false,
      openFeedBackModal: false,
      clikedContent: "",
      resumeModal: false,
      showGamificationModal: false,
      selectedSearchItem: "All",
      searchInput: {
        cityStateZip: "",
        titleNameCompany: "",
      },
    };
    this.inputRef = React.createRef();
    this.coinImageRef = React.createRef();
    this.miniCoinImageRef = React.createRef();
  }

  toggleListModal = (content) => () => {
    this.setState({ openModal: !this.state.openModal, clikedContent: content });
  };

  toggleVisitCommunityModal = () => {
    this.setState({
      showVisitCommunityModal: !this.state.showVisitCommunityModal,
    });
  };

  toggleVisitCompanyModal = () => {
    this.setState({
      showVisitCompanyModal: !this.state.showVisitCompanyModal,
    });
  };

  toggleReferCandidate = () => {
    this.setState({
      showreferCandidate: !this.state.showreferCandidate,
    });
  };

  toggleInventoryModal = (content) => () => {
    if (content === "My Resumes & Elevator Pitch") {
      this.toggleResumeModal();
    } else if (content === "My Recommendations") {
      this.toggleRequestRecommendationModal();
    } else if (content === "My Full Inventory") {
      this.redirectToAts();
    }
    this.handleClose();
  };

  toggleFeedBackModal = () => {
    localStorage.setItem("openFeedbaack", false);
    this.setState({ openFeedBackModal: !this.state.openFeedBackModal });
  };

  printData = () => {
    var printContents = document.getElementById("printDoc").outerHTML;
    var originalContents = document.body.innerHTML;
    document.body.innerHTML = printContents;
    window.print();
    document.body.innerHTML = originalContents;
    window.location.reload();
  };

  handle_on_link_click = () => {};

  handle_home_click = () => {
    this.props.history.push("/");
  };
  /**
   * Highlighting the link color on click of menu
   */
  handle_menu_change = (event, newValue) => {
    const { userData } = this.props;
    if (userData) {
      if ((newValue === 2 || newValue === 4) && !userData.userProfileInfo) {
        this.toggleRedirectProfileModal();
        return;
      }
    }
    if (newValue === 2) {
      this.redirectToLpack();
      return;
    }
    if (newValue === 4) {
      this.redirectToAts();
      return;
    }
    this.setState({
      value:
        newValue === 0 ? newValue : newValue === 2 ? 1 : newValue === 4 ? 2 : 0,
      anchorEl: newValue === 2 ? event.currentTarget : null,
      searchAnchorEl: newValue === 0 ? event.currentTarget : null,
      JourneyAnchorEl: newValue === 4 ? event.currentTarget : null,
      selected: null,
    });
  };

  handleClose = () => {
    this.setState({ anchorEl: null });
  };
  handleJourneyClose = () => {
    this.setState({ JourneyAnchorEl: null });
  };
  handleSearchClose = () => {
    this.setState({ searchAnchorEl: null });
  };
  handlePostJobClose = () => {
    this.setState({ postJobAnchorEl: null });
  };
  handleApplicantClose = () => {
    this.setState({ manageApplicantAnchorEl: null });
  };
  handleCompanyProfileClose = () => {
    this.setState({ companyProfileAnchorEl: null });
  };
  toggleResumeModal = () => {
    this.setState({
      resumeModal: !this.state.resumeModal,
    });
  };
  closeSkillsModal = () => {
    this.setState({ showSkillsDashboard: false });
  };
  toggleDreamJobModal = () => {
    this.setState({ showDreamJobModal: !this.state.showDreamJobModal });
  };
  toggleSkillsModal = () => {
    this.setState({ showSkillsDashboard: !this.state.showSkillsDashboard });
  };
  toggleSearchJobModal = () => {
    this.setState({ showSearchJobModal: !this.state.showSearchJobModal });
  };
  toggleCompanyListModal = () => {
    this.setState({ companyListModal: !this.state.companyListModal });
  };
  toggleRedirectProfileModal = () => {
    this.setState({ redirectProfileModal: !this.state.redirectProfileModal });
  };
  toggleAddCompanyModal = () => {
    this.setState({ showAddCompanyModal: !this.state.showAddCompanyModal });
  };
  toggleAddCompanyJobModal = () => {
    this.setState({
      showAddJobCompanyModal: !this.state.showAddJobCompanyModal,
    });
  };
  toggleRequestRecommendationModal = () => {
    this.setState({
      showRequestRecommendationModal:
        !this.state.showRequestRecommendationModal,
    });
  };

  navigateToNewCompany = () => {
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    localStorage.removeItem("newCompanyId");
    // localStorage.removeItem("companyName");
    localStorage.setItem("isSetNewCompany", true);
    this.props.history.push("/wizard");
    this.handleCompanyProfileClose();
  };

  mouseOut = () => {
    this.setState({
      imgSrcPhoto: "assets/images/Lil-beta-stamp.png",
    });
  };

  mouseOver = () => {
    this.setState({
      imgSrcPhoto: "assets/images/Stamp (Black).png",
    });
  };

  redirectToWpack = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    localStorage.removeItem("isSetNewCompany");
    localStorage.removeItem("isFromCats");
    localStorage.setItem("isPostNewJob", true);
    let companyId =
      localStorage.getItem("newCompanyId") || localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      // host = "twwstage.franklinreport.com/wpack";
      // window.location.href = `http://${host}/${token}/${companyId}`;
      if (host.includes("localhost")) {
        host = "localhost:3003";
        window.location.href = `http://${host}/${token}/${companyId}`;
      } else {
        if (host.includes("-dev-")) {
          host = TDW_URL.WPACK.DEV;
        }
        if (host.includes("-qa-")) {
          host = TDW_URL.WPACK.QA;
        }
        if (host.includes("-stage-")) {
          host = TDW_URL.WPACK.STAGE;
        }
        if (host.includes("-twwstage-")) {
          host = `${BASEURL.URL}wpack`;
          // window.location.href = `http://${host}/${token}/${companyId}`;
        } else {
          host = `${BASEURL.URL}wpack`;
        }
        window.location.href = `${host}/${token}/${companyId}`;
      }
    }
  };

  redirectToLpack = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("companyId");
    localStorage.removeItem("newCompanyId");
    localStorage.removeItem("companyName");
    localStorage.removeItem("isSetNewCompany");
    let token = localStorage.getItem("tradeworks_user_token");
    if (host.includes("localhost")) {
      host = "localhost:3001";
      window.location.href = `http://${host}/lpack/profile/${token}`;
    } else {
      host = BASEURL.URL;
      window.location.href = `${host}lpack/lpack/profile/${token}`;
    }
  };

  navigateToNewCompany = () => {
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    localStorage.removeItem("newCompanyId");
    localStorage.setItem("isSetNewCompany", true);
    this.props.history.push("/wizard");
    this.handleCompanyProfileClose();
  };

  redirectToActiveEmployers = () => {
    const token = localStorage.getItem(TOKEN_KEY);
    let host = get(window, "location.host", "");
    let route = "";
    if (host) {
      if (host.includes("localhost")) {
        route = "http://localhost:3006/";
      } else {
        route = `${BASEURL.URL}jpack/`;
      }
    }
    window.location.href = `${route}auth/${token}/activeEmployers`;
  };

  // redirection to settings page
  redirectToSettings = (menuoption = "") => {
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let host = `${BASEURL.URL}settings/adminAccess`;
    window.location.href = `${host}/${token}/${companyId}`;
  };

  onMenuSelected = (data) => {
    localStorage.setItem("selectedMenuItem", data.redirect);
    if (data.key === "Register New Company") {
      this.navigateToNewCompany();
    }
    if (data.key === "viewPeople") {
      this.toggleVisitCommunityModal();
    }
    if (data.key === "viewCompanies") {
      this.toggleVisitCompanyModal();
    }
    if (data.key === "teamMembers") {
      this.redirectToSettings();
    }
    if (data.key === "registerNewCompany") {
      this.navigateToNewCompany();
    }
    if (data.redirect === "redirectJpack") {
      this.redirectToJpack();
    }
    if (data.key === "discoverCandidates") {
      this.toggleTalentSearchModal();
    }
    if (data.key === "referCandidate") {
      this.toggleReferCandidate();
    }
    if (data.key === "searchJobs") {
      this.toggleSearchJobModal();
    }
    if (data.key === "activeEmployers") {
      this.redirectToActiveEmployers();
    }
    if (data.key === "registerNewCompany") {
      this.navigateToNewCompany();
    }
    if (data.redirect === "redirectAts") {
      this.redirectToAts();
    }
    if (data.redirect === "redirectWpack") {
      // this.toggleCompanyListModal();
      this.redirectToWpack();
    }
    if (data.redirect === "redirectCpack") {
      this.redirectToCpack();
    }
    if (data.redirect === "redirectCats") {
      // this.toggleCompanyListModal();
      this.redirectToCats();
    }
    if (data.redirect === "redirectToLpack") {
      this.redirectToLpack();
    }
    this.handleClose();
  };

  redirectToCats = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId =
      localStorage.getItem("newCompanyId") || localStorage.getItem("companyId");
    localStorage.removeItem("isSetNewCompany");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}cats`;
      window.location.href = `${host}/${token}/${companyId}`;
    }
  };

  redirectToAts = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("isSetNewCompany");
    const token = localStorage.getItem(TOKEN_KEY);
    localStorage.removeItem("companyId");
    if (!token) {
      return;
    }
    if (host) {
      if (host.includes("localhost")) {
        host = ATS_URLS.local;
        window.location.href = `http://${host}/auth/${token}`;
      } else {
        if (host.includes("-dev-")) {
          host = ATS_URLS.dev;
          window.location.href = `https://${host}/auth/${token}`;
        } else if (host.includes("-qa-")) {
          host = ATS_URLS.qa;
          window.location.href = `https://${host}/auth/${token}`;
        } else if (host.includes("-stage-")) {
          host = ATS_URLS.stage;
          window.location.href = `https://${host}/auth/${token}`;
        } else {
          host = `${BASEURL.URL}ats`;
          // host = "twwstage.franklinreport.com/ats";
          window.location.href = `${host}/auth/${token}`;
        }
      }
    }
  };

  redirectToCpack = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("jobId");
    let token = localStorage.getItem(TOKEN_KEY);
    if (host.includes("localhost")) {
      host = "localhost:3002";
      window.location.href = `http://${host}/${token}`;
    } else {
      host = `${BASEURL.URL}cpack/wizard`;
      // host = "http://twwstage.franklinreport.com/cpack/wizard";
      window.location.href = `${host}/${token}`;
    }
    // }
  };

  toggleTalentSearchModal = () => {
    this.setState({
      showTalentSearchModal: !this.state.showTalentSearchModal,
    });
  };

  handle_menu_change_right = (event, newValue) => {
    // if (newValue === 4) {
    //   this.toggleVisitCommunityModal();
    //   return;
    // }
    if (_.get(this.props, "CompanyListByUser.length", 0) === 0) {
      if (newValue === 0) {
        this.redirectToCpack();
        return;
      } else if (newValue === 2) {
        this.toggleAddCompanyModal();
        return;
      } else if (newValue === 4) {
        this.toggleAddCompanyJobModal();
        return;
      }
    }
    if (_.get(this.props, "CompanyListByUser.length", 0) === 1) {
      localStorage.setItem(
        "newCompanyId",
        _.get(this.props, "CompanyListByUser[0]._id", "")
      );
    }
    this.setState({
      selected:
        newValue === 0 ? newValue : newValue === 2 ? 1 : newValue === 4 ? 2 : 0,
      manageApplicantAnchorEl: newValue === 2 ? event.currentTarget : null,
      postJobAnchorEl: newValue === 0 ? event.currentTarget : null,
      companyProfileAnchorEl: newValue === 4 ? event.currentTarget : null,
      value: null,
    });
  };

  changeModal = (clikedContent) => () => {
    this.setState({ clikedContent });
  };

  openSocialMedia = (type) => () => {
    const { socialmedia } = this.props;
    const url = _.get(socialmedia, type);
    url && window.open(`//${url}`);
  };

  copyToClipboard = () => {
    const input = this.inputRef.current;
    input.select();
    input.setSelectionRange(0, 99999); /*For mobile devices*/
    window.document.execCommand("copy");
  };

  handleMenuClick = (selectedItem) => {
    if (selectedItem === "Logout") {
      let host = get(window, "location.host", "");
      host = BASEURL.URL;
      // host = "twwstage.franklinreport.com";
      window.location.href = `${host}logout`;
    }
  };

  renderMenuSubItem = () => {
    return MenuItems.map((item) => {
      return item;
    });
  };

  renderSearchSubItem = () => {
    return searchItems.map((item) => {
      return item;
    });
  };

  renderJourneySubItem = () => {
    return journeyItems.map((item) => {
      return item;
    });
  };

  renderPostJobSubItem = () => {
    return postJobItems.map((item) => {
      return item;
    });
  };

  rendermanageApplicantSubItem = () => {
    return manageApplicantsItems.map((item) => {
      return item;
    });
  };

  companyProfileSubItem = () => {
    return companyProfileiItems.map((item) => {
      return item;
    });
  };

  getInventoryList = () => {
    return [
      "My Resumes & Elevator Pitch",
      "My Cover Letters",
      "My Recommendations",
      "My Flipbook - TO COME",
      "My Full Inventory",
    ];
  };

  startCoinAnimation = (imageRef) => {
    const imageTag = imageRef.current;
    if (imageTag) {
      imageTag.src = "assets/images/coin_animation.gif";
      imageTag.style.width = "50px";
      imageTag.style.height = "unset";
      imageTag.style.marginTop = "-25px";
      setTimeout(() => {
        imageTag.src = "assets/icons/coin.svg";
        imageTag.style.width = "18px";
        imageTag.style.height = "18px";
        imageTag.style.marginTop = "10px";
      }, 7000);
    }
  };

  componentDidUpdate(prevProps) {
    if (
      _.get(prevProps, "gamification.totalEarned") !==
      _.get(this.props, "gamification.totalEarned")
    ) {
      this.startCoinAnimation(this.coinImageRef);
      this.startCoinAnimation(this.miniCoinImageRef);
    }
  }

  selectHeaderItem = (item) => () => {
    this.setState({ selectedSearchItem: item });
  };

  handleSearchChange = (e) => {
    const { name, value } = e.target;
    const { searchInput } = this.state;
    searchInput[name] = value;
    this.setState({ searchInput });
  };

  // handles search redirection to other applications
  handleSearch = () => {
    switch (this.state.selectedSearchItem) {
      case "All":
        break;
      case "jobtitle":
        this.redirectToJpack();
        break;

      default:
        break;
    }
  };

  // redirection to job posts
  redirectToJpack = () => {
    const { searchInput } = this.state;
    localStorage.removeItem("companyId");
    const token = localStorage.getItem(TOKEN_KEY);
    let host = get(window, "location.host", "");
    localStorage.removeItem("isSetNewCompany");
    let route = "";
    if (host) {
      route = `${BASEURL.URL}jpack/`;
      // route = "http://twwstage.franklinreport.com/jpack/";
    }
    window.location.href = `${route}auth/${token}/${
      searchInput.titleNameCompany || null
    }/${searchInput.cityStateZip || null}/false`;
  };

  render() {
    const {
      value,
      selected,
      anchorEl,
      searchAnchorEl,
      JourneyAnchorEl,
      postJobAnchorEl,
      manageApplicantAnchorEl,
      companyProfileAnchorEl,
      showTalentSearchModal,
      openFeedBackModal,
      companyListModal,
      showSearchJobModal,
      redirectProfileModal,
      showVisitCommunityModal,
      showVisitCompanyModal,
      showreferCandidate,
      imgSrcPhoto,
    } = this.state;
    const {
      userinfo,
      profileUserData,
      firstImpression,
      gamification,
      userData,
      CompanyListByUser,
      jobListing,
      trades,
      topLocations,
    } = this.props;
    const { classes } = this.props;
    const float = isProfilePage(this.props);

    return (
      <>
        <div className={classes.pos_relative}>
          <div
            id="header_big"
            className={`${classes.header_layout} ${classes.header_big}`}
          >
            <Grid
              item
              xs={5}
              className={classes.left_section}
              id="left_section"
            >
              <Grid
                container
                className={`${classes.dragon_position} ${classes.left_section_align}`}
              >
                <CitizenPoints />
              </Grid>
              <div id="getFound">
                <Grid
                  item
                  container
                  justify="center"
                  className={classes.headings}
                  style={{ marginTop: pxToRem(59) }}
                >
                  {strings.loggin_header.titles.get_found}
                </Grid>
                <Divider variant="inset" className={classes.divider} />
              </div>
              <Grid
                container
                item
                justify="center"
                alignItems="center"
                id="tabsLeft"
                style={{
                  borderBottom: `2px solid ${color.secondary_palette.grays.shadow_gray}`,
                }}
              >
                <Tabs
                  value={value}
                  indicatorColor="primary"
                  textColor="primary"
                  onChange={this.handle_menu_change}
                  className={classes.tabs}
                >
                  <Tab
                    label={strings.loggin_header.titles.search_jobs}
                    className={classes.navigationItems}
                  />
                  <Menu
                    open={Boolean(searchAnchorEl)}
                    PaperProps={{
                      className: classes.paperContainerStyleSearch,
                    }}
                    onMenuSelected={this.onMenuSelected}
                    menuItems={this.renderSearchSubItem()}
                    anchorElement={searchAnchorEl}
                    onClose={this.handleSearchClose}
                  />
                  <Tab
                    label={strings.loggin_header.titles.my_profile}
                    className={classes.navigationItems}
                  />
                  <Menu
                    open={Boolean(anchorEl)}
                    PaperProps={{
                      className: classes.paperContainerStyleProfile,
                    }}
                    onMenuSelected={this.onMenuSelected}
                    menuItems={this.renderMenuSubItem()}
                    anchorElement={anchorEl}
                    onClose={this.handleClose}
                  />
                  <Tab
                    label={strings.loggin_header.titles.active_employers}
                    className={classes.navigationItems}
                  />
                  <Menu
                    open={Boolean(JourneyAnchorEl)}
                    PaperProps={{
                      className: classes.paperContainerStyleJourney,
                    }}
                    onMenuSelected={this.onMenuSelected}
                    menuItems={this.renderJourneySubItem()}
                    anchorElement={JourneyAnchorEl}
                    onClose={this.handleJourneyClose}
                  />
                </Tabs>
              </Grid>
            </Grid>
            <Grid
              id="headerLogo"
              item
              xs={2}
              // className={this.props.match.path === "/tw" ? classes.marign_top_tw_logo : classes.tw_icon_center}
              className={classes.tw_icon_center}
              style={{ marginTop: "-1px" }}
              onClick={this.handle_home_click}
            >
              <img
                className={classes.imgAlign}
                src="assets/images/trade-works.png"
                alt="TW"
              />
            </Grid>
            <Grid item xs={5} id="right_section">
              <Grid
                container
                className={`${
                  float ? classes.logout_position : classes.logout_position
                } ${classes.header_right_block}`}
              >
                {(localStorage.getItem("tradeworks_user_token") !== "null" ||
                  localStorage.getItem("tradeworks_user_token") !== null) && (
                  <Row>
                    <Row direction="column">
                      <Row justify="flex-end">
                        <Grid item className={classes.current_welcome}>
                          <span>{strings.loggin_header.titles.welcome}</span>
                          <span>
                            &nbsp;
                            {localStorage.getItem("isSetNewCompany") !==
                              "true" &&
                            localStorage.getItem("companyId") !== null &&
                            localStorage.getItem("companyId") !== "" &&
                            localStorage.getItem("companyName") !== ""
                              ? localStorage.getItem("companyName")
                              : (_.get(firstImpression, "firstName", "") ||
                                  _.get(profileUserData, "firstName", "") ||
                                  _.get(userData, "firstName", "")) +
                                " " +
                                (_.get(firstImpression, "lastName", "") ||
                                  _.get(profileUserData, "lastName", "") ||
                                  _.get(userData, "lastName", ""))}
                            ! &nbsp;
                          </span>
                        </Grid>
                      </Row>
                      <Grid
                        container
                        item
                        xs
                        alignItems="center"
                        justify="flex-end"
                      >
                        <Grid
                          item
                          justify="center"
                          className={classes.iconAlign}
                        >
                          <MyCustomTooltip
                            title={
                              <>
                                <img
                                  src="assets/images/FLipbook Logo.svg"
                                  className={classes.flip_logo}
                                  alt="benjamin_apprentice"
                                />
                              </>
                            }
                            height="60px"
                            width="90px"
                            arrow
                          >
                            <div
                              style={{
                                height: "45px",
                                width: "45px",
                                textAlign: "center",
                              }}
                            >
                              <img
                                src="assets/images/new_flipbook.png"
                                className={classes.flipbook_icon}
                                alt="flipbook"
                              />
                            </div>
                          </MyCustomTooltip>
                        </Grid>
                        <Grid item className={classes.iconAlign}>
                          <MyCustomTooltip
                            title="Visit Franklin Report for client reviews of companies"
                            arrow
                            width="100px"
                            height="40px"
                          >
                            <div
                              className="iconAlign"
                              style={{
                                height: "45px",
                                width: "45px",
                                textAlign: "center",
                              }}
                            >
                              <img
                                src="assets/icons/fr_stamp.svg"
                                className={classes.fr_stamp}
                                alt="flipbook"
                              />
                            </div>
                          </MyCustomTooltip>
                        </Grid>
                        {/* <Grid item>
                      <img
                        className={classes.img_cart}
                        src="assets/icons/icon_shopping_cart.svg"
                        alt={strings.image_text.alt_text.cart_icon}
                      />
                    </Grid> */}
                        {/* <Grid item>
                      <Header />
                    </Grid> */}
                      </Grid>
                    </Row>
                    <Grid item>
                      <Header
                        toggleVisitCommunityModal={
                          this.toggleVisitCommunityModal
                        }
                      />
                    </Grid>
                  </Row>
                )}
                {/* <Grid item style={{ marginLeft: "10px" }}>
                  <Grid container>
                    <MenuComponent
                      image={_.get(userinfo, "profileImg.square")}
                      options={["Logout"]}
                      callBackFunction={(item) => {
                        this.handleMenuClick(item);
                      }}
                    />
                  </Grid>
                </Grid> */}
              </Grid>
              <div
                id="findTalent"
                style={{ marginTop: float ? pxToRem(59) : pxToRem(59) }}
              >
                <Grid
                  item
                  container
                  justify="center"
                  className={classes.headings}
                >
                  {strings.loggin_header.titles.find_talent}
                </Grid>
                <Divider variant="inset" className={classes.divider1} />
              </div>
              <Grid
                container
                item
                justify="center"
                alignItems="center"
                id="tabsRight"
                style={{
                  borderBottom: `2px solid ${color.secondary_palette.grays.shadow_gray}`,
                  // marginTop: pxToRem(4)
                }}
              >
                <Tabs
                  className={classes.tabs}
                  value={selected}
                  indicatorColor="primary"
                  textColor="primary"
                  onChange={this.handle_menu_change_right}
                >
                  <Tab
                    label={strings.loggin_header.titles.post_jobs}
                    className={classes.navigationItems}
                  />
                  <Menu
                    open={Boolean(postJobAnchorEl)}
                    PaperProps={{ className: classes.paperContainerStyle }}
                    onMenuSelected={this.onMenuSelected}
                    menuItems={this.renderPostJobSubItem()}
                    anchorElement={postJobAnchorEl}
                    onClose={this.handlePostJobClose}
                  />
                  <Tab
                    label={strings.loggin_header.titles.manage_applicants}
                    className={classes.navigationItems}
                  />
                  <Menu
                    open={Boolean(manageApplicantAnchorEl)}
                    PaperProps={{
                      className: classes.paperContainerStyleJourney,
                    }}
                    onMenuSelected={this.onMenuSelected}
                    menuItems={this.rendermanageApplicantSubItem()}
                    anchorElement={manageApplicantAnchorEl}
                    onClose={this.handleApplicantClose}
                  />
                  <Tab
                    label={strings.loggin_header.titles.discover_candidates}
                    className={classes.navigationItems}
                  />
                  <Menu
                    open={Boolean(companyProfileAnchorEl)}
                    PaperProps={{ className: classes.paperContainerStyle }}
                    onMenuSelected={this.onMenuSelected}
                    menuItems={this.companyProfileSubItem()}
                    anchorElement={companyProfileAnchorEl}
                    onClose={this.handleCompanyProfileClose}
                  />
                </Tabs>
              </Grid>
            </Grid>
          </div>

          <div
            id="header_mini"
            className={`${classes.header_layout} ${classes.header_mini}`}
            style={{
              marginTop: 0,
              justifyContent: "space-between",
              flexWrap: "nowrap",
            }}
          >
            <div
              className={classes.left_section_align}
              style={{ width: "24%" }}
            >
              <Grid container style={{ width: "auto" }} alignItems="center">
                <Grid>
                  <MyCustomTooltip
                    title="Hello. I am Benjamin. I will give you TradeCoins as
                    you progress. Applicants who reach 1000 Coins have
                    found their Dream Job 98% of the time."
                    width="300px"
                    arrow
                  >
                    <img
                      src="assets/icons/benjamin_baby.svg"
                      className={classes.benjamin_logo}
                      alt="benjamin_apprentice"
                    />
                  </MyCustomTooltip>
                </Grid>
                <div>
                  <Row align="center">
                    <Grid item className={classes.space}>
                      <span className={classes.current_position}>
                        <span>
                          {strings.loggin_header.titles.current_position}
                        </span>
                        <span className={classes.current_position_value}>
                          {_.get(gamification, "totalEarned", 0)}
                        </span>
                      </span>
                      <div className={classes.progressbar}>
                        <LinearProgress
                          className={classes.linearProgess}
                          variant="determinate"
                          value={_.get(gamification, "totalEarned", 0)}
                        />
                      </div>
                    </Grid>
                    <Grid item>
                      <img
                        ref={this.miniCoinImageRef}
                        src="assets/icons/coin.svg"
                        alt="coin_icon"
                        className={classes.coin_icon}
                      />
                    </Grid>
                  </Row>
                  <div>
                    <img
                      src="assets/images/home.PNG"
                      className={classes.homeIcon}
                      alt="home"
                    />
                    <span className={classes.homeIcon_number}>6</span>
                  </div>
                </div>
              </Grid>
            </div>
            <Row>
              <Tabs
                value={value}
                indicatorColor="primary"
                textColor="primary"
                variant="fullWidth"
                onChange={this.handle_menu_change}
                className={`${classes.tabs} ${classes.tabs_mini}`}
                style={{ alignItems: "center" }}
              >
                <Tab
                  label={strings.loggin_header.titles.search_jobs}
                  disabled
                  wrapped
                />
                <Tab label={strings.loggin_header.titles.my_profile} wrapped />
                <Menu
                  open={Boolean(anchorEl)}
                  PaperProps={{ className: classes.paperContainerStyleProfile }}
                  onMenuSelected={this.onMenuSelected}
                  menuItems={this.renderMenuSubItem()}
                  anchorElement={anchorEl}
                  onClose={this.handleClose}
                />
                <Tab
                  label={strings.loggin_header.titles.active_employers}
                  disabled
                  wrapped
                />
              </Tabs>
              <img
                src="assets/icons/trade_works_icon_small.svg"
                alt="tw"
                style={{ width: pxToRem(75) }}
              />
              <Tabs
                className={`${classes.tabs} ${classes.tabs_mini}`}
                value={selected}
                indicatorColor="primary"
                variant="fullWidth"
                textColor="primary"
                onChange={this.handle_menu_change_right}
                style={{ alignItems: "center" }}
              >
                <Tab label={strings.loggin_header.titles.post_jobs} disabled />
                <Tab
                  label={strings.loggin_header.titles.manage_applicants}
                  disabled
                />
                <Tab
                  label={strings.loggin_header.titles.discover_candidates}
                  disabled
                  wrapped
                />
              </Tabs>
            </Row>
            <div style={{ width: "20%" }}>
              <Row className={classes.header_right_block}>
                <Row direction="column" style={{ width: "72%" }}>
                  <Grid
                    item
                    className={classes.current_welcome}
                    style={{ textAlign: "right" }}
                  >
                    <span>{strings.loggin_header.titles.welcome}</span>
                    <span>
                      &nbsp;
                      {firstImpression
                        ? _.get(firstImpression, "firstName", "")
                        : _.get(userinfo, "firstName", "")}
                      !
                    </span>
                  </Grid>
                  <Grid
                    container
                    item
                    xs
                    alignItems="flex-end"
                    justify="flex-end"
                    style={{ paddingBottom: pxToRem(6) }}
                  >
                    <Grid item justify="center" className={classes.iconAlign}>
                      <MyCustomTooltip
                        title={
                          <>
                            <img
                              src="assets/images/FLipbook Logo.svg"
                              className={classes.flip_logo}
                              alt="benjamin_apprentice"
                            />
                          </>
                        }
                        height="60px"
                        width="90px"
                        arrow
                      >
                        <div
                          style={{
                            height: "45px",
                            width: "45px",
                            textAlign: "center",
                          }}
                        >
                          <img
                            src="assets/images/new_flipbook.png"
                            className={classes.flipbook_icon}
                            alt="flipbook"
                          />
                        </div>
                      </MyCustomTooltip>
                    </Grid>
                    <Grid item className={classes.iconAlign}>
                      <MyCustomTooltip
                        title="Visit Franklin Report for client reviews of companies"
                        arrow
                        width="100px"
                        height="40px"
                      >
                        <div
                          className="iconAlign"
                          style={{
                            height: "45px",
                            width: "45px",
                            textAlign: "center",
                          }}
                        >
                          <img
                            src="assets/icons/fr_stamp.svg"
                            className={classes.fr_stamp}
                            alt="flipbook"
                          />
                        </div>
                      </MyCustomTooltip>
                    </Grid>
                    {/* <Grid item>
                      <img
                        className={classes.img_cart}
                        src="assets/icons/icon_shopping_cart.svg"
                        alt={strings.image_text.alt_text.cart_icon}
                      />
                    </Grid> */}
                  </Grid>
                </Row>
                {/* <Grid item style={{ marginLeft: "10px", width: "28%" }}>
                  <Grid container>
                    <MenuComponent
                      image={_.get(userinfo, "profileImg.square")}
                      options={["Logout"]}
                      callBackFunction={(item) => {
                        this.handleMenuClick(item);
                      }}
                    />
                  </Grid>
                </Grid> */}
              </Row>
            </div>
          </div>
          {!openFeedBackModal &&
            (localStorage.getItem("tradeworks_user_token") !== "null" ||
              localStorage.getItem("tradeworks_user_token") !== null) && (
              <img
                // src="assets/images/Lil-beta-stamp.png"
                className={classes.beta_logo}
                onClick={this.toggleFeedBackModal}
                onMouseOut={() => this.mouseOut()}
                onMouseOver={() => this.mouseOver()}
                src={imgSrcPhoto}
              />
            )}
          {userData && openFeedBackModal && (
            <FeedbackModal
              userinfo={userData}
              closeModal={this.toggleFeedBackModal}
            />
          )}
          {companyListModal && (
            <CustomModal open className={classes.modal_popup}>
              <SelectCompanyModal
                user={userinfo}
                CompanyListByUser={CompanyListByUser}
                onClose={this.toggleCompanyListModal}
              />
            </CustomModal>
          )}
          {redirectProfileModal && (
            <CustomModal open className={classes.modal_popup}>
              <RedirectProfileModal
                user={userinfo}
                CompanyListByUser={CompanyListByUser}
                onClose={this.toggleRedirectProfileModal}
              />
            </CustomModal>
          )}
          {showSearchJobModal && (
            <CustomModal open className={classes.modal_popup}>
              <SearchJobModal
                user={userinfo}
                onClose={this.toggleSearchJobModal}
              />
            </CustomModal>
          )}
          {showTalentSearchModal && (
            <CustomModal open className={classes.modal_popup}>
              <TalentSearch
                user={userinfo}
                onClose={this.toggleTalentSearchModal}
                jobsListOptions={jobListing}
              />
            </CustomModal>
          )}
          {showVisitCommunityModal && (
            <CustomModal open className={classes.modal_popup}>
              <VisitCommunity
                user={userinfo}
                onClose={this.toggleVisitCommunityModal}
                trades={trades}
                // topLocations={topLocations}
              />
            </CustomModal>
          )}
          {showVisitCompanyModal && (
            <CustomModal open className={classes.modal_popup}>
              <VisitCompany
                user={userinfo}
                onClose={this.toggleVisitCompanyModal}
                trades={trades}
                // topLocations={topLocations}
              />
            </CustomModal>
          )}
          <CustomModal open={showreferCandidate}>
        <SharePortfolio
          open={showreferCandidate}
          onClose={this.toggleReferCandidate}
          // previewData={previewData}
        />
      </CustomModal>
        </div>
      </>
    );
  }
}

function mapStateToProps(state) {
  return {
    userinfo: state?.LookupReducer?.userinfo,
    profileUserData: state?.Profile?.profileUserData,
    userData: state?.Profile?.companyinfo,
    // businesscard: state?.LookupReducer?.businesscard,
    // user:
    //   (state.JoinUs.joinUsFormData.data &&
    //     state.JoinUs.joinUsFormData.data.data.user) ||
    //   "",
    gamification: state?.LookupReducer?.gamification,
    CompanyListByUser: state.Profile.CompanyListByUser,
    firstImpression: state?.EZReducer?.userinfo,
    jobListing: state.Profile.jobListing,
    trades: state.Profile.trades,
    topLocations: state.Profile?.topLocations,
  };
}
export default connect(mapStateToProps)(
  withRouter(withStyles(useStyles, { withTheme: true })(LoggedInHeader))
);
