import { withStyles } from "@material-ui/core/styles";

import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  left_section: {
    flexGrow: 0,
    maxWidth: "41.666667%",
    flexBasis: "41.666667%",
    "& .MuiTabs-indicator": {
      height: pxToRem(6),
      left: `${pxToRem(190)} !important`,
      width: `${pxToRem(100)} !important`,
    },
  },
  public_pg_margin: { marginTop: pxToRem(73) },
  linearProgess: {
    backgroundColor: `${color.secondary_palette.grays.shadow_gray} !important`,
  },
  right_section: {
    flexGrow: 0,
    maxWidth: "41.666667%",
    flexBasis: "41.666667%",
  },
  middle_section: {
    flexGrow: 0,
    maxWidth: "16.666667%",
    flexBasis: "16.666667%",
    justifyContent: "center",
  },
  benjamin_logo: {
    position: "relative",
    right: pxToRem(5),
    bottom: pxToRem(10),
  },
  space: {
    margin: 0,
  },
  beta_logo: {
    position: "absolute",
    height: pxToRem(120),
    width: pxToRem(120),
    right: 0,
    top: pxToRem(145),
  },
  current_position: {
    ...tradework_theme.typography.styles.avenir_black_r,
    fontSize: pxToRem(12),
    color: color.primary_palette.black,
    textTransform: "uppercase",
  },
  current_position_value: {
    marginLeft: pxToRem(16),
  },
  points_wrap: {
    borderTop: `${pxToRem(1)} solid black`,
    width: pxToRem(140),
  },
  points: {
    padding: `${pxToRem(0)}`,
    width: "50%",
  },
  very_good: {
    padding: `${pxToRem(0)} `,
    width: "50%",
    textAlign: "end",
    marginTop: pxToRem(2),
  },

  progressbar: {
    height: pxToRem(4),
    width: pxToRem(148),
    "& .MuiLinearProgress-colorPrimary": {
      backgroundColor: color.light_blue,
    },
  },
  header_layout: {
    margin: "2rem",
    marginBottom: `${pxToRem(0)} !important`,
    padding: `${pxToRem(0)} ${pxToRem(20)}`,
    position: "relative",
    borderBottom: `solid ${pxToRem(2)} ${
      color.secondary_palette.grays.shadow_gray
    }`,
  },
  coin_icon: {
    width: pxToRem(18),
    height: pxToRem(18),
    VerticalAlign: "middle",
  },
  left_section_align: {
    display: "flex",
    alignItems: "center",
    marginBottom: pxToRem(10),
  },
  headings: {
    fontSize: pxToRem(30),
    height: pxToRem(40),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.gillsans_sb,
    textAlign: "center",
  },
  divider: {
    margin: `${pxToRem(0)} ${pxToRem(16)} ${pxToRem(0)} ${pxToRem(36)}`,
    background: color.secondary_palette.grays.shadow_gray,
    height: pxToRem(2),
  },
  divider2: {
    margin: `${pxToRem(0)} ${pxToRem(10)} ${pxToRem(0)} ${pxToRem(45)}`,
    background: color.secondary_palette.grays.shadow_gray,
    height: pxToRem(2),
  },
  tabs: {
    opacity: `1 !important`,
  },
  current_welcome: {
    ...tradework_theme.typography.styles.avenir_bold,
    fontSize: pxToRem(13),
    color: color.primary_palette.black,
    textTransform: "uppercase",
  },
  iconAlign: {
    marginLeft: "0.3rem",
  },
  tw_icon: {
    width: pxToRem(29),
    height: pxToRem(28),
  },
  icon_hat: {
    width: pxToRem(30),
    height: pxToRem(29),
    opacity: "0.2",
  },
  flipbook_icon: {
    width: pxToRem(29),
    height: pxToRem(28),
  },
  fr_stamp: {
    width: pxToRem(25),
    height: pxToRem(25),
  },
  profile_icon: {
    width: pxToRem(40),
    height: pxToRem(40),
  },
  cart_icon: {
    opacity: "0.5",
    marginLeft: pxToRem(3),
    marginTop: pxToRem(10),
  },
  img_cart: {
    width: pxToRem(49),
    height: pxToRem(36),
  },
  profile_spacing: {
    margin: `${pxToRem(2)} ${pxToRem(16)}`,
    paddingBottom: pxToRem(11),
    // position: "absolute",
    "&:hover": {
      border: `${pxToRem(3)} solid ${
        color.secondary_palette.purples.franklin_purple
      }`,
    },
  },
  right_section_align: {
    display: "flex",
    marginBottom: pxToRem(20),
    width: "100%",
    justifyContent: "flex-end",
  },
  imgAlign: {
    paddingBottom: pxToRem(20),
  },
  highlight_tab: {
    borderBottom: `${pxToRem(6)} solid ${
      color.secondary_palette.purples.franklin_purple
    }`,
  },

  logoutBtn: {
    position: "relative",
  },
  btnPos: {
    position: "absolute",
  },
  menuIcon: {
    position: "absolute",
    left: pxToRem(14),
    cursor: "pointer",
  },
  settings: {
    cursor: "pointer",
    "& ul": {
      border: `${pxToRem(1)} solid ${
        color.secondary_palette.grays.shadow_gray
      }`,
      paddingBottom: pxToRem(5),
      paddingTop: pxToRem(5),
      marginTop: pxToRem(11),
      position: "absolute",
      paddingLeft: 0,
      backgroundColor: color.primary_palette.white,
      zIndex: "9999",
      width: pxToRem(280),
      left: -232,
      borderRadius: pxToRem(10),
      boxShadow: "2px 2px 4px 0 rgba(0, 0, 0, 0.5)",
    },
    "& li": {
      listStyleType: "none",
      padding: pxToRem(5),
      cursor: "pointer",
      color: color.primary_palette.white,
      position: "relative",
      zIndex: 999,
      borderTop: `${pxToRem(1)} solid ${
        color.secondary_palette.grays.shadow_gray
      }`,
      textAlign: "right",
    },
  },
  border_top_gray: {
    borderTop: `${pxToRem(1)} solid ${
      color.secondary_palette.grays.shadow_gray
    }`,
  },
  logout_align: {
    width: pxToRem(16),
    height: pxToRem(15),
    paddingLeft: pxToRem(10),
  },
  pos_relative: {
    position: "relative",
    cursor: "pointer",
  },
  nav_logo: {
    height: pxToRem(30),
    width: pxToRem(30),
    borderRadius: "50%",
    marginLeft: pxToRem(7),
    position: "absolute",
    right: pxToRem(10),
    float: "right",
  },
  nav_logo_profile: {
    height: pxToRem(20),
    width: pxToRem(20),
    borderRadius: "50%",
    marginTop: pxToRem(4),
    marginLeft: pxToRem(7),
    position: "absolute",
    right: pxToRem(13),
    float: "right",
  },
  txtRight: {
    // width: pxToRem(80),
    textAlign: "right",
  },
  spacing_plus: {
    paddingRight: pxToRem(10),
    marginBottom: pxToRem(8),
  },
  spacing_plus_company: {
    marginBottom: pxToRem(3),
    paddingRight: pxToRem(10),
  },
  flexCenter: {
    // height: pxToRem(30),
    // marginLeft: pxToRem(37),
    marginBottom: pxToRem(10),
    // justifyContent: "flex-end",
    paddingRight: pxToRem(10),
    paddingLeft: pxToRem(16),
    position: "relative",
    alignItems: "center",
    "& p": {
      paddingTop: pxToRem(5),
    },
    fontFamily: "gillsans_bold",
    fontWeight: "bold",
    "& img.star": {
      height: pxToRem(15),
      width: pxToRem(15),
      position: "relative",
      // left: pxToRem(-24),
      right: pxToRem(4),
      top: pxToRem(2),
      // float: "left",
    },
    "&:hover": {
      backgroundColor: "#F4F0FF !important",
    },
  },
  flexCenterInactive: {
    // height: pxToRem(30),
    // marginLeft: pxToRem(37),
    marginBottom: pxToRem(10),
    paddingLeft: pxToRem(32),
    // justifyContent: "flex-end",
    paddingRight: pxToRem(10),
    alignItems: "center",
    "& p": {
      paddingTop: pxToRem(8),
    },
    "& img.star": {
      display: "none !important",
    },
    "&:hover": {
      backgroundColor: "#F4F0FF !important",
    },
  },
  logoutTxt: {
    paddingLeft: pxToRem(10),
  },
  secondaryMenu: {
    borderTop: `${pxToRem(1)} solid ${
      color.secondary_palette.grays.shadow_gray
    }`,
    paddingTop: pxToRem(11),
    // paddingLeft: pxToRem(8),
    "& p": {
      paddingLeft: pxToRem(9),
    },
    paddingBottom: pxToRem(1),
  },
  settingsMenu: {
    "&:hover": {
      background: "#f3f1fe",
    },
  },
  secondaryOption: {
    height: pxToRem(20),
    textTransform: "uppercase",
    padding: pxToRem(5),
    paddingLeft: pxToRem(12),
    "& img": {
      width: pxToRem(20),
      height: pxToRem(20),
    },
    "&:hover": {
      backgroundColor: "#F4F0FF",
    },
    marginBottom: pxToRem(10),
  },
  logoutMenu: {
    paddingTop: pxToRem(12),
    paddingLeft: pxToRem(8),
    paddingBottom: pxToRem(16),
    textTransform: "uppercase",
    "&:hover": {
      backgroundColor: "#F4F0FF",
    },
  },
  margin_bottom_0: {
    marginBottom: "0px !important",
  },
});

export default styles;
