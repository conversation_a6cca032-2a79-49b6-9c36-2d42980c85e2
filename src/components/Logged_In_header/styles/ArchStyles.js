import { withStyles } from "@material-ui/core/styles";

import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  parentDiv: {
    width: pxToRem(60),
    height: pxToRem(60),
    position: "absolute",
    top: pxToRem(3),
    left: pxToRem(3),
    borderRadius: "50%",
    transform: "rotate(0deg)",
    transition: "transform 0.7s linear",
  },
  child: {
    position: "absolute",
    width: pxToRem(15),
    height: pxToRem(15),
    transform: "rotate(0deg)",
    transition: "transform 0.7s linear",
    top: pxToRem(45),
    left: pxToRem(5),
    "& img": {
      maxWidth: "100%",
    },
  },
  points: {
    position: "absolute",
    left: pxToRem(25),
  },
  star: {
    position: "relative",

    display: "inline - block",
    width: 0,
    height: 0,

    marginLeft: ".9em",
    marginRight: ".9em",
    marginBottom: "1.2em",

    borderRight: ".3em solid transparent",
    borderBottom: ".7em  solid #FC0",
    borderLeft: ".3em solid transparent",

    /* Controlls the size of the stars. */
    fontSize: "24px",

    "&:after": {
      content: "",

      display: "block",
      width: 0,
      height: 0,

      position: "absolute",
      top: ".6em",
      left: "-1em",

      borderRight: "1em solid transparent",
      borderBottom: ".7em  solid #FC0",
      borderLeft: "1em solid transparent",

      transform: "rotate(-35deg)",
    },
    "& :before": {
      content: "",

      display: "block",
      width: 0,
      height: 0,

      position: "absolute",
      top: ".6em",
      left: "-1em",

      borderRight: "1em solid transparent",
      borderBottom: ".7em  solid #FC0",
      borderLeft: "1em solid transparent",

      transform: "rotate(-35deg)",
    },
  },
});

export default styles;
