import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  people_search: {
    justifyContent: "center",
  },
  select_input: {
    width: pxToRem(50),
    fontWeight: "600",
    fontSize: pxToRem(13),
    color: color.primary_palette.black,
    position: "relative",
    top: pxToRem(23),
    paddingBottom: pxToRem(3),
    border: 0,
    height: pxToRem(10),
    borderRight: `${pxToRem(0.5)} solid ${color.primary_palette.black}`,
    borderBottom: `${pxToRem(0.5)} solid ${color.primary_palette.black}`,
  },
  search_icon: {
    height: pxToRem(20),
    width: pxToRem(23),
    position: "relative",
    top: pxToRem(20),
  },
  input_svg: {
    "& .MuiSvgIcon-root": {
      padding: pxToRem(4),
      width: pxToRem(9.5),
    },
  },
  people_search_block_drpdown: {
    width: pxToRem(80),
    "& .MuiSelect-root": {
      paddingBottom: 0,
    },
    "label + .MuiInput-formControl": {
      marginTop: "0",
    },
    "& .MuiFormControl-root": {
      width: "100%",
    },
    "& .MuiInputLabel-root": {
      marginLeft: pxToRem(15),
      fontSize: pxToRem(13),
      color: "black",
      fontWeight: "600",
    },
    "& .MuiInputBase-inputSelect": {
      padding: pxToRem(3),
    },
    "& .MuiSvgIcon-root": {
      padding: pxToRem(4),
      width: pxToRem(9.5),
    },
    "& .MuiTextField-root": {
      "& .MuiInputBase-input": {
        padding: `${pxToRem(6)} ${pxToRem(0)} ${pxToRem(0)}`,
      },
      "& .MuiInputLabel-root": {
        fontFamily: "gillsans_light",
        fontSize: pxToRem(13),
        fontWeight: "300",
        color: color.secondary_palette.grays.shadow_gray,
      },
    },
  },
  people_search_block: {
    width: pxToRem(165),
    "& .MuiSelect-root": {
      paddingBottom: 0,
    },
    "label + .MuiInput-formControl": {
      marginTop: "0",
    },
    "& .MuiFormControl-root": {
      width: "100%",
    },
    "& .MuiInputLabel-root": {
      marginLeft: pxToRem(15),
      fontSize: pxToRem(13),
      color: "black",
      padding: `${pxToRem(5)} ${pxToRem(0)}`,
      fontWeight: "600",
    },
    "& .MuiInputBase-inputSelect": {
      padding: pxToRem(3),
    },
    "& .MuiSvgIcon-root": {
      padding: pxToRem(4),
      width: pxToRem(9.5),
    },
  },
  textarea_header: {
    width: pxToRem(165),
    marginTop: pxToRem(17),
    fontSize: pxToRem(13),
    textAlign: "center",
    ...tradework_theme.typography.styles.gillsans_sb,
    border: 0,
    borderBottom: `${pxToRem(0.5)} solid ${color.primary_palette.black}`,
    resize: "none",
    outline: "none",
    color: color.primary_palette.black,
    "&::placeholder": {
      color: color.secondary_palette.grays.shadow_gray,
      opacity: 1,
    },
  },
  first_people_search_divider: {
    marginTop: pxToRem(30),
    width: pxToRem(1),
    height: pxToRem(11),
    backgroundColor: color.primary_palette.black,
    opacity: "0.2",
  },
  second_people_search_divider: {
    margin: `${pxToRem(25)} ${pxToRem(8)} ${pxToRem(0)} ${pxToRem(6)}`,
    width: pxToRem(1),
    height: pxToRem(25),
    backgroundColor: color.primary_palette.black,
  },
  advanced_search_block: {
    margin: `${pxToRem(10)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(10)}`,
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  happy_arrow: {
    marginRight: pxToRem(5),
  },
  adv_search_label: {
    fontFamily: "gillsans_r",
    fontSize: pxToRem(14),
    color: color.primary_palette.franklin_purple,
  },
});

export default styles;
