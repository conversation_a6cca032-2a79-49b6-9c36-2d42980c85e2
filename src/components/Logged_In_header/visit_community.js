import React, { Component } from "react";
import _, { get } from "lodash";
import { color } from "../../utilities/themes";
import RequestStyles from "./styles/requestStyles";
import Text from "../common/ui_kit/text";
import Row from "../common/ui_kit/row";
import CustomButton from "../navigations/custom_buttons";
import Close_Icon from "../data_display/icons/Close";
import CustomSelect from "../inputs/custom_select";
import Chevron_Down_Icon from "../data_display/icons/Arrow_Chevron_Down";
import { MenuItem } from "@material-ui/core";
import { BASEURL, TOKEN_KEY } from "../../constants";
import CustomScrollbars from "../data_display/custom_scroll";
import CustomAutocomplete from "../inputs/custom_auto_complete";
import { ProfileActions } from "../../redux/actions";
import { connect } from "react-redux";

class VisitCommunity extends Component {
  constructor(props) {
    super(props);
    this.state = {
      jobs: "",
      location: {},
    };
  }

  handleDropdownSelect = (name, value) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    this.setState({ [name]: value });
  };

  sendRequest = () => {
    const { trade, market, location } = this.state;
    // let host = "twwstage.franklinreport.com/cats";
    let host = `${BASEURL.URL}cats`;
    let token = localStorage.getItem(TOKEN_KEY);
    let companyId = localStorage.getItem("companyId");
    let tradeVal =
      trade && trade.name !== "Select All Trades" ? trade.name : "empty";
    let marketVal =
      location && location.cityAndState !== "Select All Markets"
        ? location.cityAndState
        : "empty";
    window.location.href = `${host}/${token}/${companyId}/DiscoverSearch/${tradeVal}/${marketVal}`;
    this.props.onClose();
  };

  onLocationChange = (location) => () => {
    if (!location) {
      return;
    }
    this.setState({
      location: location,
    });
  };

  handleInput = (evt) => {
    this.rapInput = evt.target.value;
    this.handleGetRapData();
  };

  handleGetRapData = () => {
    this.props.dispatch(
      ProfileActions.fetchTopLocations({ name: this.rapInput })
    );
  };

  render() {
    const { classes, onClose, trades, topLocations } = this.props;
    const { trade, market, location } = this.state;
    return (
      <>
        <div className={classes.talentModal_width}>
          <div className={classes.btnRight}>
            <CustomButton className={classes.crossBtn}>
              <Close_Icon onClick={onClose} />
            </CustomButton>
          </div>
          <div className={classes.textCenter}>
            <img
              src="assets/images/community_image.png"
              style={{ height: "80px", marginBottom: "14px" }}
            />
          </div>
          <div className={classes.spacing_from}>
            <Text
              size={18}
              color={color.primary_palette.black}
              family="gillsans_sb"
              className={classes.textCenter}
              style={{ marginBottom: "15px" }}
            >
              SEARCH COMMUNITY BY TRADE
            </Text>
            <Row className={classes.margin20}>
              <Text
                size={14}
                color={color.primary_palette.black}
                family="gillsans_sb"
                className={classes.text_width}
                style={{ marginTop: "6px", width: "130px", textAlign: "right" }}
              >
                Trade
              </Text>
              <CustomSelect
                className={classes.job_listing_dropdown2}
                IconComponent={Chevron_Down_Icon}
                name="trade"
                value={trade}
                renderValue={(value) =>
                  get(value, "name", "All Trades or Choose One")
                }
              >
                <MenuItem
                  key="999"
                  onClick={this.handleDropdownSelect("trade", {
                    name: "Select All Trades",
                  })}
                >
                  Select All Trades
                </MenuItem>
                {trades &&
                  trades.map((jobs, idx) => {
                    return (
                      <MenuItem
                        key={idx}
                        onClick={this.handleDropdownSelect("trade", jobs)}
                      >
                        {jobs.name}
                      </MenuItem>
                    );
                  })}
              </CustomSelect>
            </Row>
            <Row className={classes.margin20}>
              <Text
                size={14}
                color={color.primary_palette.black}
                family="gillsans_sb"
                className={classes.text_width}
                style={{ marginTop: "6px", width: "130px", textAlign: "right" }}
              >
                Residence
              </Text>
              {/* <CustomSelect
                className={classes.job_listing_dropdown2}
                IconComponent={Chevron_Down_Icon}
                name="market"
                value={market}
                renderValue={(value) =>
                  get(value, "cityAndState", "Closest Market")
                }
              >
                <CustomScrollbars height="274px">
                  <MenuItem
                    key="999"
                    onClick={this.handleDropdownSelect("market", {
                      cityAndState: "Select All Markets",
                    })}
                  >
                    Select All Markets
                  </MenuItem>
                  {topLocations &&
                    topLocations.map((jobs, idx) => {
                      return (
                        <MenuItem
                          key={idx}
                          onClick={this.handleDropdownSelect("market", jobs)}
                        >
                          {jobs.cityAndState}
                        </MenuItem>
                      );
                    })}
                </CustomScrollbars>
              </CustomSelect> */}
              <CustomAutocomplete
                className={classes.your_location}
                placeholder="All Markets or Select One"
                onTextChange={this.handleInput}
                value={location}
                freeSolo
                getOptionLabel={(option) => {
                  return option.cityAndState || this.rapInput;
                }}
                popupIcon={
                  <Chevron_Down_Icon
                    className={classes.drop_down}
                    // onClick={this.openAccoladeDropdown}
                  />
                }
                title={location.cityAndState}
                options={topLocations || []}
                ListboxComponent={React.forwardRef(
                  ({ children, ...rest }, ref, itemsLength) => (
                    <CustomScrollbars
                      style={
                        _.get(this.props, "topLocations.length") > 6
                          ? { minHeight: "240px" }
                          : { minHeight: "120px" }
                      }
                      ref={ref}
                      {...rest}
                      itemsLength={topLocations ? topLocations.length : []}
                    >
                      <MenuItem
                        key="999"
                        onClick={this.onLocationChange({
                          cityAndState: "Select All Markets",
                        })}
                      >
                        Select All Markets
                      </MenuItem>
                      {_.map(topLocations, (trade) => (
                        <MenuItem onMouseUp={this.onLocationChange(trade)}>
                          {trade.cityAndState}
                        </MenuItem>
                      ))}
                    </CustomScrollbars>
                  )
                )}
              />
            </Row>
            <div className={classes.textCenter}>
              <CustomButton
                className={classes.reqBtn}
                onClick={this.sendRequest}
                style={{ width: "100px" }}
              >
                SEEK
              </CustomButton>
            </div>
          </div>
        </div>
      </>
    );
  }
}

// export default RequestStyles(VisitCommunity);

function mapStateToProps(state) {
  return {
    topLocations: state.Profile?.topLocations,
  };
}

export default connect(mapStateToProps)(RequestStyles(VisitCommunity));
