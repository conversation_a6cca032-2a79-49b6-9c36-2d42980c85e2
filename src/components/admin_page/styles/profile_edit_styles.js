import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  right_side: {
    display: "block",
    margin: "0 auto",
  },
  text_blocks: {
    display: "block",
  },
  edit_text: {
    fontSize: pxToRem(35),
    ...tradework_theme.typography.styles.gillsans_light,
    textTransform: "uppercase",
    textAlign: "center",
  },
  click_Edit: {
    fontSize: pxToRem(18),
    ...tradework_theme.typography.styles.gillsans_r,
    color: color.secondary_palette.grays.medium_gray,
    textAlign: "center",
    paddingTop: pxToRem(5),
    paddingBottom: pxToRem(22),
  },
  click_blocks: {
    display: "block",
    textAlign: "center",
    "& .MuiButton-root": {
      "&:hover": {
        backgroundColor: "transparent !important",
        color: "black !important",
      },
    },
  },
  clickable: {
    width: pxToRem(280),
    borderRadius: pxToRem(4.1),
    textTransform: "none",
    display: "block",
    "&:hover": {
      border: `solid ${pxToRem(2)} ${color.primary_palette.highlight_purple}`,
    },
    "& .MuiButton-label": {
      justifyContent: "left !important",
    },
    border: `solid ${pxToRem(2)} ${color.primary_palette.black}`,
    marginTop: pxToRem(18),
    margin: "0 auto",
    ...tradework_theme.typography.styles.avenir_sb,
    fontSize: pxToRem(20.4),
    color: color.primary_palette.black,
    paddingLeft: pxToRem(18),
    paddingBottom: pxToRem(3),
    paddingTop: pxToRem(3),
    textAlign: "left",
  },
  logo_cover: {
    "&:hover": {
      border: `solid ${pxToRem(2)} ${color.profile_parts.logo_cover}`,
    }
  },
  company_pitch: {
    "&:hover": {
      border: `solid ${pxToRem(2)} ${color.profile_parts.company_pitch}`,
    }
  },
  water_cooler: {
    "&:hover": {
      border: `solid ${pxToRem(2)} ${color.profile_parts.water_cooler}`,
    }
  },
  our_offices: {
    "&:hover": {
      border: `solid ${pxToRem(2)} ${color.profile_parts.our_offices}`,
    }
  },
  company_culture: {
    "&:hover": {
      border: `solid ${pxToRem(2)} ${color.profile_parts.company_culture}`,
    }
  },
  team_alumni: {
    "&:hover": {
      border: `solid ${pxToRem(2)} ${color.profile_parts.team_alumni}`,
    }
  },
  btn: {
    ...tradework_theme.typography.styles.avenir_bold,
    fontSize: pxToRem(20),
    ...tradework_theme.typography.styles.avenir_black_r,
    border: `solid ${pxToRem(2)} ${color.secondary_palette.blues.action_blue}`,
    borderRadius: pxToRem(27),
    color: color.secondary_palette.blues.action_blue,
    paddingLeft: pxToRem(33),
    paddingRight: pxToRem(33),
    paddingBottom: pxToRem(3),
    paddingTop: pxToRem(3),
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.secondary_palette.blues.action_blue} !important`,
      border: `${pxToRem(2)} solid ${
        color.secondary_palette.blues.action_blue
      }`,
    },
  },
  btn_align: {
    justifyContent: "center",
    paddingTop: pxToRem(50),
    paddingBottom: pxToRem(45),
  },
});

export default styles;
