import React from "react";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import profileStyles from "./styles/profile_edit_styles";
import CustomButton from "../navigations/custom_buttons";

function ProfileEdit(props) {
  const { classes, setHoverSection, history } = props;

  const handleButtonHover = (section) => () => {
    setHoverSection(section);
  };

  const handleButtonClick = (step) => (e) => {
    history.push({ pathname: "/wizard", state: { step, edit: true } });
  };

  const redirectToProfile = () => {
    history.push("profile");
  };

  return (
    <>
      <Row>
        <Row className={classes.right_side}>
          <Row className={classes.text_blocks}>
            <Text className={classes.edit_text}>
              {strings.admin_edit.titles.admin_edit}
            </Text>
            <Text className={classes.click_Edit}>
              {strings.admin_edit.titles.click_Edit}
            </Text>
          </Row>
          <Row className={classes.click_blocks}>
            <CustomButton
              className={`${classes.clickable} ${classes.logo_cover}`}
              onMouseEnter={handleButtonHover("logoAndCover")}
              onMouseLeave={handleButtonHover("")}
              onClick={handleButtonClick(2)}
            >
              {strings.admin_edit.titles.logo_cover}
            </CustomButton>
            <CustomButton
              className={`${classes.clickable} ${classes.company_pitch}`}
              onMouseEnter={handleButtonHover("companyPitch")}
              onMouseLeave={handleButtonHover("")}
              onClick={handleButtonClick(2)}
            >
              {strings.admin_edit.titles.company_pitch}
            </CustomButton>
            <CustomButton
              className={`${classes.clickable} ${classes.water_cooler}`}
              onMouseEnter={handleButtonHover("waterCooler")}
              onMouseLeave={handleButtonHover("")}
              onClick={handleButtonClick(3)}
            >
              {strings.admin_edit.titles.water_cooler}
            </CustomButton>
            <CustomButton
              className={`${classes.clickable} ${classes.our_offices}`}
              onMouseEnter={handleButtonHover("ourOffices")}
              onMouseLeave={handleButtonHover("")}
              onClick={handleButtonClick(4)}
            >
              {strings.admin_edit.titles.our_offices}
            </CustomButton>
            <CustomButton
              className={`${classes.clickable} ${classes.company_culture}`}
              onMouseEnter={handleButtonHover("companyHighlights")}
              onMouseLeave={handleButtonHover("")}
              onClick={handleButtonClick(5)}
            >
              {strings.admin_edit.titles.Company_highlights}
            </CustomButton>
            <CustomButton
              className={`${classes.clickable} ${classes.team_alumni}`}
              onMouseEnter={handleButtonHover("currentTeam")}
              onMouseLeave={handleButtonHover("")}
            >
              {strings.admin_edit.titles.Current_team}
            </CustomButton>
          </Row>
          <Row className={classes.btn_align}>
            <CustomButton className={classes.btn} onClick={redirectToProfile}>
              Done
            </CustomButton>
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default profileStyles(ProfileEdit);
