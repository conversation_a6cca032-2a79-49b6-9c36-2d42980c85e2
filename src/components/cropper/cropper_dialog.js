import React, { useState } from "react";
import CustomModal from "../inputs/custom_modal";
import Close_Icon from "../data_display/icons/Close";
import { Text, Row } from "../common/ui_kit";
import CustomCropper from "./custom_cropper";
import CustomSlider from "../common/ui_kit/custom_slider";
import CustomButton from "../navigations/custom_buttons";
import { makeStyles } from "@material-ui/core";
import { pxToRem, color } from "../../utilities/themes";

const useStyles = makeStyles({
  main: {
    width: "40%",
    backgroundColor: color.secondary_palette.blues.modal_blue,
  },
  Close_icon: {
    float: "right",
    padding: pxToRem(12),
    cursor: "pointer",
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(12),
    },
  },
  content: {
    padding: pxToRem(30),
  },
  cropper_container: {
    position: "relative",
    height: "50vh",
    margin: pxToRem(12),
  },
  center_text: {
    textAlign: "center",
  },
  slider: {
    margin: pxToRem(12),
  },
  btnTxtStyle: {
    fontFamily: "gillsans_sb",
    fontSize: pxToRem(15),
    fontWeight: "600",
    color: color.form_colors.royal_purple_1,
    border: `1.4px solid` + color.form_colors.blueberry_purple,
    width: "108px",
    height: pxToRem(28),
    padding: "1px 0",
    "&:hover": {
      backgroundColor: "#eeeeee",
      color: `${color.primary_palette.franklin_purple} !important`,
      border: `1px solid ` + color.primary_palette.franklin_purple,
      padding: "1px 0",
    },
  },
});

function CropperDialog(props) {
  const classes = useStyles();
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(2);
  const [croppedCoords, setCroppedCoords] = useState(null);

  const onZoomChange = (e, value) => {
    setZoom(value);
  };

  return (
    <CustomModal
      open
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
    >
      <div className={`${classes.main} ${props.className}`} style={props.style}>
        <Close_Icon className={classes.Close_icon} onClick={() => props.onClose()} />
        <div className={classes.content}>
          <Text size={25} className={classes.center_text}>
            • EDIT MEDIA •
          </Text>
          <Text family="gillsans_sb" size={16} className={classes.center_text}>
            Let's make sure your images look great for this experience
          </Text>
          <Text
            size={15}
            className={classes.center_text}
            style={{ marginTop: pxToRem(5) }}
          >
            Use Yellow Guidelines if displaying two images for this Experience.
          </Text>
          <Text size={15} className={classes.center_text}>
            And Blue Guidelines if only one
          </Text>
          <div className={classes.cropper_container}>
            <CustomCropper
              multiple
              image={props.media}
              crop={crop}
              zoom={zoom}
              cropShape="rect"
              onCropChange={setCrop}
              onCropComplete={setCroppedCoords}
              onZoomChange={onZoomChange}
              showGrid={false}
            />
          </div>
          <CustomSlider
            zoom={zoom}
            onZoomChange={onZoomChange}
            className={classes.slider}
          />
          <Text
            family="avenir_light"
            size={15}
            color={color.secondary_palette.grays.drag_text}
            className={classes.center_text}
          >
            Drag the crop selection to desired position.
          </Text>
          <Text
            family="avenir_light"
            size={15}
            color={color.secondary_palette.grays.drag_text}
            className={classes.center_text}
          >
            Drag the zoom handle to change photo size
          </Text>
          <Row
            justify="space-between"
            style={{ padding: `0 5%`, marginTop: pxToRem(12) }}
          >
            <CustomButton
              onClick={() => props.onClose()}
              color="primary"
              variant="outlined"
              className={classes.btnTxtStyle}
            >
              CANCEL
            </CustomButton>
            <CustomButton
              onClick={() => props.onClose(croppedCoords)}
              color="primary"
              variant="outlined"
              className={classes.btnTxtStyle}
            >
              CONTINUE
            </CustomButton>
          </Row>
        </div>
      </div>
    </CustomModal>
  );
}

export default CropperDialog;
