import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { filter, map, find, sortBy, omit, get } from "lodash";
import { useHistory } from "react-router-dom";
import uuidv4 from "uuid/v4";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import WaterCoolerStyles from "./styles/water_cooler_styles";
import Lil_Plus_filled from "../../components/data_display/icons/Lil_Plus_filled";
import WaterCoolerBlock from "./water_cooler_block";
import CustomButton from "../navigations/custom_buttons";
import { ProfileActions } from "../../redux/actions";
import { pxToRem } from "../../utilities/themes";
import { BASEURL } from "../../constants";

function WaterCooler(props) {
  const { classes, handleNext, handleBack } = props;
  const history = useHistory();
  const [initialValues, setInitialValues] = useState({
    waterCoolerBlocks: [],
    errors: {},
  });

  const dispatch = useDispatch();

  const waterCoolerData1 = useSelector((state) => state.Profile.watercooler);
  const { waterCoolerBlocks, errors } = initialValues;
  const { edit } = props;

  const fetchData = () => {
    dispatch(ProfileActions.getCompanyProfileData(["waterCooler"]));
  };

  const setInitialData = () => {
    if (
      waterCoolerData1.length == 0 &&
      initialValues.waterCoolerBlocks.length == 0
    ) {
      addWaterCoolerSection();
      return;
    }
    formatWaterCoolerBlocks(waterCoolerData1);
  };
  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    waterCoolerData1 && setInitialData();
  }, [waterCoolerData1]);

  const formatWaterCoolerBlocks = (wcArray) => {
    if (!wcArray.length) {
      return;
    }
    // CHECKING FOR LENGTH MULTIPLE OF 3
    if (wcArray.length % 3 == 0) {
      /**
       * CASE LENGTHS - 3,6,9
       * NO NEED TO ADDING EXTRA SECTIONS
       */
      setInitialValues({
        ...initialValues,
        waterCoolerBlocks: [...wcArray],
      });
      return;
    } else {
      /**
       * CASE LENGTHS - 1,2,4,5,7,8
       * NEED TO ADD EXTRA SECTIONS
       * 2 SECTIONS FOR LENGTH 1,7,4
       * 1 SECTION FOR LENGTHS 2,5,8
       */
      // 1-2,7-2,2-1,4-1,5-1,8-1,
      if (wcArray.length == 1 || wcArray.length == 4 || wcArray.length == 7) {
        setInitialValues({
          ...initialValues,
          waterCoolerBlocks: [
            ...wcArray,
            {
              _id: uuidv4(),
              uploadType: "image",
              headShotLink: "",
              teamMemberName: "",
              title: "",
              videoLink: "",
              videoSummery: "",
              position: wcArray.length + 1,
            },
            {
              _id: uuidv4(),
              uploadType: "image",
              headShotLink: "",
              teamMemberName: "",
              title: "",
              videoLink: "",
              videoSummery: "",
              position: wcArray.length + 2,
            },
          ],
        });
        return;
      } else {
        // CASES 2,5,8
        setInitialValues({
          ...initialValues,
          waterCoolerBlocks: [
            ...wcArray,
            {
              _id: uuidv4(),
              headShotLink: "",
              uploadType: "image",
              teamMemberName: "",
              title: "",
              videoLink: "",
              videoSummery: "",
              position: wcArray.length + 1,
            },
          ],
        });
        return;
      }
    }
  };

  const addWaterCoolerSection = () => {
    /**
     * ADDING WATER COOLER SECTIONS(3) ON CLICK
     */
    let position = waterCoolerBlocks.length;
    for (let i = 0; i <= 2; i++) {
      waterCoolerBlocks.push({
        _id: uuidv4(),
        uploadType: "image",
        headShotLink: "",
        teamMemberName: "",
        title: "",
        videoLink: "",
        videoSummery: "",
        position: position + 1,
      });
      position++;
    }
    setInitialValues({ ...initialValues });
  };

  const onBlurInput = (block, name) => (e) => {
    block[name] = e.target.value.trim();
    if (name === "uploadType" && e.target.value === "image") {
      // IF UPLOAD TYPE IS VIDEO CLEARING VIDEO LINK IF GIVEN
      block.videoLink = "";
      block.videoLinkErr = false;
    }
    if (name === "videoLink") {
      block.videoLinkErr = e.target.value.length > 0 ? false : true;
    }
    setInitialValues({ ...initialValues });
  };
  const onPositionChange = (block, selectedOrder) => (e) => {
    const duplicate = find(waterCoolerBlocks, {
      position: selectedOrder,
    });
    if (duplicate) {
      duplicate.position = block.position;
      block.position = selectedOrder;
      setInitialValues({ ...initialValues });
      return;
    }
  };

  const rediectToEdit = () => {
    history.push("twc/profile");
  };

  const onSubmit = () => {
    const dataToSubmit = sortBy(waterCoolerBlocks, "position").map(
      (block, idx) => {
        const omitData = omit(block, "_id");
        return { ...omitData, position: idx + 1 };
      }
    );
    dispatch(
      ProfileActions.updateWaterCooler([...dataToSubmit], (res) => {
        if (!res.error) {
          if (edit) {
            history.push("twc/profile");
          } else {
            handleNext();
          }
        }
      })
    );
  };

  const saveDataRedirect = () => {
    const dataToSubmit = sortBy(waterCoolerBlocks, "position").map(
      (block, idx) => {
        const omitData = omit(block, "_id");
        return { ...omitData, position: idx + 1 };
      }
    );
    dispatch(
      ProfileActions.updateWaterCooler([...dataToSubmit], (res) => {
        if (!res.error) {
          redirectToWpack();
        }
      })
    );
  };

  const handleSummaryError = (obj) => {
    if (!errors.summary) {
      errors.summary = {};
    }
    errors.summary[obj.index] = obj.error;
    setInitialValues({ ...initialValues });
  };

  const handleBlockImageUpload = (imageData, block) => {
    block.headShotLink = imageData.image;
    block.imageFile = imageData.imageFile;
    setInitialValues({ ...initialValues });
  };

  const handleDeleteWaterCooler = (id) => () => {
    const updatedData = filter(waterCoolerBlocks, (block) => {
      return block._id !== id;
    });
    formatWaterCoolerBlocks(updatedData);
  };

  const disableAddButton = () => {
    let error = false;
    if (waterCoolerBlocks.length === 9) {
      error = true;
      return error;
    } else {
      for (let i = 0; i < waterCoolerBlocks.length; i++) {
        if (!waterCoolerBlocks[i].teamMemberName) {
          error = true;
          return error;
        }
      }
    }
  };

  const submitValidation = () => {
    let summaryError = false;
    let videoLinkErr = false;

    const summaryValidation = errors.summary
      ? Object.values(errors.summary)
      : [];

    summaryValidation.forEach((err) => {
      if (err) {
        summaryError = true;
        return false;
      }
    });
    if (summaryError) {
      // IF ERROR IN SUMMARY RETURN ERROR:TRUE
      return true;
    }
    waterCoolerBlocks.forEach((block) => {
      if (block.uploadType === "video") {
        if (!block.videoLink) {
          videoLinkErr = true;
          return false;
        }
      }
    });
    return videoLinkErr;
  };

  const totalRanges = waterCoolerBlocks.filter((block) => {
    return block.title;
  });

  const handleFinishLater = () => {
    history.push("twc/profile");
  };

  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let token = localStorage.getItem("tradeworks_user_token");
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}wpack`;
      window.location.href = `${host}/${token}/${companyId}`;
      // if (host.includes("localhost")) {
      //   host = "localhost:3003";
      //   window.location.href = `http://${host}/${token}/${companyId}`;
      // } else {
      //   if (host.includes("-dev-")) {
      //     host = TDW_URL.WPACK.DEV;
      //   }
      //   if (host.includes("-qa-")) {
      //     host = TDW_URL.WPACK.QA;
      //   }
      //   if (host.includes("-stage-")) {
      //     host = TDW_URL.WPACK.STAGE;
      //   } else {
      //     host = "http://twwstage.franklinreport.com/wpack";
      //   }
      //   window.location.href = `${host}/${token}/${companyId}`;
      // }
    }
  };

  return (
    <>
      <Row className={classes.Water_cooler}>
        <Row className={classes.center_text}>
          <Text className={classes.main_des}>
            {strings.company_wizard.step3.titles.step3_title}
          </Text>
          {/* <Text className={classes.main_brand}>
            {strings.company_wizard.step3.titles.step3_des}
          </Text> */}
        </Row>
        {edit && (
          <CustomButton
            className={classes.logo_btn_red}
            // disabled={disableAddButton()}
            onClick={saveDataRedirect}
            style={{ float: "right" }}
          >
            POST A JOB NOW
          </CustomButton>
        )}
        <CustomButton
          variant="outlined"
          color="primary"
          disabled={submitValidation()}
          className={`${classes.greenactionButtons} ${classes.second_btn_width}`}
          onClick={onSubmit}
          style={{ float: "right", width: edit && pxToRem(240) }}
        >
          {edit
            ? "Save & Go back to Profile"
            : strings.general.titles.save_continue}
        </CustomButton>
        <Row className={classes.main_head}>
          <Text className={classes.step_2a}>
            {strings.company_wizard.step3.titles.step3}
          </Text>
          <Text className={classes.step_2a_des}>
            {strings.company_wizard.step3.titles.step3_title_des}
          </Text>
          <Text className={classes.step_2a_des}>
            An image is required to display each set.
          </Text>
        </Row>

        <Row className={classes.btn_blk}>
          {waterCoolerBlocks &&
            map(waterCoolerBlocks, (each, index) => (
              <WaterCoolerBlock
                block={each}
                key={each._id}
                onBlur={onBlurInput}
                index={index}
                totalRanges={totalRanges}
                imageUpload={handleBlockImageUpload}
                handleSummaryError={handleSummaryError}
                handleDeleteWaterCooler={handleDeleteWaterCooler}
                onPositionChange={onPositionChange}
              />
            ))}
        </Row>
        {get(waterCoolerBlocks, "length", 0) <= 6 && (
          <Row className={classes.btn_sty}>
            <CustomButton
              className={classes.logo_btn}
              disabled={disableAddButton()}
              onClick={addWaterCoolerSection}
            >
              <Lil_Plus_filled />
              {strings.company_wizard.step3.titles.Add_More_Videos}
            </CustomButton>
          </Row>
        )}
        <Row className={classes.btn_blk_width}>
          {!edit && (
            <customLink
              className={`${classes.later_btn} ${classes.margin_l_60}`}
              onClick={handleFinishLater}
            >
              {strings.general.titles.finish_later}
            </customLink>
          )}
          <Row className={classes.btn_flex}>
            {!edit && (
              <CustomButton
                variant="outlined"
                color="primary"
                onClick={handleNext}
                className={`${classes.actionButtons} ${classes.first_btn_width}`}
              >
                {strings.general.titles.skip}
              </CustomButton>
            )}
            {/* <CustomButton
              variant="outlined"
              color="primary"
              className={`${classes.actionButtons} ${classes.second_btn_width}`}
              onClick={edit ? rediectToEdit : handleBack}
            >
              CANCEL
            </CustomButton> */}
            <CustomButton
              variant="outlined"
              color="primary"
              disabled={submitValidation()}
              className={`${classes.greenactionButtons} ${classes.second_btn_width}`}
              onClick={onSubmit}
              style={{ width: edit && pxToRem(240) }}
            >
              {edit
                ? "Save & Go back to Profile"
                : strings.general.titles.save_continue}
            </CustomButton>
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default WaterCoolerStyles(WaterCooler);
