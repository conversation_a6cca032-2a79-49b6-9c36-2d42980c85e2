import React, { useState, useRef, useEffect } from "react";

import Row from "../common/ui_kit/row";
import StaticProfile from "./businessCard_steps/static_profile";
import EditSections from "../admin_page/profile_edit";
import businessCardStyles from "./styles/businessCardStyles";
import CustomScrollbars from "../data_display/custom_scroll";

function EditProfile(props) {
  const { classes } = props;
  let scrollRef = useRef(null);
  const [hover_section, setHoverSection] = useState(null);

  useEffect(() => {
    switch (hover_section) {
      case "logoAndCover":
        return scrollRef.scrollToTop();
      case "companyPitch":
        return scrollRef.scrollTop();
      case "waterCooler":
        return scrollRef.scrollTop(300);
      case "ourOffices":
        return scrollRef.scrollTop(350);
      case "companyHighlights":
        return scrollRef.scrollTop(700);
      case "currentTeam":
        return scrollRef.scrollToBottom();
      default:
        return scrollRef.scrollToTop();
    }
  }, [hover_section]);

  return (
    <>
      <Row className={classes.business_card_width_edit}>
        <Row className={`${classes.left_side_edit}`}>
          <CustomScrollbars
            className={`${classes.scroll_area_profile}`}
            scrollbarTrackStyles={classes.scrollbar_track}
            setRef={(ref) => (scrollRef = ref)}
          >
            <StaticProfile edit hover_section={hover_section} />
          </CustomScrollbars>
        </Row>
        <Row className={classes.right_side_edit}>
          <Row className={classes.block}>
            <EditSections
              history={props.history}
              setHoverSection={setHoverSection}
            />
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default businessCardStyles(EditProfile);
