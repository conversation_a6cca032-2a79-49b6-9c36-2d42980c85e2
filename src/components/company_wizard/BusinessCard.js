import React, { useState } from "react";
import { get } from "lodash";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import StaticProfile from "./businessCard_steps/static_profile";
import BusinessCardOne from "./businessCard_steps/businesscard_step1";
import businessCardStyles from "./styles/businessCardStyles";
import { color } from "../../utilities/themes";

function BusinessCard(props) {
  const { classes, handleNext } = props;
  const [values, setValues] = useState({ userType: "" });

  const setUserType = (type) => {
    // HEADING MUST BE CHANGED BASED ON SELECTED USER TYPE IN BUSINESS CARD
    const { userType } = values;
    if (userType !== type) {
      setValues({ ...values, userType: type });
    }
    return;
  };

  return (
    <>
      <Row className={classes.business_text}>
        <Text
          size={35}
          color={color.primary_palette.black}
          family="gillsans_light"
          className={`${classes.text_center} ${classes.upper_case}`}
        >
          {strings.company_wizard.step1.titles.businessTitle}
        </Text>
        <Text
          size={20}
          color={color.primary_palette.black}
          family="avenir_roman"
          className={`${classes.text_center} ${classes.marginBottom30}`}
        >
          {strings.company_wizard.step1.titles.businessTitle_des}
        </Text>
      </Row>
      <Row className={classes.business_card_width}>
        <Row className={classes.left_side}>
          <Text
            size={20}
            color={color.primary_palette.franklin_purple}
            family="gillsans_sb"
            className={classes.left_text}
          >
            {get(values, "userType", "") === "teamMember"
              ? strings.company_wizard.step1.titles.your_company
              : strings.company_wizard.step1.titles.sample_company}
          </Text>
          <Text
            size={16}
            color={color.primary_palette.black}
            family="gillsans_sb"
            className={classes.left_text}
          >
            This is what your profile will look like when you are finished!
          </Text>
          {/* <StaticProfile /> */}
          <img
            src="assets/images/Baby-Yacht.jpg"
            style={{ width: "527px" }}
            // className={classes.showmore_add_icon}
          />
        </Row>
        <Row className={classes.right_side}>
          <BusinessCardOne setUserType={setUserType} handleNext={handleNext} />
        </Row>
      </Row>
    </>
  );
}

export default businessCardStyles(BusinessCard);
