import React, { useRef, useState, useEffect } from "react";
import { get } from "lodash";

import StylesSheet from "./styles/addImageLogoStyles";
import Lil_Plus_filled from "../../components/data_display/icons/Lil_Plus_filled";
import { MAX_UPLOAD_SIZE } from "../../constants";
import { color } from "../../utilities/themes";
import strings from "../../utilities/strings";
import Text from "../common/ui_kit/text";
import Row from "../common/ui_kit/row";
import CropperModal from "../modals/cropper_modal";
import CustomModal from "../../components/inputs/custom_modal";
import Element_Required_Icon from "../../components/data_display/icons/ElementRequiered";

function AddImageHoverLogo(props) {
  const uploadRef = useRef(null);

  const {
    classes,
    buttonLabel,
    deleteIcon,
    defaultImage,
    refreshIcon,
    plus,
    className,
    margin10,
    marginLeft_9,
    marginRight_9,
    margin_left_20,
    errorStyles,
    handleUploadError,
    noErrorDisplay,
    pencilEdit,
    cropShape,
    cropSize,
    layoutData,
  } = props;
  const [errors, setErrors] = useState({});
  const [uploadedImage, setUploadedImage] = useState({
    openCropperModal: false,
  });
  const [defaultPlaceImage, setDefaultPlaceImage] = useState("");

  useEffect(() => {
    if (props.img && props.img[props.name] && props.edit) {
      setUploadedImage({ ...props.img[props.name] });
    }
    if (props.layoutImage && props.layoutImage[props.index] && props.edit) {
      setUploadedImage({ ...props.layoutImage[props.index] });
    }
  }, [props.img, props.layoutImage]);

  useEffect(() => {
    if (props.name === "companyLogo") {
      setDefaultPlaceImage("assets/images/Company Logo Initial.svg");
    } else if (props.name === "coverImage") {
      setDefaultPlaceImage("assets/images/Cover Image.png");
    } else if (props.name === "topFeatureImage1") {
      setDefaultPlaceImage("assets/images/Feature Image 1 Initial.svg");
    } else if (props.name === "topFeatureImage2") {
      setDefaultPlaceImage("assets/images/Feature Image 2 Initial.svg");
    } else {
      setDefaultPlaceImage("assets/images/Feature Image 2 Initial.svg");
    }
  }, [props.name, buttonLabel]);

  const imageUpload = (e) => {
    e.preventDefault();
    let file = e.target.files[0];
    if (!file) {
      return;
    }
    uploadRef.current.value = "";
    if (!file.type.includes("image")) {
      handleUploadError &&
        handleUploadError({
          error: true,
          message: "Upload JPG or PNG, Max 2MB",
          layout: props.id,
        });
      setErrors({
        ...errors,
        image: "Upload JPG or PNG, Max 2MB",
      });
      return;
    }
    if (file.size > MAX_UPLOAD_SIZE) {
      handleUploadError &&
        handleUploadError({
          error: true,
          message: "Upload JPG or PNG, Max 2MB",
          layout: props.id,
        });
      setErrors({
        ...errors,
        image: "Upload JPG or PNG, Max 2MB",
      });
      return;
    }
    setUploadedImage({
      ...uploadedImage,
      cImage: URL.createObjectURL(file),
      openCropperModal: true,
    });
    setErrors({
      image: "",
    });
    handleUploadError && handleUploadError({ error: false });
  };

  const handleButtonClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    uploadRef.current.click();
  };

  const recropImage = () => {
    setUploadedImage({
      ...uploadedImage,
      image: uploadedImage.image,
      openCropperModal: !uploadedImage.openCropperModal,
    });
  };

  const handleDelete = () => {
    setUploadedImage({});
    props.deleteImage && props.deleteImage(props.name, props.id, props.index);
  };

  const setCroppedImage = (imageUrl, imageFile) => {
    setUploadedImage({ image: imageUrl, imageFile, openCropperModal: false });
    if (props.imageUpload) {
      props.imageUpload(
        {
          image: imageUrl,
          imageFile,
          id: props.id,
          index: props.index,
        },
        props.name
      );
    }
  };

  const handleCropperModal = () => {
    setUploadedImage({
      ...uploadedImage,
      openCropperModal: !uploadedImage.openCropperModal,
    });
  };

  const returnActionButtons = () => {
    return (
      <div className={classes.action_btns}>
        {uploadedImage.image && (
          <>
            {deleteIcon && (
              <img
                src="assets/images/Trash_Can_Gray.png"
                className={classes.trash_img_office_shots}
                onClick={handleDelete}
              />
            )}
            {refreshIcon && (
              <img
                src="assets/images/swap_gray.png"
                className={classes.refresh_img}
                onClick={handleButtonClick}
              />
            )}
          </>
        )}
      </div>
    );
  };

  const returnCompanyActionButtons = () => {
    return (
      <div className={classes.action_btns}>
        {uploadedImage.image && deleteIcon && (
          <img
            src="assets/images/Trash_Can_Gray.png"
            className={`${classes.trash_img} ${props.deleteStyles}`}
            onClick={handleDelete}
          />
        )}
      </div>
    );
  };

  const returnMandatoryImageError = () => {
    return (
      <div className={classes.img_mandatory_err}>
        <Text
          size={11}
          color={color.primary_palette.tricks_red}
          family="gillsans_sb"
        >
          <img src="assets/icons/info_1.svg" />
          {strings.company_wizard.step5.errors.image}
        </Text>
      </div>
    );
  };
  const mouseOut = (section) => () => {
    if (section == "companyLogo") {
      setDefaultPlaceImage("assets/images/Company Logo Initial.svg");
    } else if (section === "coverImage") {
      setDefaultPlaceImage("assets/images/Cover Image.png");
    } else if (section === "topFeatureImage1") {
      setDefaultPlaceImage("assets/images/Feature Image 1 Initial.svg");
    } else if (section === "topFeatureImage2") {
      setDefaultPlaceImage("assets/images/Feature Image 2 Initial.svg");
    } else {
      setDefaultPlaceImage("assets/images/Feature Image 2 Initial.svg");
    }
  };
  const mouseOver = (section) => () => {
    if (section == "companyLogo") {
      setDefaultPlaceImage("assets/images/Company Logo Hover.svg");
    } else if (section === "coverImage") {
      setDefaultPlaceImage("assets/images/Cover Image Hover.png");
    } else if (section === "topFeatureImage1") {
      setDefaultPlaceImage("assets/images/Feature Image 1 Hover.svg");
    } else if (section === "topFeatureImage2") {
      setDefaultPlaceImage("assets/images/Feature Image 2 Hover.svg");
    } else {
      setDefaultPlaceImage("assets/images/Feature Image 2 Hover.svg");
    }
  };
  return (
    <>
      <div
        className={`${classes.circle} ${margin10} ${marginLeft_9} ${marginRight_9}`}
      >
        <label className={classes.label_pos}>
          <img
            src={uploadedImage.image || defaultPlaceImage}
            className={className}
            onClick={!uploadedImage.image ? handleButtonClick : recropImage}
            onMouseLeave={mouseOut(props.name)}
            onMouseEnter={mouseOver(props.name)}
          />
          {get(uploadedImage, "image", "") && pencilEdit && (
            <img
              src="assets/images/pencil_icon.png"
              className={classes.pencil_edit}
              onClick={recropImage}
            />
          )}
          {!uploadedImage.image && plus && (
            <img
              src="assets/images/water_cooler.png"
              className={classes.plus}
              onClick={handleButtonClick}
            />
          )}
          {props.type === "companyPitch" && returnCompanyActionButtons()}
          {props.type === "officeShots" && returnActionButtons()}
        </label>

        {/* IF BUTTON LABEL IS NOT REQUIRED */}
        {!buttonLabel && !noErrorDisplay && (
          <Row className={`${errorStyles} ${classes.error_msg_container}`}>
            {get(errors, "image", "") && (
              <img
                src="assets/images/warning.svg"
                className={classes.warn_icon}
              />
            )}
            <Text
              size={15}
              color={color.primary_palette.christmas_red}
              family="gillsans_r"
              className={`${classes.errorMessage} `}
            >
              {get(errors, "image", "")}
            </Text>{" "}
          </Row>
        )}

        {buttonLabel && (
          <div className={`${classes.img_spacing}`}>
            <div className={classes.add_btn_wrap}>
              <label
                className={`${classes.logo_btn} ${props.button_padding} ${margin_left_20}`}
                onClick={handleButtonClick}
              >
                {uploadedImage.image ? (
                  <img
                    className={classes.refreshIcon}
                    src="assets/images/refreshIcon.png"
                  />
                ) : (
                  <Lil_Plus_filled />
                )}
                {buttonLabel}
              </label>
              {props.mandatoryImage && (
                <Element_Required_Icon
                  color={color.form_colors.blueberry_purple}
                />
              )}
            </div>
            {props.mandatoryError && returnMandatoryImageError()}

            {/* IF BUTTON LABEL IS REQUIRED */}
            {!noErrorDisplay && (
              <Row className={`${errorStyles} ${classes.error_msg_container}`}>
                {get(errors, "image", "") && (
                  <img
                    src="assets/images/warning.svg"
                    className={classes.warn_icon}
                  />
                )}
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} ${errorStyles}`}
                >
                  {get(errors, "image", "")}
                </Text>{" "}
              </Row>
            )}
          </div>
        )}

        <input
          type="file"
          ref={uploadRef}
          onChange={imageUpload}
          accept="image/*"
          className={classes.input_circle}
        />
      </div>
      <CustomModal open={uploadedImage.openCropperModal}>
        <CropperModal
          shape={cropShape}
          aspect={cropSize}
          image={
            get(uploadedImage, "cImage", "") || get(uploadedImage, "image", "")
          }
          setCroppedImage={setCroppedImage}
          chooseNew={handleButtonClick}
          onClose={handleCropperModal}
          errorMessage={get(errors, "image", "")}
          disableBackdropClick={false}
          edit={props.edit}
        />
      </CustomModal>
    </>
  );
}
export default StylesSheet(AddImageHoverLogo);
