import React from "react";
import { find, get } from "lodash";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import strings from "../../../utilities/strings";
import highlightStyle from "../styles/highlight_style";
import CustomTextArea from "../../inputs/custom_text_area";
import CustomInputCount from "../../inputs/custom_input_count";
import AddImageLogo from "../add_image_logo";
import Element_Required_Icon from "../../../components/data_display/icons/ElementRequiered";

import { pxToRem, color } from "../../../utilities/themes";

function LayoutOne(props) {
  const {
    classes,
    handleChange,
    layouts,
    imageUpload,
    id,
    validateMandatoryImage,
    edit,
  } = props;
  const layout = find(layouts, { layoutType: "layout1" });

  return (
    <>
      <Row className={`${classes.layout1_blk}`}>
        <Row className={classes.layout1_img} style={{ width: "260px" }}>
          <AddImageLogo
            defaultImage="assets/images/Shadow Images _Layout 1.svg"
            buttonLabel={strings.company_wizard.step5.titles.Add_Image}
            className={`${classes.upload_box}`}
            mandatoryError={validateMandatoryImage(layout)}
            mandatoryImage
            index={0}
            layoutImage={layout.url}
            cropShape="newRect"
            type="officeShots"
            imageUpload={imageUpload}
            id={id}
            edit={edit}
            margin_left_20={classes.margin_left_20}
            button_padding={classes.addbtn_padding}
            layoutData={layout}
          />
          <Text className={classes.helper_name}>JPG or PNG | 200KB to 2MB</Text>
        </Row>
        <Row className={`${classes.layout1}`}>
          <Row className={classes.high_blk_1}>
            <Text
              className={classes.highlight}
              size={pxToRem(16)}
              color={color.primary_palette.franklin_purple}
              family="avenir_black_r"
            >
              {strings.company_wizard.step5.titles.Highlight_Title}
              <Element_Required_Icon />
            </Text>
            <CustomInputCount
              enableCount
              countValue={150}
              countColor={color.secondary_palette.grays.dim_grey}
              defaultValue={get(layout, "title", "")}
              className={classes.highlight_input}
              onBlur={handleChange("layout1")}
              name="title"
              placeholder={
                strings.company_wizard.step5.titles.Highlight_Title_input
              }
            />
            {get(layout, "errors.title", false) && (
              <Row className={classes.errorMsgLayout}>
                <img src="assets/icons/info_1.svg" />
                {strings.companyCulture.title_validation}
              </Row>
            )}
          </Row>
          <Row className={classes.high_blk_1}>
            <Text
              className={classes.highlight_body_content}
              size={pxToRem(16)}
              color={color.primary_palette.franklin_purple}
              family="avenir_black_r"
            >
              {strings.company_wizard.step5.titles.Body_Content}
              <Element_Required_Icon />
            </Text>
            <CustomTextArea
              enableCount
              enableCurrentCount
              placeholder={"Minimum of 100 characters required"}
              countValue={1100}
              countColor={color.secondary_palette.grays.dim_grey}
              defaultValue={get(layout, "content", "")}
              name="content"
              // showErrText
              onBlur={handleChange("layout1")}
              className={classes.l1_description}
            />
            {get(layout, "errors.content", false) &&
              !get(layout, "errors.tmc", false) && (
                <Row className={classes.errormsg}>
                  <img src="assets/icons/info_1.svg" />
                  {strings.companyCulture.description_validation}
                </Row>
              )}
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default highlightStyle(LayoutOne);
