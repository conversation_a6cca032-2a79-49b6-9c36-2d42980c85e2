import React from "react";
import { find, get } from "lodash";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import strings from "../../../utilities/strings";
import highlightStyle from "../styles/highlight_style";
import CustomTextArea from "../../inputs/custom_text_area";
import CustomInputCount from "../../inputs/custom_input_count";
import AddImageLogo from "../add_image_logo";
import Element_Required_Icon from "../../../components/data_display/icons/ElementRequiered";

import { pxToRem, color } from "../../../utilities/themes";

function LayoutFour(props) {
  const {
    classes,
    handleChange,
    layouts,
    imageUpload,
    id,
    validateMandatoryImage,
    edit,
  } = props;
  const layout = find(layouts, { layoutType: "layout4" });

  return (
    <>
      <Row className={`${classes.margin_right_20} ${classes.layout1_blk}`}>
        <Row
          className={classes.layout2_img_layout4}
          style={{ position: "relative" }}
        >
          <AddImageLogo
            mandatoryImage
            defaultImage="assets/images/Shadow Images _Layout 4.svg"
            buttonLabel={strings.company_wizard.step5.titles.Add_Image}
            className={`${classes.upload_box_L2} ${classes.margin_Top_10}`}
            index={0}
            layoutImage={layout.url}
            cropShape="rect2_2"
            type="officeShots"
            edit={edit}
            imageUpload={imageUpload}
            id={id}
            button_padding={classes.addbtn_padding}
            mandatoryError={validateMandatoryImage(layout)}
          />
          <Text
            className={classes.helper_name}
            style={{ position: "absolute", bottom: "-10px", left: "72px" }}
          >
            JPG or PNG | 200KB to 2MB
          </Text>
        </Row>
        <Row>
          <Row className={classes.layout1_in4}>
            <Row className={`${classes.high_blk_4} ${classes.paddingTop_0}`}>
              <Text
                className={classes.highlight}
                size={pxToRem(16)}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
              >
                {strings.company_wizard.step5.titles.Highlight_Title}
                <Element_Required_Icon />
              </Text>
              <CustomInputCount
                enableCount
                defaultValue={get(layout, "title", "")}
                countValue={150}
                // showErrText
                countColor={color.secondary_palette.grays.dim_grey}
                name="title"
                onBlur={handleChange("layout4")}
                className={classes.highlight_input}
                placeholder={
                  strings.company_wizard.step5.titles.Highlight_Title_input
                }
              />
              {get(layout, "errors.title", false) && (
                <Row className={classes.errormsg}>
                  <img src="assets/icons/info_1.svg" />
                  {strings.companyCulture.title_validation}
                </Row>
              )}
            </Row>
            <Row className={`${classes.high_blk_4} ${classes.padding_top_10}`}>
              <Text
                className={classes.highlight_body_content}
                size={pxToRem(16)}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
              >
                {strings.company_wizard.step5.titles.Body_Content}
                <Element_Required_Icon />
              </Text>
              <CustomTextArea
                enableCount
                enableCurrentCount
                placeholder={"Minimum of 100 characters required"}
                defaultValue={get(layout, "content", "")}
                countValue={450}
                // showErrText
                countColor={color.secondary_palette.grays.dim_grey}
                onBlur={handleChange("layout4")}
                name="content"
                className={`${classes.l4_l5_para1} ${classes.Paragraph1_textarea}`}
              />
              {get(layout, "errors.content", false) &&
                !get(layout, "errors.tmc", false) && (
                  <Row className={classes.errormsg}>
                    <img src="assets/icons/info_1.svg" />
                    {strings.companyCulture.description_validation}
                  </Row>
                )}
            </Row>
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default highlightStyle(LayoutFour);
