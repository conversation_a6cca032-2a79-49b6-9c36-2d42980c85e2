import React from "react";

import { find, get } from "lodash";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import strings from "../../../utilities/strings";
import highlightStyle from "../styles/highlight_style";
import CustomTextArea from "../../inputs/custom_text_area";
import CustomInputCount from "../../inputs/custom_input_count";
import AddImageLogo from "../add_image_logo";
import Element_Required_Icon from "../../../components/data_display/icons/ElementRequiered";

import { pxToRem, color } from "../../../utilities/themes";

function LayoutTwo(props) {
  const { classes, handleChange, layouts, imageUpload, id, edit } = props;
  const layout = find(layouts, { layoutType: "layout2" });

  const validateMandatoryImage = (index) => {
    if (layout) {
      const contentType = index === 0 ? "paragraph1" : "paragraph2";
      const titleType = index === 0 ? "title" : "title2";
      if (layout[contentType] && layout[titleType]) {
        if (!layout.url) {
          return true;
        }
        if (!layout.url[index]) {
          return true;
        }
      }
    }
    return false;
  };

  return (
    <>
      <Row className={classes.layout_block}>
        <Row>
          <Row className={`${classes.l2_content_wrap} ${classes.layout1}`}>
            <Row className={classes.high_blk}>
              <Text
                className={classes.highlight}
                size={pxToRem(16)}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
              >
                {strings.company_wizard.step5.titles.Highlight_Title}
                <Element_Required_Icon />
              </Text>
              <CustomInputCount
                enableCount
                defaultValue={get(layout, "title", "")}
                countValue={150}
                countColor={color.secondary_palette.grays.dim_grey}
                name="title"
                className={classes.highlight_input}
                onBlur={handleChange("layout2")}
                placeholder={
                  strings.company_wizard.step5.titles.Highlight_Title_input
                }
              />
              {get(layout, "errors.title", false) && (
                <Row className={classes.errormsg}>
                  <img src="assets/icons/info_1.svg" />
                  {strings.companyCulture.title_validation}
                </Row>
              )}
            </Row>
            <Row className={`${classes.high_blk} ${classes.l2_content1}`}>
              <Text
                className={classes.highlight_body_content}
                size={pxToRem(16)}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
              >
                {strings.company_wizard.step5.titles.Paragraph1}
                <Element_Required_Icon />
              </Text>
              <CustomTextArea
                enableCount
                enableCurrentCount
                name="paragraph1"
                placeholder={"Minimum of 100 characters required"}
                defaultValue={get(layout, "paragraph1", "")}
                onBlur={handleChange("layout2")}
                countValue={450}
                // showErrText
                countColor={color.secondary_palette.grays.dim_grey}
                className={`${classes.l2_para1} ${classes.Paragraph1_textarea}`}
              />
              {get(layout, "errors.paragraph1", false) &&
                !get(layout, "errors.tmc", false) && (
                  <Row className={classes.errormsg}>
                    <img src="assets/icons/info_1.svg" />
                    {strings.companyCulture.description_validation}
                  </Row>
                )}
            </Row>
          </Row>
          <Row className={`${classes.layout2_img}`}>
            <AddImageLogo
              defaultImage="assets/images/Shadow Images _Layout 2A.svg"
              buttonLabel={strings.company_wizard.step5.titles.Add_Image}
              button_padding={classes.addbtn_padding}
              className={`${classes.upload_box_L2_1} ${classes.margin_Top}`}
              index={0}
              edit={edit}
              layoutImage={layout.url}
              cropShape="rect2"
              type="officeShots"
              imageUpload={imageUpload}
              id={id}
              mandatoryError={validateMandatoryImage(0)}
              mandatoryImage
            />
            <Text className={classes.helper_name}>
              JPG or PNG | 200KB to 2MB
            </Text>
          </Row>
        </Row>
        <Row>
          <Row>
            <AddImageLogo
              defaultImage="assets/images/Shadow Images _Layout 2B.svg"
              buttonLabel={strings.company_wizard.step5.titles.Add_Image}
              className={`${classes.upload_box_L2_2} ${classes.margin_Top_9}`}
              index={1}
              layoutImage={layout.url}
              type="officeShots"
              imageUpload={imageUpload}
              edit={edit}
              cropShape="rect2_2"
              button_padding={classes.addbtn_padding}
              id={id}
              mandatoryError={validateMandatoryImage(1)}
              mandatoryImage
            />
            <Text
              className={classes.helper_name}
              style={{
                position: "absolute",
                bottom: "78px",
                marginLeft: "72px",
              }}
            >
              JPG or PNG | 200KB to 2MB
            </Text>
          </Row>
          <Row className={`${classes.high_blk_l2} ${classes.l2_content2}`}>
            <Text
              className={classes.highlight}
              size={pxToRem(16)}
              color={color.primary_palette.franklin_purple}
              family="avenir_black_r"
            >
              {strings.company_wizard.step5.titles.Highlight_Title}
            </Text>
            <CustomInputCount
              enableCount
              defaultValue={get(layout, "title2", "")}
              countValue={150}
              countColor={color.secondary_palette.grays.dim_grey}
              name="title2"
              className={classes.highlight_input}
              onBlur={handleChange("layout2")}
              placeholder={
                strings.company_wizard.step5.titles.Highlight_Title_input
              }
            />
            <Text
              className={classes.highlight}
              size={pxToRem(16)}
              color={color.primary_palette.franklin_purple}
              family="avenir_black_r"
            >
              {strings.company_wizard.step5.titles.Paragraph2}
              <Element_Required_Icon />
            </Text>
            <CustomTextArea
              enableCount
              enableCurrentCount
              placeholder={"Minimum of 100 characters required"}
              name="paragraph2"
              defaultValue={get(layout, "paragraph2", "")}
              onBlur={handleChange("layout2")}
              countValue={450}
              // showErrText
              countColor={color.secondary_palette.grays.dim_grey}
              className={`${classes.l2_para2} ${classes.Paragraph1_textarea}`}
            />
            {get(layout, "errors.paragraph2", false) &&
              !get(layout, "errors.tmc", false) && (
                <Row className={classes.errormsg}>
                  <img src="assets/icons/info_1.svg" />
                  {strings.companyCulture.description_validation}
                </Row>
              )}
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default highlightStyle(LayoutTwo);
