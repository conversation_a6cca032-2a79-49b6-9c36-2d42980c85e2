import React from "react";
import { find, get } from "lodash";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import strings from "../../../utilities/strings";
import highlightStyle from "../styles/highlight_style";
import CustomTextArea from "../../inputs/custom_text_area";
import CustomInputCount from "../../inputs/custom_input_count";
import AddImageLogo from "../add_image_logo";
import Element_Required_Icon from "../../../components/data_display/icons/ElementRequiered";

import { pxToRem, color } from "../../../utilities/themes";

function LayoutFive(props) {
  const {
    classes,
    handleChange,
    edit,
    layouts,
    imageUpload,
    id,
    validateMandatoryImage,
  } = props;
  const layout = find(layouts, { layoutType: "layout5" });

  return (
    <>
      <Row className={classes.layout_block}>
        <Row>
          <Row className={classes.layout1} style={{ marginRight: "15px" }}>
            <Row className={classes.high_blk}>
              <Text
                className={classes.highlight}
                size={pxToRem(16)}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
              >
                {strings.company_wizard.step5.titles.Highlight_Title}
                <Element_Required_Icon />
              </Text>
              <CustomInputCount
                enableCount
                // showErrText
                defaultValue={get(layout, "title", "")}
                name="title"
                onBlur={handleChange("layout5")}
                countValue={150}
                countColor={color.secondary_palette.grays.dim_grey}
                className={classes.highlight_input}
                placeholder={
                  strings.company_wizard.step5.titles.Highlight_Title_input
                }
              />
              {get(layout, "errors.title", false) && (
                <Row className={classes.errormsg}>
                  <img src="assets/icons/info_1.svg" />
                  {strings.companyCulture.title_validation}
                </Row>
              )}
            </Row>
            <Row
              className={`${classes.high_blk} ${classes.layout5_content_blk}`}
            >
              <Text
                className={classes.highlight_body_content}
                size={pxToRem(16)}
                color={color.primary_palette.franklin_purple}
                family="avenir_black_r"
              >
                {strings.company_wizard.step5.titles.Body_Content}
                <Element_Required_Icon />
              </Text>
              <CustomTextArea
                enableCount
                enableCurrentCount
                // showErrText
                defaultValue={get(layout, "content", "")}
                placeholder={"Minimum of 100 characters required"}
                name="content"
                onBlur={handleChange("layout5")}
                countValue={450}
                countColor={color.secondary_palette.grays.dim_grey}
                className={`${classes.l4_l5_para1} ${classes.Paragraph1_textarea}`}
                style={{ height: "146px" }}
              />
              {get(layout, "errors.content", false) &&
                !get(layout, "errors.tmc", false) && (
                  <Row className={classes.errormsg}>
                    <img src="assets/icons/info_1.svg" />
                    {strings.companyCulture.description_validation}
                  </Row>
                )}
            </Row>
          </Row>
          <Row
            className={`${classes.layout2_img}`}
            style={{ marginTop: "4px" }}
          >
            <AddImageLogo
              mandatoryImage
              defaultImage="assets/images/Shadow Images _Layout 5.svg"
              buttonLabel={strings.company_wizard.step5.titles.Add_Image}
              className={`${classes.upload_box_L2_1}`}
              index={0}
              cropShape="rect2"
              layoutImage={layout.url}
              edit={edit}
              type="officeShots"
              imageUpload={imageUpload}
              id={id}
              button_padding={classes.addbtn_padding}
              mandatoryError={validateMandatoryImage(layout)}
            />
            <Text
              className={classes.helper_name}
              style={{ position: "absolute", marginLeft: "72px" }}
            >
              JPG or PNG | 200KB to 2MB
            </Text>
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default highlightStyle(LayoutFive);
