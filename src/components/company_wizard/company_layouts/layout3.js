import React from "react";
import { find, get } from "lodash";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import strings from "../../../utilities/strings";
import highlightStyle from "../styles/highlight_style";
import CustomTextArea from "../../inputs/custom_text_area";
import CustomInputCount from "../../inputs/custom_input_count";
import AddImageLogo from "../add_image_logo";
import Element_Required_Icon from "../../../components/data_display/icons/ElementRequiered";

import { pxToRem, color } from "../../../utilities/themes";

function LayoutThree(props) {
  const {
    classes,
    handleChange,
    layouts,
    imageUpload,
    edit,
    id,
    validateMandatoryImage,
  } = props;
  const layout = find(layouts, { layoutType: "layout3" });

  return (
    <>
      <Row className={classes.layout1_blk}>
        <Row className={`${classes.l3_content1} ${classes.layout1}`}>
          <Row className={classes.high_blk_3}>
            <Text
              className={classes.highlight}
              size={pxToRem(16)}
              color={color.primary_palette.franklin_purple}
              family="avenir_black_r"
            >
              {strings.company_wizard.step5.titles.Highlight_Title}
              <Element_Required_Icon />
            </Text>
            <CustomInputCount
              enableCount
              defaultValue={get(layout, "title", "")}
              name="title"
              countValue={150}
              countColor={color.secondary_palette.grays.dim_grey}
              onBlur={handleChange("layout3")}
              className={classes.highlight_input}
              // showErrText
              placeholder={
                strings.company_wizard.step5.titles.Highlight_Title_input
              }
            />
            {get(layout, "errors.title", false) && (
              <Row className={classes.errormsg}>
                <img src="assets/icons/info_1.svg" />
                {strings.companyCulture.title_validation}
              </Row>
            )}
          </Row>
          <Row className={classes.high_blk_3} style={{ marginTop: "25px" }}>
            <Text
              className={classes.highlight}
              size={pxToRem(16)}
              color={color.primary_palette.franklin_purple}
              family="avenir_black_r"
            >
              {strings.company_wizard.step5.titles.Body_Content}
              <Element_Required_Icon />
            </Text>
            <CustomTextArea
              enableCount
              enableCurrentCount
              placeholder={"Minimum of 100 characters required"}
              defaultValue={get(layout, "content", "")}
              name="content"
              countValue={750}
              // showErrText
              countColor={color.secondary_palette.grays.dim_grey}
              onBlur={handleChange("layout3")}
              className={classes.bodycontent_textarea_l3}
            />
            {get(layout, "errors.content", false) &&
              !get(layout, "errors.tmc", false) && (
                <Row className={classes.errormsg}>
                  <img src="assets/icons/info_1.svg" />
                  {strings.companyCulture.description_validation}
                </Row>
              )}
          </Row>
        </Row>
        <Row className={`${classes.layout2_img} ${classes.mr_top_l3}`}>
          <AddImageLogo
            mandatoryImage
            defaultImage="assets/images/Shadow Images _Layout 3A.svg"
            buttonLabel={strings.company_wizard.step5.titles.Add_Image}
            className={classes.upload_box_L2_2}
            index={0}
            edit={edit}
            type="officeShots"
            cropShape="rect2_2"
            layoutImage={layout.url}
            imageUpload={imageUpload}
            id={id}
            button_padding={classes.addbtn_padding}
            mandatoryError={validateMandatoryImage(layout, 0)}
          />
          <Text className={classes.helper_name}>JPG or PNG | 200KB to 2MB</Text>
          <AddImageLogo
            mandatoryImage
            defaultImage="assets/images/Shadow Images _Layout 3B.svg"
            buttonLabel={strings.company_wizard.step5.titles.Add_Image}
            className={`${classes.upload_box_L2_2} `}
            index={1}
            edit={edit}
            type="officeShots"
            cropShape="rect2_2"
            layoutImage={layout.url}
            imageUpload={imageUpload}
            id={id}
            button_padding={classes.addbtn_padding}
            mandatoryError={validateMandatoryImage(layout, 1)}
          />
          <Text className={classes.helper_name}>JPG or PNG | 200KB to 2MB</Text>
        </Row>
      </Row>
    </>
  );
}

export default highlightStyle(LayoutThree);
