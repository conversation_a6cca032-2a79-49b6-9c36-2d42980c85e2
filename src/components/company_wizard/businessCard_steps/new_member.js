import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { MenuItem, InputAdornment } from "@material-ui/core";
import { get, map, find, filter, omit } from "lodash";
import uuidv4 from "uuid/v4";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import strings from "../../../utilities/strings";
import Chevron_Down_Icon from "../../data_display/icons/Arrow_Chevron_Down";
import CustomCheckbox from "../../inputs/custom_checkbox";
import businessCardStyles from "../styles/businessCardStyles";
import CustomTextField from "../../inputs/custom_textfields";
import CustomSelect from "../../inputs/custom_select";
import CustomButton from "../../navigations/custom_buttons";
import Lil_Plus_filled from "../../data_display/icons/Lil_Plus_filled";
import BusinessCardTwo from "./businessCard_step2";
import CustomAutoComplete from "../../inputs/custom_auto_complete";
import CustomScrollbars from "../../data_display/custom_scroll";
import PingAdmin from "../../modals/pingAdmin";
import PingAdminSuccess from "../../modals/messageSentModal";
import CompanyExistsWarningModal from "../../modals/companyAlready";
import JoinUsSuccessModal from "../../modals/new_joinModal";

import {
  PHONE_NUMBER_FORMAT,
  REGEX_EMAIL,
  WEBSITE_URL,
  NON_USA_REGEX,
} from "../../../validations/validations";
import { color, pxToRem } from "../../../utilities/themes";
import { ProfileActions, LoginActions } from "../../../redux/actions";
import { ProfileTypes } from "../../../redux/actionTypes";
import Element_Required_Icon from "../../../components/data_display/icons/ElementRequiered";
import { BASEURL, PUBLIC_PROFILE } from "../../../constants";
import { useHistory } from "react-router-dom";

function NewMember(props) {
  const { classes, handleNext } = props;
  const companies = useSelector((state) => state.Profile.companyBasicList);
  const CompanyListByUser = useSelector(
    (state) => state.Profile.CompanyListByUser
  );
  const history = useHistory();
  const userProfieInfo = useSelector((state) => state.Profile);
  const businesscardData = useSelector(
    (state) => state.Profile.refBusinessCard
  );

  const countries_flags = useSelector((state) => state.Configs.countries_flags);
  const userInfo = useSelector((state) => state.Profile.profileUserData);
  const userData = useSelector((state) => state.Profile.companyinfo);

  const dispatch = useDispatch();
  const [adminInfo, setAdminInfo] = useState({});

  const [values, setValues] = useState({
    businessCard: [],
    aboutMe: {
      profileUrl: {},
      email: "",
      phoneNumber: "",
    },
    isTeamMember: false,
    isOfficialRepresentative: false,
    isCurrentEmployee: false,
    errors: {},
    showAlertModal: false,
    activeField: "",
    showPingAdmin: false,
    showPingAdminSuccess: false,
    showTmSuccessModal: false,
    sameAsCompany: false,
    showCountryDropdown: false,
    hasExistingAdmin: false,
    isCurrentEmployeeError: false,
    aboutMeEmailError: false,
  });

  const fetchData = () => {
    dispatch(ProfileActions.getProfileDataOfInfo("COMPANYINFO"));
    // dispatch(ProfileActions.fetchAllCompanies());
    dispatch(ProfileActions.fetchBasicCompaniesInfo());
    dispatch(ProfileActions.fetchAllCountries());
  };

  useEffect(() => {
    !businesscardData && fetchData();
  }, []);

  useEffect(() => {
    if (userInfo) {
      setValues({
        ...values,
        aboutMe: {
          profileUrl: {},
          email: get(userInfo, "email", ""),
          phone: get(userInfo, "phoneNumber", "")
            ? formatPhoneNumber(get(userInfo, "phoneNumber", ""), {
                flag: { code: "US" },
              })
            : get(userInfo, "phoneNumber", ""),
        },
      });
    }
    if (countries_flags) {
      if (!businesscardData) {
        if (values.businessCard.length === 0 && countries_flags) {
          handleAddBusinessCard(true);
        }
        return;
      } else {
        setValues({ ...values, ...businesscardData });
      }
    }
  }, [businesscardData, countries_flags, userInfo]);

  useEffect(() => {
    if (companies && companies.length) {
      const foundRecord = find(companies, {
        _id: localStorage.getItem("companyId"),
      });
      setAdminInfo(foundRecord);
    }
  }, [companies]);

  const handleAddBusinessCard = (isTrue) => {
    // ADDING NEW BUSINESS CARD
    const flag = find(countries_flags, { code: "US" });
    // SETTING US AS DEFAULT SELECTED FLAG
    values.businessCard = [
      ...values.businessCard,
      {
        id: uuidv4(),
        isHeadOffice: isTrue === true ? true : false,
        companyId: {},
        selectedOffice: {},
        flag: { ...flag },
        email: get(userInfo, "email", ""),
        phoneNumber: get(userInfo, "phoneNumber", "")
          ? formatPhoneNumber(get(userInfo, "phoneNumber", ""), {
              flag: { code: "US" },
            })
          : get(userInfo, "phoneNumber", ""),
      },
    ];
    setValues({ ...values });
  };

  const imageUpload = (imageData, name) => {
    // PROFILE IMAGE UPLOAD
    const { aboutMe } = values;
    aboutMe.profileUrl = imageData;
    setValues({ ...values });
  };

  const onPhoneNumberBlur = (card) => () => {
    // FORMATTING PHONE NUMBER(only us number)
    if (card.flag.code !== "US") {
      return;
    }
    if (card.phoneNumber) {
      card.phoneNumber = card.phoneNumber
        .toString()
        .replace(PHONE_NUMBER_FORMAT, "$1.$2.$3");
    }
    setValues({ ...values, activeField: {} });
  };

  const formatPhoneNumber = (value, card) => {
    const country = get(card, "flag.code", "");
    if (country !== "US") {
      return value;
    }
    return value.replace(PHONE_NUMBER_FORMAT, "$1.$2.$3");
  };

  const deformatPhoneNumber = (number) => {
    // DEFORMATTING
    if (!number) {
      return;
    }
    return number.replace(/[().\s/]/g, "");
  };
  const onPhoneNumberFocus = (card) => (event) => {
    // ON FOCUS REMOVING FORMAT
    const { name, value } = event.target;
    if (card && card.phoneNumber) {
      card.phoneNumber = card.phoneNumber.replace(/[().\s/]/g, "");
      setValues({ ...values, activeField: { name: "phone", id: card.id } });
      return;
    }
    if (!card && value) {
      setValues({
        ...values,
        aboutMe: { ...values.aboutMe, [name]: deformatPhoneNumber(value) },
      });
      return;
    }
  };

  const onCompanyBlur = (card, index) => (e) => {
    const { companyId } = card;
    const { value } = e.target;
    const flag = find(countries_flags, { code: "US" });
    if (companyId && companyId._id && companyId.name == value.trim()) {
      // SELECTED FORM DROPDOWN
      card.isNewCompany = false;
      setValues({
        ...values,
      });
      return;
    } else {
      if (get(card, "companyId._id", false)) {
        // IF ENTITY IS CHANGED
        setValues({
          ...values,
          businessCard: [
            {
              companyId: { name: e.target.value.trim() },
              isNewCompany: true,
              flag: { ...flag },
              id: uuidv4(),
            },
          ],
          aboutMe: {
            profileUrl: {},
            email: get(userInfo, "email", ""),
            phone: get(userInfo, "phoneNumber", "")
              ? formatPhoneNumber(get(userInfo, "phoneNumber", ""), {
                  flag: { code: "US" },
                })
              : get(userInfo, "phoneNumber", ""),
          },
          isTeamMember: false,
          isCurrentEmployee: false,
          isOfficialRepresentative: false,
          hasExistingAdmin: false,
        });
        return;
      }
      // IF NOTHING IS SELECTED AND IT IS A NEW ENTERED VALUE
      if (value || index > 0) {
        card.companyId = { name: value.trim() };
        card.isNewCompany = value.length > 0 ? true : false;
        card.flag = { ...flag };
        setValues({
          ...values,
          aboutMe: {
            profileUrl: {},
            email: get(userInfo, "email", ""),
            phone: get(userInfo, "phoneNumber", "")
              ? formatPhoneNumber(get(userInfo, "phoneNumber", ""), {
                  flag: { code: "US" },
                })
              : get(userInfo, "phoneNumber", ""),
          },
          isTeamMember: false,
          isCurrentEmployee: false,
          isOfficialRepresentative: false,
          hasExistingAdmin: false,
        });
      } else {
        // RESET IF THERE IS NOT VALUE IN MASTER BUSINESS CARD
        setValues({
          businessCard: [
            {
              id: uuidv4(),
              isHeadOffice: true,
              companyId: {},
              selectedOffice: {},
              flag: { ...flag },
            },
          ],
          aboutMe: {
            profileUrl: {},
            email: get(userInfo, "email", ""),
            phone: get(userInfo, "phoneNumber", ""),
          },
          isTeamMember: false,
          isOfficialRepresentative: false,
          isCurrentEmployee: false,
          errors: {},
          showAlertModal: false,
          activeField: "",
          hasExistingAdmin,
        });
      }
    }
  };

  const handleBranchBlur = (card) => (event) => {
    const { value } = event.target;
    if (card.companyName !== value.trim()) {
      card.companyId = { name: value.trim() };
      card.companyName = value.trim();
      setValues({ ...values });
    }
    return;
  };

  const handleCompanySelect = (event, value, card) => {
    if (!value) {
      return;
    }
    const flag = find(countries_flags, { code: "US" });
    const { address } = value;
    // const headquarter = find(address, { type: "headquarters" });
    const { city, state, zip } = address;
    const phoneNumber = value.phoneNumber;
    const email = value.email;
    const website = value.website;
    const filteredAdmin = get(value, "companyAdmins").filter(
      (each) => each.email == get(userInfo, "email", "")
    );
    const hasExistingAdmin =
      get(value, "companyAdmins.length") !== 0 &&
      get(filteredAdmin, "length") === 0
        ? true
        : false;
    localStorage.setItem("companyId", value._id);
    localStorage.setItem("newCompanyId", value._id);
    localStorage.setItem("companyName", value.name);
    /**
     * STATE RESET ON COMPANY CHANGE WITH (companyId = selected value)
     *
     **/

    setValues({
      ...values,
      businessCard: [
        {
          companyId: value,
          flag: { ...flag },
          city,
          state,
          zip,
          address: get(address, "address", ""),
          address1: get(address, "address1", ""),
          website,
          phoneNumber,
          email,
          id: uuidv4(),
        },
      ],
      aboutMe: {
        profileUrl: "",
        email: get(userInfo, "email", ""),
        phone: get(userInfo, "phoneNumber", ""),
      },
      isTeamMember: false,
      isCurrentEmployee: false,
      isOfficialRepresentative: false,
      newErrors: [],
      hasExistingAdmin,
    });
  };

  const handleOfficeSelect = (address, name, card) => (e) => {
    if (card) {
      // BUSINESS CARD OFFICE SELECT (ARRAY)
      card[name] = address;
      setValues({ ...values });
    } else {
      // STEP2 OFFICE SELECT
      const { aboutMe } = values;
      aboutMe[name] = address;
      setValues({ ...values });
    }
  };

  const handleCardValidation = (name, value, card) => {
    const cardId = get(card, "id", null)
      ? get(card, "id", null)
      : get(card, "_id", null);

    if (name === "website" && !value) {
      // NOT VALIDATING WEBSITE IF NO VALUE ENTERED
      const { errors } = values;
      errors[cardId] = errors[cardId] || {};
      errors[cardId] = { ...errors[cardId], [name]: false };
      setValues({ ...values });
      return;
    }

    const regexTest =
      name === "email" ? REGEX_EMAIL.test(value) : WEBSITE_URL.test(value);
    if (!regexTest) {
      const { errors } = values;
      errors[cardId] = errors[cardId] || {};
      errors[cardId] = { ...errors[cardId], [name]: true };
      setValues({ ...values });
      return;
    }
    errors[cardId] = errors[cardId] || {};
    errors[cardId][name] = false;

    setValues({ ...values, errors: {} });
  };

  const handleBusinessCardChange = (card) => (event) => {
    // BUSINESS CARD HANDLE CHANGE FUNTION
    const { name, value } = event.target;
    if (name === "email" || name === "website") {
      // VALIDATIONG EMAIL AND WEBSITE FIELDS
      handleCardValidation(name, value, card);
    }
    if (name === "zip") {
      const { companyId } = card;
      const duplicateData = filter(companies, (each) => {
        const addressData = find(each.address, { zip: value });
        return companyId.name === each.name && addressData;
      });
      if (duplicateData.length > 0) {
        setValues({
          ...values,
          showAlertModal: true,
          foundCompany: duplicateData[0],
          newErrors: [],
        });
        return;
      }
    }
    if (name === "phoneNumber") {
      const country = get(card, "flag.code", "");
      const val = deformatPhoneNumber(value);
      if (country === "US") {
        // DEFORMATTING PHONE NUMBER AND SENDING TO VALIDATION
        if (isNaN(val)) {
          /**returning if input is NaN */
          if (!val) {
            card[name] = "";
            setValues({ ...values, newErrors: [] });
          }
          return;
        }
        /**saving deformatted value to phone number in businesscard (formatted) */
        card[name] = val;
      } else {
        const validationTest = NON_USA_REGEX.test(value);
        if (!validationTest) {
          card[name] = value;
          setValues({ ...values, newErrors: [] });
          return;
        }
        return;
      }
    } else {
      card[name] = value;
    }
    setValues({ ...values, newErrors: [] });
  };

  const handleBusinessCardBlur = (card) => (event) => {
    const { name, value } = event.target;
    card[name] = value.trim();
    if (name === "city") {
      card.nickname = value;
    }
    if (name === "email" || name === "website") {
      // VALIDATIONG EMAIL AND WEBSITE FIELDS
      handleCardValidation(name, value.trim(), card);
    }
    setValues({ ...values });
    removeActiveField();
  };

  const handleCountryFlagChange = (card, name, value) => () => {
    card[name] = value;
    card.phoneNumber = "";
    setValues({ ...values });
  };

  const handleInputChange = (e) => {
    const checkboxes = [
      "isHeadOffice",
      "isCurrentEmployee",
      "isOfficialRepresentative",
      "isTeamMember",
    ];

    if (checkboxes.includes(e.target.name)) {
      if (e.target.name === "isCurrentEmployee") {
        setValues({ ...values, isCurrentEmployeeError: false });
      }
      // SINCE THESE ALL ARE CONDITIONAL RENDERED CHECKBOXES, HANDLING ALL PROBABILITES
      if (e.target.name === "isHeadOffice") {
        /** HEADQUARTER MUST BE ONE CARD, CHECKING HEADQUARTER OF SELECTED CARD TO TRUE, REST ALL TO FALSE IF ANY */
        const { id } = e.target;
        const updatedBusinessCard = businessCard.map((each) => {
          if (each.id === id) {
            return { ...each, isHeadOffice: e.target.checked };
          }
          return { ...each, isHeadOffice: false };
        });
        setValues({ ...values, businessCard: [...updatedBusinessCard] });
        return;
      }
      const { checked, name } = e.target;
      setValues({ ...values, [name]: checked });
      return;
    }
    if (e.target.name === "sameAsCompany") {
      const { checked, name } = e.target;
      setValues({ ...values, [name]: checked });
      return;
    }
    // IF NOT CHECKBOX VALUE
    setValues({ ...values, [e.target.name]: e.target.value });
  };

  const handleAboutMeChange = (event) => {
    /**
     * ABOUT ME --> BUSINESS CARD STEP 2 (PERSONAL DETAILS)
     */
    const { name, value } = event.target;
    if (name === "phone") {
      if (isNaN(value)) {
        return;
      }
      setValues({
        ...values,
        aboutMe: { ...values.aboutMe, [name]: value },
      });
      return;
    }
    if (name === "email") {
      setValues({
        ...values,
        aboutMe: { ...values.aboutMe, [name]: value.trim() },
      });
      if (value.trim() !== "") {
        setValues({
          ...values,
          aboutMeEmailError: true,
          aboutMe: { ...values.aboutMe, [name]: value.trim() },
        });
        return;
      } else {
        if (!REGEX_EMAIL.test(value)) {
          setValues({
            ...values,
            aboutMeEmailError: true,
            aboutMe: { ...values.aboutMe, [name]: value.trim() },
          });
          return;
        }
        setValues({
          ...values,
          aboutMeEmailError: false,
          aboutMe: { ...values.aboutMe, [name]: value.trim() },
        });
      }
      return;
    }
    setValues({ ...values, aboutMe: { ...values.aboutMe, [name]: value } });
  };

  const aboutMePhoneNumBlur = (e) => {
    const { value, name } = e.target;
    setValues({
      ...values,
      aboutMe: {
        ...values.aboutMe,
        [name]:
          value.length === 10
            ? formatPhoneNumber(value, { flag: { code: "US" } })
            : value,
      },
    });
    return;
  };

  const checkErrors = (id, key) => {
    const { newErrors } = values;
    const foundRecord = find(newErrors, { index: id, key });
    if (foundRecord) {
      return foundRecord.error;
    }
    return false;
  };

  const finalValidation = () => {
    const { businessCard, aboutMe } = values;
    let validationErrors = [];
    businessCard.forEach((tile, idx) => {
      if (!tile.email) {
        validationErrors.push({
          error: true,
          key: "email",
          index: idx,
        });
      }
      if (!tile.city) {
        validationErrors.push({
          error: true,
          key: "city",
          index: idx,
        });
      }
      if (!tile.phoneNumber) {
        validationErrors.push({
          error: true,
          key: "phoneNumber",
          index: idx,
        });
      }
      if (!get(tile, "companyId.name")) {
        validationErrors.push({
          error: true,
          key: "companyName",
          index: idx,
        });
      }
      if (!tile.nickname) {
        validationErrors.push({
          error: true,
          key: "nickName",
          index: idx,
        });
      }
    });
    if (!aboutMe.email) {
      validationErrors.push({
        error: true,
        key: "aboutEmail",
        index: 0,
      });
    }
    setValues({
      ...values,
      newErrors: [...validationErrors],
      businessCard: [...businessCard],
    });
  };

  const clearPhoneNumber = () => {
    setValues({
      ...values,
      aboutMe: {
        ...values.aboutMe,
        phone: "",
      },
    });
    return;
  };

  const clearEmail = () => {
    setValues({
      ...values,
      aboutMe: { ...values.aboutMe, email: "" },
    });
    return;
  };

  const handleSubmit = () => {
    // if (checkForErrors()) {
    //   finalValidation();
    //   return;
    // }
    if (userType === "admin" && !values.isCurrentEmployee) {
      values.isCurrentEmployeeError = true;
      setValues({ ...values });
      return;
    } else {
      values.isCurrentEmployeeError = false;
      setValues({ ...values });
    }
    if (!aboutMe.email) {
      values.aboutMeEmailError = true;
      setValues({ ...values });
      return;
    } else {
      if (!REGEX_EMAIL.test(aboutMe.email)) {
        values.aboutMeEmailError = true;
        setValues({ ...values });
        return;
      }
      values.aboutMeEmailError = false;
      setValues({ ...values });
    }
    aboutMe.phone = deformatPhoneNumber(aboutMe.phone);
    const dataToSubmit = {
      aboutMe: {
        ...aboutMe,
        myOffice: get(aboutMe, "myOffice.city", ""),
        email: get(aboutMe, "email", ""),
        phone: get(aboutMe, "phone", ""),
        profileUrl: get(aboutMe, "profileUrl.image")
          ? get(aboutMe, "profileUrl")
          : get(aboutMe, "profileUrl.image") ||
            get(userData, "profileImg.square") ||
            get(userData, "profileImg.circle") ||
            get(userData, "profileUrl") ||
            get(userInfo, "profileUrl"),
      },
      isTeamMember: true,
      isOfficialRepresentative: false,
      isCurrentEmployee: true,
      companyId: localStorage.getItem("companyId"),
    };
    dispatch(
      ProfileActions.addMeToCompany(dataToSubmit, (res) => {
        if (!res.error.error) {
          getPublicProfileLink(res.data.tradeWorkUrl);
          history.push(`/company/public/${res.data.tradeWorkUrl}`);
          localStorage.setItem("isNewAdmin", true);
        }
      })
    );
  };

  const getPublicProfileLink = (tradeWorkUrl) => {
    if (!tradeWorkUrl) {
      return;
    }
    let host = get(window.location, "host", "");
    if (host) {
      let tradeworksUrlData = "";
      if (host.includes("localhost")) {
        host = "localhost";
        tradeworksUrlData = `${PUBLIC_PROFILE.local}${tradeWorkUrl}`;
      } else {
        if (host.includes("-dev-")) {
          tradeworksUrlData = `${PUBLIC_PROFILE.dev}${tradeWorkUrl}`;
        }
        if (host.includes("-qa-")) {
          tradeworksUrlData = `${PUBLIC_PROFILE.qa}${tradeWorkUrl}`;
        }
        if (host.includes("-stage-")) {
          tradeworksUrlData = `${PUBLIC_PROFILE.stage}${tradeWorkUrl}`;
        }
        if (host.includes("twwstage")) {
          tradeworksUrlData = `${BASEURL.URL}${tradeWorkUrl}`;
        } else {
          tradeworksUrlData = `${BASEURL.URL}${tradeWorkUrl}`;
        }
      }
      return tradeworksUrlData;
    }
    return null;
  };

  const redirectToSpack = () => {
    // SINCE WE DO NOT HAVE PROFILE FOR TEAM MEMBER REGISTRATION, REDIRECTING TO S PACK
    dispatch(LoginActions.logOut());
    let host = get(window, "location.host", "");
    if (host) {
      if (host.includes("localhost")) {
        host = "localhost:3000";
        window.location.href = `http://${host}/logout`;
      } else {
        if (host.includes("-dev-")) {
          host = "tradeworks-spack-dev-fe.azurewebsites.net";
        }
        if (host.includes("-qa-")) {
          host = "tradeworks-spack-qa-fe.azurewebsites.net";
        }
        window.location.href = `https://${host}/logout`;
      }
    }
  };

  const redirectToLpack = () => {
    let host = get(window, "location.host", "");
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    let token = localStorage.getItem("tradeworks_user_token");
    if (host.includes("localhost")) {
      host = "localhost:3001";
      window.location.href = `http://${host}/lpack/profile/${token}`;
    } else {
      host = BASEURL.URL;
      window.location.href = `${host}lpack/lpack/profile/${token}`;
    }
  };

  const redirectToAnotherCompany = () => {
    localStorage.removeItem("companyId");
    localStorage.removeItem("companyName");
    history.push("/wizard");
  };

  const triggerCountryDropdown = (card) => () => {
    const { showCountryDropdown } = values;
    card.showCountryDropdown = !card.showCountryDropdown;
    setValues({ ...values });
  };

  const returnAggrementCheckbox = (userType) => {
    /**
     *
     * EACH ENTITES HAVE RESPECTIVE OPTIONS TO CHECK
     * VALIDATING AND RETURNING OPTIONS CONDITIONALLY
     *
     */

    switch (userType) {
      case "admin":
        return (
          <>
            <Row className={classes.checkbox_section}>
              <Element_Required_Icon
                className={`${classes.checkBox_required} ${
                  values.isCurrentEmployeeError ? classes.redIcon : ""
                }`}
              />
              <CustomCheckbox
                label="I confirm that I am currently employed by this company, and will be added as a staff member right now."
                className={`${classes.checkbox_aggrement} ${
                  values.isCurrentEmployeeError ? classes.errorCheck : ""
                }`}
                name="isCurrentEmployee"
                checked={isCurrentEmployee}
                onChange={handleInputChange}
              />
            </Row>
            <Row className={classes.checkbox_section}>
              {/* <CustomCheckbox
                label={strings.company_wizard.step2.checkbox.admin_a_2}
                className={classes.checkbox_aggrement_2}
                name="isOfficialRepresentative"
                checked={isOfficialRepresentative}
                onChange={handleInputChange}
              /> */}
              <div>
                <Text
                  size={16}
                  color={color.primary_palette.black}
                  family="gillsans_r"
                  // className={classes.step1_subheading}
                >
                  The current Admin for this company is{" "}
                  {get(adminInfo, "companyAdmins[0].firstName", "")}{" "}
                  {get(adminInfo, "companyAdmins[0].lastName", "")}, who
                  automatically will be notified that you are joining.
                </Text>
                <Text
                  size={16}
                  color={color.primary_palette.black}
                  family="gillsans_r"
                >
                  To change your TW status to Admin, please contact{" "}
                  {get(adminInfo, "companyAdmins[0].firstName", "")}{" "}
                  {get(adminInfo, "companyAdmins[0].lastName", "")}, directly,
                  or contact Alex <br />
                  at <EMAIL>, 866.960.9100
                </Text>
              </div>
            </Row>
          </>
        );
      case "teamMember":
        return (
          <Row className={classes.checkbox_section}>
            <CustomCheckbox
              label={strings.company_wizard.step2.checkbox.team_mem}
              className={classes.checkbox_aggrement}
              name="isTeamMember"
              onChange={handleInputChange}
              checked={isTeamMember}
            />
            <Element_Required_Icon className={classes.checkBox_required} />
          </Row>
        );

      default:
        return null;
    }
  };

  const handleCountryChange = (value, card) => {
    card.phonenumber = "";
    card.countryCode = value;
    card.showCountryDropdown = !card.showCountryDropdown;
    setValues({
      ...values,
    });
  };

  const returnUserType = () => {
    const { businessCard } = values;
    if (!businessCard) {
      return;
    }
    if (businessCard.length > 1) {
      // ADMIN CASE(multiple business cards)
      return "admin";
    }
    const card = businessCard[0];
    const adminId = get(card, "cPackUserId", null);
    if (adminId === get(userInfo, "id", "")) {
      return "admin";
    }
    const hasAdmin = get(card, "companyId.isCompanyHavingAdmin", false);
    const newCompany = get(card, "isNewCompany", false);
    if (!newCompany) {
      if (hasAdmin) {
        // HAS ADMIN, NOT NEW COMPANY(REGISTERED)
        return "teamMember";
      }
      return "admin";
    }
    return "admin";
  };

  const handleAlertModalClose = (name) => (event) => {
    setValues({ ...values, [name]: false });
  };

  const setCompany = () => {
    /**
     * WHEN USER TRIES TO REGISTER EXISTING COMPANY, CLICKS YES IN ALERT MODAL
     * SETTING THE "FOUND COMPANY" TO THE STATE
     */
    const { foundCompany } = values;
    const { address } = foundCompany;
    const flag = find(countries_flags, { code: "US" });
    const headquarter = find(address, { type: "headquarters" });
    const { city, state, zip } = headquarter;
    setValues({
      ...values,
      businessCard: [
        {
          companyId: foundCompany,
          flag: { ...flag },
          city,
          state,
          zip,
          id: uuidv4(),
        },
      ],
      aboutMe: {
        profileUrl: {},
        email: get(userInfo, "email", ""),
        phone: get(userInfo, "phoneNumber", ""),
      },
      showAlertModal: false,
      isTeamMember: false,
      isCurrentEmployee: false,
      isOfficialRepresentative: false,
    });
  };

  const setActiveField = (name, id) => () => {
    /**
     * WHEN USER IS IN THE INPUT, SHOW LABEL OF THAT PARTICULAR INPUT
     */
    setValues({ ...values, activeField: { name, id } });
  };

  const removeActiveField = () => {
    setValues({ ...values, activeField: {} });
  };

  const checkForErrors = () => {
    const {
      businessCard,
      errors,
      isTeamMember,
      isCurrentEmployee,
      isOfficialRepresentative,
    } = values;
    const userType = returnUserType();

    const errValues = Object.values(errors).map((each) => {
      if (each.email || each.website) {
        return true;
      }
      return false;
    });
    const hqCard = find(businessCard, { isHeadOffice: true });
    if (!hqCard) {
      return true;
    }
    const isCompanySelected = get(hqCard, "companyId.name", false);
    const isCityAvailable = get(hqCard, "city", "");
    const isEmailAvailable = get(hqCard, "email", "");
    const isPhoneAvailable = get(hqCard, "phoneNumber", "");
    const agreements =
      userType === "teamMember"
        ? isTeamMember
        : isCurrentEmployee && isOfficialRepresentative;
    if (
      errValues.includes(true) ||
      !isCompanySelected ||
      !isCityAvailable ||
      !isEmailAvailable ||
      !isPhoneAvailable ||
      !agreements
    ) {
      return true;
    }
    return false;
  };

  const handlePingAdmin = () => {
    setValues({ ...values, showPingAdmin: !values.showPingAdmin });
  };

  const handleToggleModal = (closeModal, openModal) => {
    if (!openModal) {
      setValues({ ...values, [closeModal]: false });
      return;
    }
    setValues({ ...values, [openModal]: true, [closeModal]: false });
  };

  /**this functions returns placeholder as the previous card value */
  const returnPlaceholder = (cardIndex) => {
    const { businessCard } = values;
    const foundCard = businessCard[cardIndex - 1];
    if (!foundCard || cardIndex === 0) {
      return "Company Name";
    }
    return get(foundCard, "companyId.name", "Company Name");
  };

  const {
    isCurrentEmployee,
    isTeamMember,
    isOfficialRepresentative,
    businessCard,
    aboutMe,
    errors,
    showAlertModal,
    activeField,
    showPingAdmin,
    showPingAdminSuccess,
    showTmSuccessModal,
    sameAsCompany,
    showCountryDropdown,
    countryCode,
    hasExistingAdmin,
    isCurrentEmployeeError,
    aboutMeEmailError,
  } = values;
  const headQuarterCard = find(businessCard, { isHeadOffice: true });
  const addresses = headQuarterCard
    ? get(headQuarterCard, "companyId.address", [])
    : [];
  const userType = returnUserType();
  const hasFormErrors = checkForErrors();
  return (
    <>
      {!hasExistingAdmin && (
        <Row
          className={classes.block}
          style={{ width: "800px", margin: "0 auto" }}
        >
          <Text
            size={35}
            color={color.primary_palette.black}
            family="gillsans_light"
            className={`${classes.text_center} ${classes.upper_case}`}
            style={{ marginTop: "34px" }}
          >
            • ADD ME TO THIS COMPANY •
          </Text>
          <Text
            size={26}
            color={color.primary_palette.black}
            family="gillsans_light"
            className={`${classes.text_center} ${classes.upper_case}`}
            // style={{ marginTop: "34px" }}
          >
            {localStorage.getItem("companyName") ||
              get(CompanyListByUser, "[0].name", "")}
          </Text>
          <div className={classes.checkbox_section_container_2}>
            {returnAggrementCheckbox("admin")}
            {userType === "teamMember" && (
              <Text
                size={16}
                family="avenir_light"
                className={classes.ping_admin_txt}
              >
                {strings.company_wizard.step1.labels.ping_admin}
              </Text>
            )}
          </div>
          {/* </>
        )} */}
          <Row
            className={`${classes.block} ${classes.block_space}`}
            style={{ paddingTop: pxToRem(46) }}
          >
            <Text
              size={20}
              color={color.primary_palette.franklin_purple}
              family="gillsans_sb"
              className={classes.step1_heading}
            >
              {userType === "teamMember"
                ? strings.company_wizard.step1.titles.step2_2
                : strings.company_wizard.step1.titles.step2}
            </Text>
            <Text
              size={16}
              color={color.secondary_palette.grays.up_in_smoke}
              family="avenir_roman"
              className={classes.step1_subheading}
            >
              {userType === "teamMember"
                ? strings.company_wizard.step1.titles.step2_des_1
                : strings.company_wizard.step1.titles.step2_des}
            </Text>
            <BusinessCardTwo
              key={userType}
              aboutMe={aboutMe}
              newCompany={get(headQuarterCard, "isNewCompany", false)}
              offices={addresses}
              userData={userData}
              userType={userType}
              imageUpload={imageUpload}
              handleOfficeSelect={handleOfficeSelect}
              checkErrors={checkErrors}
              emailError={aboutMeEmailError}
              onPhoneNumberFocus={onPhoneNumberFocus}
              handleChange={handleAboutMeChange}
              aboutMePhoneNumBlur={aboutMePhoneNumBlur}
              phoneNumberLength={
                get(headQuarterCard, "flag.code", "") === "US" ? 10 : 20
              }
              businessCardData={businessCard}
              sameAsCompany={sameAsCompany}
              hideSameCompany={true}
              clearPhoneNumber={clearPhoneNumber}
              clearEmail={clearEmail}
            />
          </Row>
          {values.newErrors && values.newErrors.length !== 0 && (
            <Row
              style={{
                justifyContent: "end",
                marginRight: "100px",
              }}
            >
              <img
                src="assets/images/warning.svg"
                className={classes.warn_icon}
              />
              <Text
                size={15}
                color={color.primary_palette.christmas_red}
                family="gillsans_r"
                className={`${classes.errorMessage} `}
              >
                Please review required fields above
              </Text>
            </Row>
          )}
          {userType === "admin" ? (
            // <Row className={classes.btn_blk2}>
            //   <Row className={classes.btn_alignment}>
            <CustomButton
              variant="outlined"
              color="primary"
              // disabled={hasFormErrors}
              onClick={handleSubmit}
              className={`${classes.actionButtons} ${classes.second_btn_width}`}
              style={{ float: "right", bottom: "140px", right: "60px" }}
            >
              CONTINUE
            </CustomButton>
          ) : (
            //   </Row>
            // </Row>
            <Row className={classes.btn_blk}>
              <Row className={classes.btn_alignment_2}>
                <CustomButton
                  variant="outlined"
                  color="primary"
                  onClick={handleSubmit}
                  className={`${classes.actionButtons} ${classes.join_us_btn}`}
                >
                  {strings.general.titles.join}
                </CustomButton>
              </Row>
            </Row>
          )}
          {userType === "admin" && values.isCurrentEmployeeError ? (
            <Row
              style={{
                justifyContent: "end",
                marginRight: "100px",
                paddingBottom: "30px",
              }}
            >
              <img
                src="assets/images/warning.svg"
                className={classes.warn_icon}
                style={{
                  marginTop: pxToRem(-10),
                  marginRight: pxToRem(5),
                }}
              />
              <Text
                size={12}
                color={color.primary_palette.tricks_red}
                family="gillsans_sb"
                className={`${classes.errorMessage} `}
              >
                If you are not currently employed by this Company,
                <br />
                please sign into{" "}
                <span
                  className={classes.linkStyle}
                  onClick={redirectToAnotherCompany}
                >
                  Another Company
                </span>{" "}
                or join TradeWorks as an{" "}
                <span className={classes.linkStyle} onClick={redirectToLpack}>
                  Individual
                </span>
              </Text>
            </Row>
          ) : (
            ""
          )}
          {userType === "admin" && aboutMeEmailError ? (
            <Row
              style={{
                justifyContent: "end",
                marginRight: "100px",
                marginBottom: "20px",
              }}
            >
              <img
                src="assets/images/warning.svg"
                className={classes.warn_icon}
                style={{
                  marginTop: pxToRem(-1),
                  marginRight: pxToRem(5),
                }}
              />
              <Text
                size={12}
                color={color.primary_palette.tricks_red}
                family="gillsans_sb"
                className={`${classes.errorMessage} `}
              >
                Don’t forget to add your email
              </Text>
            </Row>
          ) : (
            ""
          )}
          {userType === "teamMember" && (
            <div className={classes.ping_admin_wrap}>
              {" "}
              <Text
                size={16}
                color={color.secondary_palette.grays.up_in_smoke}
                family="avenir_roman"
                className={classes.tm_ping_admin_txt2}
                onClick={handlePingAdmin}
                style={{ cursor: "pointer" }}
              >
                <span className={classes.ping_admin}>
                  {strings.company_wizard.step1.labels.tm_ping_admin}
                </span>
                {strings.company_wizard.step1.labels.tm_ping_admin2}
                <img
                  src="assets/images/ping_flag.png"
                  className={classes.ping_flag}
                />
              </Text>
            </div>
          )}
          {showAlertModal && (
            <CompanyExistsWarningModal
              setCompany={setCompany}
              handleAlertModalClose={handleAlertModalClose("showAlertModal")}
            />
          )}
        </Row>
      )}
      {hasExistingAdmin && (
        <BusinessCardTwo
          key={userType}
          aboutMe={aboutMe}
          newCompany={get(headQuarterCard, "isNewCompany", false)}
          offices={addresses}
          userData={userData}
          userType={userType}
          imageUpload={imageUpload}
          handleOfficeSelect={handleOfficeSelect}
          checkErrors={checkErrors}
          onPhoneNumberFocus={onPhoneNumberFocus}
          handleChange={handleAboutMeChange}
          aboutMePhoneNumBlur={aboutMePhoneNumBlur}
          phoneNumberLength={
            get(headQuarterCard, "flag.code", "") === "US" ? 10 : 20
          }
          businessCardData={businessCard}
          sameAsCompany={sameAsCompany}
          clearPhoneNumber={clearPhoneNumber}
          clearEmail={clearEmail}
        />
      )}
      {showPingAdmin && (
        <PingAdmin
          handlePingAdmin={handlePingAdmin}
          handleToggleModal={handleToggleModal}
          businessCard={get(values, "businessCard[0]", {})}
        />
      )}
      {showPingAdminSuccess && (
        <PingAdminSuccess handleToggleModal={handleToggleModal} />
      )}
      {showTmSuccessModal && (
        <JoinUsSuccessModal
          businessCard={values.businessCard}
          redirectApp={redirectToSpack}
        />
      )}
    </>
  );
}

export default businessCardStyles(NewMember);
