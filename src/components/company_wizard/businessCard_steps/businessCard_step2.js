import React, { useEffect } from "react";
import { get, map } from "lodash";
import { MenuItem } from "@material-ui/core";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import strings from "../../../utilities/strings";
import Chevron_Down_Icon from "../../data_display/icons/Arrow_Chevron_Down";
import { color } from "../../../utilities/themes";
import businessCardStyles from "../styles/businessCardStyles";
import CustomSelect from "../../inputs/custom_select";
import CustomPlainInputField from "../../inputs/custom_plain_input_field";
import AddImageLogo from "../add_image_logo";
import CustomTextField from "../../inputs/custom_textfields";
import Element_Required_Icon from "../../../components/data_display/icons/ElementRequiered";
import Close from "../../../components/data_display/icons/Close";

function BusinessCardTwo(props) {
  const {
    classes,
    handleChange,
    handleOfficeSelect,
    imageUpload,
    aboutMe,
    userType,
    newCompany,
    onPhoneNumberFocus,
    phoneNumberLength,
    aboutMePhoneNumBlur,
    clearPhoneNumber,
    clearEmail,
    businessCardData,
    sameAsCompany,
    checkErrors,
    hideSameCompany,
    emailError,
    userData,
    userInfo,
  } = props;

  const { profileUrl, email } = aboutMe;
  const myOffices = map(businessCardData, (card) => {
    return card.nickname;
  });
  return (
    <>
      {userType === "admin" && (
        <Row className={classes.block_input}>
          <Row className={classes.input_block}>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_black_r"
              className={classes.text_width_email}
            >
              <Element_Required_Icon
                className={`${
                  checkErrors(0, "aboutEmail")
                    ? classes.select_field_required_email_red
                    : classes.select_field_required_email
                } ${emailError ? classes.emailError : ""}`}
              />
              {strings.company_wizard.step1.titles.my_email}
            </Text>
            <div>
              <CustomPlainInputField
                name="email"
                defaultValue={
                  sameAsCompany
                    ? get(businessCardData, "[0].email")
                    : get(aboutMe, "email", "")
                }
                onBlur={handleChange}
                className={`${classes.textfield} ${
                  emailError ? classes.emailInputError : ""
                }`}
              />
              <Close onClick={clearEmail} className={classes.clear_icon2} />
              <Text
                size={12}
                color={color.primary_palette.black}
                family="avenir_roman"
              >
                {newCompany
                  ? strings.company_wizard.step1.titles.additional_info_2
                  : strings.company_wizard.step1.titles.additional_info}
              </Text>
            </div>
          </Row>
          <Row
            className={`${classes.input_block} ${classes.marginLeft17}`}
            style={{ position: "relative" }}
          >
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_black_r"
              className={classes.text_width}
            >
              {strings.company_wizard.step1.titles.my_phone}
            </Text>
            <CustomTextField
              name="phone"
              value={
                sameAsCompany
                  ? get(businessCardData, "[0].phoneNumber")
                  : get(aboutMe, "phone", "")
              }
              onChange={handleChange}
              className={classes.textfield1}
              onBlur={aboutMePhoneNumBlur}
              inputProps={{
                maxLength: phoneNumberLength,
                onFocus: onPhoneNumberFocus(),
              }}
            />
            <Close onClick={clearPhoneNumber} className={classes.clear_icon} />
          </Row>
          {!hideSameCompany && (
            <Row className={`${classes.input_block} ${classes.marginLeft17}`}>
              <Text
                size={16}
                color={color.primary_palette.black}
                family="avenir_black_r"
                className={classes.text_width}
              >
                {strings.company_wizard.step1.titles.my_office}
              </Text>
              <CustomSelect
                className={classes.choose_office}
                IconComponent={Chevron_Down_Icon}
                renderValue={(value) => value || "CHOOSE YOUR OFFICE"}
                value={
                  (myOffices && myOffices[0]) || get(aboutMe, "myOffice", "")
                }
                MenuProps={{
                  getContentAnchorEl: null,
                  disableScrollLock: true,
                  anchorOrigin: {
                    vertical: "bottom",
                    horizontal: "left",
                  },
                }}
              >
                {map(myOffices, (address, index) => {
                  return (
                    <MenuItem
                      value={address}
                      onClick={handleOfficeSelect(address, "myOffice")}
                    >
                      {address}
                    </MenuItem>
                  );
                })}
              </CustomSelect>
              <Text
                size={12}
                color={color.primary_palette.black}
                family="avenir_roman"
                className={classes.nickNameTxtAlign}
              >
                Office Nickname where I am located
              </Text>
            </Row>
          )}
          <Row className={`${classes.input_block} ${classes.marginLeft17}`}>
            <Text
              size={16}
              color={color.primary_palette.black}
              family="avenir_black_r"
              className={`${classes.text_width} ${classes.positionRelative}`}
            >
              {strings.company_wizard.step1.titles.my_profile}
            </Text>
            <Text
              size={12}
              color={color.primary_palette.black}
              family="avenir_roman"
              className={classes.formatAlign}
            >
              JPG or PNG | 200KB to 2MB
            </Text>
            <AddImageLogo
              defaultImage={
                get(userData, "profileImg.square") ||
                get(userData, "profileImg.circle") ||
                get(userData, "profileUrl") ||
                get(userInfo, "profileUrl") ||
                "assets/images/water_cooler.png"
              }
              className={classes.circle_img}
              imageUpload={imageUpload}
              name="profile"
              cropShape="round"
              cropSize="1"
              multipleVal={true}
              img={{ profile: { ...profileUrl } }}
              pencilEdit
            />
          </Row>
        </Row>
      )}
      <div className={classes.circle_img_tm}>
        {userType === "teamMember" && (
          <AddImageLogo
            defaultImage={
              get(userData, "profileImg.square") ||
              get(userData, "profileImg.circle") ||
              get(userData, "profileUrl") ||
              get(userInfo, "profileUrl") ||
              "assets/images/water_cooler.png"
            }
            className={`${classes.circle_img}`}
            imageUpload={imageUpload}
            name="profile"
            img={{ profile: { ...profileUrl } }}
            pencilEdit
          />
        )}
      </div>
    </>
  );
}

export default businessCardStyles(BusinessCardTwo);
