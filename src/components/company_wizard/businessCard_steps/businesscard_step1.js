import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { MenuItem, InputAdornment } from "@material-ui/core";
import { get, map, find, filter, omit } from "lodash";
import uuidv4 from "uuid/v4";

import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import strings from "../../../utilities/strings";
import Chevron_Down_Icon from "../../data_display/icons/Arrow_Chevron_Down";
import CustomCheckbox from "../../inputs/custom_checkbox";
import businessCardStyles from "../styles/businessCardStyles";
import CustomTextField from "../../inputs/custom_textfields";
import CustomSelect from "../../inputs/custom_select";
import CustomButton from "../../navigations/custom_buttons";
import Lil_Plus_filled from "../../data_display/icons/Lil_Plus_filled";
import BusinessCardTwo from "./businessCard_step2";
import CustomAutoComplete from "../../inputs/auto_complete_company";
import CustomScrollbars from "../../data_display/custom_scroll";
import PingAdmin from "../../modals/pingAdmin";
import PingAdminSuccess from "../../modals/messageSentModal";
import CompanyExistsWarningModal from "../../modals/companyAlready";
import JoinUsSuccessModal from "../../modals/new_joinModal";

import {
  PHONE_NUMBER_FORMAT,
  REGEX_EMAIL,
  WEBSITE_URL,
  NON_USA_REGEX,
} from "../../../validations/validations";
import { color } from "../../../utilities/themes";
import { ProfileActions, LoginActions } from "../../../redux/actions";
import { ProfileTypes } from "../../../redux/actionTypes";
import Element_Required_Icon from "../../../components/data_display/icons/ElementRequiered";
import { useHistory } from "react-router-dom";
import { BASEURL } from "../../../constants";

function BusinessCardOne(props) {
  const { classes, handleNext, setUserType } = props;
  const companies = useSelector((state) => state.Profile.companyBasicList);
  const userProfieInfo = useSelector((state) => state.Profile);
  const history = useHistory();
  const businesscardData = useSelector(
    (state) => state.Profile.refBusinessCard
  );
  const countries_flags = useSelector((state) => state.Configs.countries_flags);
  const userInfo = useSelector((state) => state.Profile.profileUserData);
  const userData = useSelector((state) => state.Profile.companyinfo);
  const dispatch = useDispatch();

  const [values, setValues] = useState({
    businessCard: [],
    aboutMe: {
      profileUrl: {},
    },
    isTeamMember: false,
    isOfficialRepresentative: false,
    isCurrentEmployee: false,
    errors: {},
    showAlertModal: false,
    activeField: "",
    showPingAdmin: false,
    showPingAdminSuccess: false,
    showTmSuccessModal: false,
    sameAsCompany: true,
    showCountryDropdown: false,
    hasExistingAdmin: false,
  });

  const fetchData = () => {
    dispatch(ProfileActions.getProfileDataOfInfo("COMPANYINFO"));
    // dispatch(ProfileActions.fetchAllCompanies());
    dispatch(ProfileActions.fetchAllCountries());
    dispatch(ProfileActions.fetchBasicCompaniesInfo());
    // dispatch(ProfileActions.companiesList());
  };

  useEffect(() => {
    !businesscardData && fetchData();
  }, []);

  useEffect(() => {
    if (userInfo && countries_flags) {
      if (!businesscardData) {
        if (values.businessCard.length === 0 && countries_flags) {
          handleAddBusinessCard(true);
        }
        return;
      } else {
        setValues({ ...values, ...businesscardData });
      }
    }
  }, [businesscardData, countries_flags, userInfo]);

  const handleAddBusinessCard = (isTrue) => {
    // ADDING NEW BUSINESS CARD
    const flag = find(countries_flags, { code: "US" });
    // SETTING US AS DEFAULT SELECTED FLAG
    values.businessCard = [
      ...values.businessCard,
      {
        id: uuidv4(),
        isHeadOffice: isTrue === true ? true : false,
        companyId: {},
        selectedOffice: {},
        flag: { ...flag },
        email: get(userInfo, "email", ""),
        phoneNumber: get(userInfo, "phoneNumber", "")
          ? formatPhoneNumber(get(userInfo, "phoneNumber", ""), {
              flag: { code: "US" },
            })
          : get(userInfo, "phoneNumber", ""),
      },
    ];
    setValues({ ...values });
  };

  const imageUpload = (imageData, name) => {
    // PROFILE IMAGE UPLOAD
    const { aboutMe } = values;
    aboutMe.profileUrl = imageData;
    setValues({ ...values });
  };

  const onPhoneNumberBlur = (card) => () => {
    // FORMATTING PHONE NUMBER(only us number)
    if (card.flag.code !== "US") {
      return;
    }
    if (card.phoneNumber) {
      card.phoneNumber = card.phoneNumber
        .toString()
        .replace(PHONE_NUMBER_FORMAT, "$1.$2.$3");
    }
    setValues({ ...values, activeField: {} });
  };

  const formatPhoneNumber = (value, card) => {
    const country = get(card, "flag.code", "");
    if (country !== "US") {
      return value;
    }
    return value.replace(PHONE_NUMBER_FORMAT, "$1.$2.$3");
  };

  const deformatPhoneNumber = (number) => {
    // DEFORMATTING
    if (!number) {
      return;
    }
    return number.replace(/[().\s/]/g, "");
  };
  const onPhoneNumberFocus = (card) => (event) => {
    // ON FOCUS REMOVING FORMAT
    const { name, value } = event.target;
    if (card && card.phoneNumber) {
      card.phoneNumber = card.phoneNumber.replace(/[().\s/]/g, "");
      setValues({ ...values, activeField: { name: "phone", id: card.id } });
      return;
    }
    if (!card && value) {
      setValues({
        ...values,
        aboutMe: { ...values.aboutMe, [name]: deformatPhoneNumber(value) },
      });
      return;
    }
  };

  const onCompanyBlur = (card, index) => (e) => {
    const { companyId } = card;
    const { value } = e.target;
    const flag = find(countries_flags, { code: "US" });
    if (companyId && companyId._id && companyId.name == value.trim()) {
      // SELECTED FORM DROPDOWN
      card.isNewCompany = false;
      setValues({
        ...values,
      });
      return;
    } else {
      localStorage.setItem("companyName", e.target.value);
      if (get(card, "companyId._id", false)) {
        // IF ENTITY IS CHANGED
        setValues({
          ...values,
          businessCard: [
            {
              companyId: { name: e.target.value.trim() },
              isNewCompany: true,
              flag: { ...flag },
              id: uuidv4(),
            },
          ],
          aboutMe: {
            profileUrl: {},
            email: get(userInfo, "email", ""),
            phone: get(userInfo, "phoneNumber", "")
              ? formatPhoneNumber(get(userInfo, "phoneNumber", ""), {
                  flag: { code: "US" },
                })
              : get(userInfo, "phoneNumber", ""),
          },
          isTeamMember: false,
          isCurrentEmployee: false,
          isOfficialRepresentative: false,
          hasExistingAdmin: false,
        });
        return;
      }
      // IF NOTHING IS SELECTED AND IT IS A NEW ENTERED VALUE
      if (value || index > 0) {
        card.companyId = { name: value.trim() };
        card.isNewCompany = value.length > 0 ? true : false;
        card.flag = { ...flag };
        setValues({
          ...values,
          aboutMe: {
            profileUrl: {},
            email: get(userInfo, "email", ""),
            phone: get(userInfo, "phoneNumber", "")
              ? formatPhoneNumber(get(userInfo, "phoneNumber", ""), {
                  flag: { code: "US" },
                })
              : get(userInfo, "phoneNumber", ""),
          },
          isTeamMember: false,
          isCurrentEmployee: false,
          isOfficialRepresentative: false,
          hasExistingAdmin: false,
        });
      } else {
        // RESET IF THERE IS NOT VALUE IN MASTER BUSINESS CARD
        setValues({
          businessCard: [
            {
              id: uuidv4(),
              isHeadOffice: true,
              companyId: {},
              selectedOffice: {},
              flag: { ...flag },
            },
          ],
          aboutMe: {
            profileUrl: {},
            email: get(userInfo, "email", ""),
            phone: get(userInfo, "phoneNumber", ""),
          },
          isTeamMember: false,
          isOfficialRepresentative: false,
          isCurrentEmployee: false,
          errors: {},
          showAlertModal: false,
          activeField: "",
          hasExistingAdmin,
        });
      }
    }
  };

  const handleBranchBlur = (card) => (event) => {
    const { value } = event.target;
    if (card.companyName !== value.trim()) {
      card.companyId = { name: value.trim() };
      card.companyName = value.trim();
      setValues({ ...values });
    }
    return;
  };

  const handleCompanySelect = (event, value, card) => {
    if (!value) {
      return;
    }
    const flag = find(countries_flags, { code: "US" });
    const { address } = value;
    // const headquarter = find(address, { type: "headquarters" });
    const { city, state, zip } = address;
    const phoneNumber = value.phoneNumber;
    const email = value.email;
    const website = value.website;
    const filteredAdmin = get(value, "companyAdmins").filter(
      (each) => each.email == get(userInfo, "email", "")
    );
    const hasExistingAdmin =
      get(value, "companyAdmins.length") !== 0 &&
      get(filteredAdmin, "length") === 0
        ? true
        : false;
    localStorage.setItem("companyId", value._id);
    localStorage.setItem("newCompanyId", value._id);
    localStorage.setItem("companyName", value.name);
    /**
     * STATE RESET ON COMPANY CHANGE WITH (companyId = selected value)
     *
     **/
    if (hasExistingAdmin) {
      history.push("/newMember");
    }

    setValues({
      ...values,
      businessCard: [
        {
          companyId: value,
          flag: { ...flag },
          city,
          state,
          zip,
          address: get(address, "address", ""),
          address1: get(address, "address1", ""),
          website,
          phoneNumber,
          email,
          id: uuidv4(),
        },
      ],
      aboutMe: {
        profileUrl: {},
        email: get(userInfo, "email", ""),
        phone: get(userInfo, "phoneNumber", ""),
      },
      isTeamMember: false,
      isCurrentEmployee: false,
      isOfficialRepresentative: false,
      newErrors: [],
      hasExistingAdmin,
    });
  };

  const handleOfficeSelect = (address, name, card) => (e) => {
    if (card) {
      // BUSINESS CARD OFFICE SELECT (ARRAY)
      card[name] = address;
      setValues({ ...values });
    } else {
      // STEP2 OFFICE SELECT
      const { aboutMe } = values;
      aboutMe[name] = address;
      setValues({ ...values });
    }
  };

  const handleCardValidation = (name, value, card) => {
    const cardId = get(card, "id", null)
      ? get(card, "id", null)
      : get(card, "_id", null);

    if (name === "website" && !value) {
      // NOT VALIDATING WEBSITE IF NO VALUE ENTERED
      const { errors } = values;
      errors[cardId] = errors[cardId] || {};
      errors[cardId] = { ...errors[cardId], [name]: false };
      setValues({ ...values });
      return;
    }

    const regexTest =
      name === "email" ? REGEX_EMAIL.test(value) : WEBSITE_URL.test(value);
    if (!regexTest) {
      const { errors } = values;
      errors[cardId] = errors[cardId] || {};
      errors[cardId] = { ...errors[cardId], [name]: true };
      setValues({ ...values });
      return;
    }
    errors[cardId] = errors[cardId] || {};
    errors[cardId][name] = false;

    setValues({ ...values, errors: {} });
  };

  const handleBusinessCardChange = (card) => (event) => {
    // BUSINESS CARD HANDLE CHANGE FUNTION
    const { name, value } = event.target;
    if (name === "email" || name === "website") {
      // VALIDATIONG EMAIL AND WEBSITE FIELDS
      handleCardValidation(name, value, card);
    }
    if (name === "zip") {
      const { companyId } = card;
      const duplicateData = filter(companies, (each) => {
        const addressData = find(each.address, { zip: value });
        return companyId.name === each.name && addressData;
      });
      if (duplicateData.length > 0) {
        setValues({
          ...values,
          showAlertModal: true,
          foundCompany: duplicateData[0],
          newErrors: [],
        });
        return;
      }
    }
    if (name === "phoneNumber") {
      const country = get(card, "flag.code", "");
      const val = deformatPhoneNumber(value);
      if (country === "US") {
        // DEFORMATTING PHONE NUMBER AND SENDING TO VALIDATION
        if (isNaN(val)) {
          /**returning if input is NaN */
          if (!val) {
            card[name] = "";
            setValues({ ...values, newErrors: [] });
          }
          return;
        }
        /**saving deformatted value to phone number in businesscard (formatted) */
        card[name] = val;
      } else {
        const validationTest = NON_USA_REGEX.test(value);
        if (!validationTest) {
          card[name] = value;
          setValues({ ...values, newErrors: [] });
          return;
        }
        return;
      }
    } else {
      card[name] = value;
    }
    setValues({ ...values, newErrors: [] });
  };

  const handleBusinessCardBlur = (card) => (event) => {
    const { name, value } = event.target;
    card[name] = value.trim();
    if (name === "city") {
      card.nickname = value;
    }
    if (name === "email" || name === "website") {
      // VALIDATIONG EMAIL AND WEBSITE FIELDS
      handleCardValidation(name, value.trim(), card);
    }
    setValues({ ...values });
    removeActiveField();
  };

  const handleCountryFlagChange = (card, name, value) => () => {
    card[name] = value;
    card.phoneNumber = "";
    setValues({ ...values });
  };

  const handleInputChange = (e) => {
    const checkboxes = [
      "isHeadOffice",
      "isCurrentEmployee",
      "isOfficialRepresentative",
      "isTeamMember",
    ];

    if (checkboxes.includes(e.target.name)) {
      // SINCE THESE ALL ARE CONDITIONAL RENDERED CHECKBOXES, HANDLING ALL PROBABILITES
      if (e.target.name === "isHeadOffice") {
        /** HEADQUARTER MUST BE ONE CARD, CHECKING HEADQUARTER OF SELECTED CARD TO TRUE, REST ALL TO FALSE IF ANY */
        const { id } = e.target;
        const updatedBusinessCard = businessCard.map((each) => {
          if (each.id === id) {
            return { ...each, isHeadOffice: e.target.checked };
          }
          return { ...each, isHeadOffice: false };
        });
        setValues({ ...values, businessCard: [...updatedBusinessCard] });
        return;
      }
      const { checked, name } = e.target;
      setValues({ ...values, [name]: checked });
      return;
    }
    if (e.target.name === "sameAsCompany") {
      const { checked, name } = e.target;
      if (checked) {
        setValues({ ...values, [name]: checked });
      } else {
        setValues({
          ...values,
          [name]: checked,
          aboutMe: { ...values.aboutMe, email: "", phone: "" },
        });
      }
      return;
    }
    // IF NOT CHECKBOX VALUE
    setValues({ ...values, [e.target.name]: e.target.value });
  };

  const handleAboutMeChange = (event) => {
    /**
     * ABOUT ME --> BUSINESS CARD STEP 2 (PERSONAL DETAILS)
     */
    const { name, value } = event.target;
    if (name === "phone") {
      if (isNaN(value)) {
        return;
      }
      setValues({
        ...values,
        aboutMe: { ...values.aboutMe, [name]: value },
      });
      return;
    }
    if (name === "email") {
      setValues({
        ...values,
        aboutMe: { ...values.aboutMe, [name]: value.trim() },
      });
      return;
    }
    setValues({ ...values, aboutMe: { ...values.aboutMe, [name]: value } });
  };

  const aboutMePhoneNumBlur = (e) => {
    const { value, name } = e.target;
    setValues({
      ...values,
      aboutMe: {
        ...values.aboutMe,
        [name]:
          value.length === 10
            ? formatPhoneNumber(value, { flag: { code: "US" } })
            : value,
      },
    });
    return;
  };

  const clearPhoneNumber = () => {
    setValues({
      ...values,
      aboutMe: {
        ...values.aboutMe,
        phone: "",
      },
    });
    return;
  };

  const clearEmail = () => {
    setValues({
      ...values,
      aboutMe: { ...values.aboutMe, email: "" },
    });
    return;
  };

  const checkErrors = (id, key) => {
    const { newErrors } = values;
    const foundRecord = find(newErrors, { index: id, key });
    if (foundRecord) {
      return foundRecord.error;
    }
    return false;
  };

  const finalValidation = () => {
    const { businessCard, aboutMe } = values;
    let validationErrors = [];
    businessCard.forEach((tile, idx) => {
      if (!tile.email) {
        validationErrors.push({
          error: true,
          key: "email",
          index: idx,
        });
      }
      if (!tile.city) {
        validationErrors.push({
          error: true,
          key: "city",
          index: idx,
        });
      }
      if (!tile.state) {
        validationErrors.push({
          error: true,
          key: "state",
          index: idx,
        });
      }
      if (!tile.phoneNumber) {
        validationErrors.push({
          error: true,
          key: "phoneNumber",
          index: idx,
        });
      }
      if (!get(tile, "companyId.name")) {
        validationErrors.push({
          error: true,
          key: "companyName",
          index: idx,
        });
      }
      if (!tile.nickname) {
        validationErrors.push({
          error: true,
          key: "nickName",
          index: idx,
        });
      }
    });
    if (!aboutMe.email) {
      validationErrors.push({
        error: true,
        key: "aboutEmail",
        index: 0,
      });
    }
    if (!isCurrentEmployee) {
      validationErrors.push({
        error: true,
        key: "isCurrentEmployee",
        index: 0,
      });
    }
    if (!isOfficialRepresentative) {
      validationErrors.push({
        error: true,
        key: "isOfficialRepresentative",
        index: 0,
      });
    }
    setValues({
      ...values,
      newErrors: [...validationErrors],
      businessCard: [...businessCard],
    });
  };

  const handleSubmit = () => {
    if (checkForErrors()) {
      finalValidation();
      return;
    }
    const { businessCard, aboutMe } = values;
    const userType = returnUserType();
    const formattedBusinessCard = filter(businessCard, (card) => {
      return card.companyId.name;
    }).map((card) => {
      let omitData = omit(card, "_id");
      omitData = omit(card, "id");
      return {
        ...omitData,
        companyId: get(card, "companyId._id", ""),
        companyName: get(card, "companyId.name", ""),
        selectedOffice: get(card, "selectedOffice.city", ""),
        businessCardId: card.businessCardId || "",
      };
    });
    aboutMe.phone = deformatPhoneNumber(aboutMe.phone);
    const dataToSubmit = {
      businessCard: formattedBusinessCard,
      aboutMe: {
        ...aboutMe,
        myOffice:
          get(businessCard, "[0].nickname") ||
          get(aboutMe, "myOffice.city", ""),
        email: sameAsCompany
          ? get(businessCard, "[0].email")
          : get(aboutMe, "email", ""),
        phone: sameAsCompany
          ? get(businessCard, "[0].phoneNumber")
          : get(aboutMe, "phone", ""),
        profileUrl: get(aboutMe, "profileUrl.image")
          ? get(aboutMe, "profileUrl")
          : get(aboutMe, "profileUrl.image") ||
            get(userData, "profileImg.square") ||
            get(userData, "profileImg.circle") ||
            get(userData, "profileUrl") ||
            get(userInfo, "profileUrl"),
      },
      isTeamMember: values.isTeamMember,
      isOfficialRepresentative: values.isOfficialRepresentative,
      isCurrentEmployee: values.isCurrentEmployee,
    };
    if (userType === "admin") {
      dispatch(
        ProfileActions.updateBusinessCard(dataToSubmit, (res) => {
          if (!res.error.error) {
            dispatch({
              type: ProfileTypes.UPDATE_BUSINESSCARD_DATA,
              payload: res.data,
            });
            handleNext();
          }
        })
      );
      // dispatch({
      //   type: ProfileTypes.UPDATE_BUSINESSCARD_DATA,
      //   payload: dataToSubmit,
      // });
      // handleNext();
    } else {
      const teamMemberBusinessCard = filter(businessCard, (card) => {
        return card.companyId.name;
      }).map((card, index) => {
        delete card._id;
        return {
          ...card,
          companyId: get(card, "companyId._id", ""),
          companyName: get(card, "companyId.name", ""),
          selectedOffice: get(card, "selectedOffice.city", ""),
          id: "",
        };
      });
      const teamMemberData = {
        ...dataToSubmit,
        businessCard: teamMemberBusinessCard,
        aboutMe: {
          ...aboutMe,
          myOffice: get(aboutMe, "myOffice.city", ""),
        },
      };
      dispatch(
        ProfileActions.updateBusinessCard(teamMemberData, (res) => {
          if (!res.error) {
            setValues({ ...values, showTmSuccessModal: true });
          }
        })
      );
    }
  };

  const redirectToSpack = () => {
    // SINCE WE DO NOT HAVE PROFILE FOR TEAM MEMBER REGISTRATION, REDIRECTING TO S PACK
    dispatch(LoginActions.logOut());
    let host = get(window, "location.host", "");
    if (host) {
      if (host.includes("localhost")) {
        host = "localhost:3000";
        window.location.href = `http://${host}/logout`;
      } else {
        if (host.includes("-dev-")) {
          host = "tradeworks-spack-dev-fe.azurewebsites.net";
        }
        if (host.includes("-qa-")) {
          host = "tradeworks-spack-qa-fe.azurewebsites.net";
        }
        window.location.href = `https://${host}/logout`;
      }
    }
  };

  const triggerCountryDropdown = (card) => () => {
    const { showCountryDropdown } = values;
    card.showCountryDropdown = !card.showCountryDropdown;
    setValues({ ...values });
  };

  const returnAggrementCheckbox = (userType) => {
    /**
     *
     * EACH ENTITES HAVE RESPECTIVE OPTIONS TO CHECK
     * VALIDATING AND RETURNING OPTIONS CONDITIONALLY
     *
     */

    switch (userType) {
      case "admin":
        return (
          <>
            <Row className={classes.checkbox_section}>
              <Element_Required_Icon
                className={
                  checkErrors(0, "isCurrentEmployee")
                    ? classes.checkBox_required_red
                    : `${classes.checkBox_required}`
                }
              />
              <CustomCheckbox
                label={strings.company_wizard.step2.checkbox.admin_a_1}
                className={classes.checkbox_aggrement}
                name="isCurrentEmployee"
                checked={isCurrentEmployee}
                onChange={handleInputChange}
              />
            </Row>
            <Row className={classes.checkbox_section}>
              <Element_Required_Icon
                className={
                  checkErrors(0, "isOfficialRepresentative")
                    ? classes.checkBox_required_red
                    : `${classes.checkBox_required}`
                }
              />
              <CustomCheckbox
                label={strings.company_wizard.step2.checkbox.admin_a_2}
                className={classes.checkbox_aggrement_2}
                name="isOfficialRepresentative"
                checked={isOfficialRepresentative}
                onChange={handleInputChange}
              />
            </Row>
          </>
        );
      case "teamMember":
        return (
          <Row className={classes.checkbox_section}>
            <CustomCheckbox
              label={strings.company_wizard.step2.checkbox.team_mem}
              className={classes.checkbox_aggrement}
              name="isTeamMember"
              onChange={handleInputChange}
              checked={isTeamMember}
            />
            <Element_Required_Icon className={classes.checkBox_required} />
          </Row>
        );

      default:
        return null;
    }
  };

  const handleCountryChange = (value, card) => {
    card.phonenumber = "";
    card.countryCode = value;
    card.showCountryDropdown = !card.showCountryDropdown;
    setValues({
      ...values,
    });
  };

  const returnUserType = () => {
    const { businessCard } = values;
    if (!businessCard) {
      return;
    }
    if (businessCard.length > 1) {
      // ADMIN CASE(multiple business cards)
      return "admin";
    }
    const card = businessCard[0];
    const adminId = get(card, "cPackUserId", null);
    if (adminId === get(userInfo, "id", "")) {
      return "admin";
    }
    const hasAdmin = get(card, "companyId.isCompanyHavingAdmin", false);
    const newCompany = get(card, "isNewCompany", false);
    if (!newCompany) {
      if (hasAdmin) {
        // HAS ADMIN, NOT NEW COMPANY(REGISTERED)
        return "teamMember";
      }
      return "admin";
    }
    return "admin";
  };

  const handleAlertModalClose = (name) => (event) => {
    setValues({ ...values, [name]: false });
  };

  const setCompany = () => {
    /**
     * WHEN USER TRIES TO REGISTER EXISTING COMPANY, CLICKS YES IN ALERT MODAL
     * SETTING THE "FOUND COMPANY" TO THE STATE
     */
    const { foundCompany } = values;
    const { address } = foundCompany;
    const flag = find(countries_flags, { code: "US" });
    const headquarter = find(address, { type: "headquarters" });
    const { city, state, zip } = headquarter;
    setValues({
      ...values,
      businessCard: [
        {
          companyId: foundCompany,
          flag: { ...flag },
          city,
          state,
          zip,
          id: uuidv4(),
        },
      ],
      aboutMe: {
        profileUrl: {},
        email: get(userInfo, "email", ""),
        phone: get(userInfo, "phoneNumber", ""),
      },
      showAlertModal: false,
      isTeamMember: false,
      isCurrentEmployee: false,
      isOfficialRepresentative: false,
    });
  };

  const setActiveField = (name, id) => () => {
    /**
     * WHEN USER IS IN THE INPUT, SHOW LABEL OF THAT PARTICULAR INPUT
     */
    setValues({ ...values, activeField: { name, id } });
  };

  const removeActiveField = () => {
    setValues({ ...values, activeField: {} });
  };

  const checkForErrors = () => {
    const {
      businessCard,
      errors,
      isTeamMember,
      isCurrentEmployee,
      isOfficialRepresentative,
    } = values;
    const userType = returnUserType();

    const errValues = Object.values(errors).map((each) => {
      if (each.email || each.website) {
        return true;
      }
      return false;
    });
    const hqCard = find(businessCard, { isHeadOffice: true });
    if (!hqCard) {
      return true;
    }
    const isCompanySelected = get(hqCard, "companyId.name", false);
    const isCityAvailable = get(hqCard, "city", "");
    const isEmailAvailable = get(hqCard, "email", "");
    const isPhoneAvailable = get(hqCard, "phoneNumber", "");
    const agreements =
      userType === "teamMember"
        ? isTeamMember
        : isCurrentEmployee && isOfficialRepresentative;
    if (
      errValues.includes(true) ||
      !isCompanySelected ||
      !isCityAvailable ||
      !isEmailAvailable ||
      !isPhoneAvailable ||
      !agreements
    ) {
      return true;
    }
    return false;
  };

  const handlePingAdmin = () => {
    setValues({ ...values, showPingAdmin: !values.showPingAdmin });
  };

  const handleToggleModal = (closeModal, openModal) => {
    if (!openModal) {
      setValues({ ...values, [closeModal]: false });
      return;
    }
    setValues({ ...values, [openModal]: true, [closeModal]: false });
  };

  /**this functions returns placeholder as the previous card value */
  const returnPlaceholder = (cardIndex) => {
    const { businessCard } = values;
    const foundCard = businessCard[cardIndex - 1];
    if (!foundCard || cardIndex === 0) {
      return "Company Name";
    }
    return get(foundCard, "companyId.name", "Company Name");
  };

  const {
    isCurrentEmployee,
    isTeamMember,
    isOfficialRepresentative,
    businessCard,
    aboutMe,
    errors,
    showAlertModal,
    activeField,
    showPingAdmin,
    showPingAdminSuccess,
    showTmSuccessModal,
    sameAsCompany,
    showCountryDropdown,
    countryCode,
    hasExistingAdmin,
  } = values;
  const headQuarterCard = find(businessCard, { isHeadOffice: true });
  localStorage.setItem(
    "isNewCompany",
    get(headQuarterCard, "isNewCompany", false)
  );
  const addresses = headQuarterCard
    ? get(headQuarterCard, "companyId.address", [])
    : [];
  const userType = returnUserType();
  const hasFormErrors = checkForErrors();
  setUserType(userType);
  return (
    <>
      <Row className={classes.block}>
        <Text
          size={20}
          color={color.primary_palette.franklin_purple}
          family="gillsans_sb"
          fontWeight={900}
          className={classes.step1_heading}
        >
          {userType === "admin"
            ? strings.company_wizard.step1.titles.step1_2
            : strings.company_wizard.step1.titles.step1}
        </Text>
        {userType === "admin" && (
          <>
            {" "}
            <Text
              size={16}
              color={color.secondary_palette.grays.up_in_smoke}
              family="avenir_roman"
              className={classes.step1_subheading}
            >
              {strings.company_wizard.step1.titles.step1_des}
            </Text>
            <Text
              size={16}
              color={color.secondary_palette.grays.up_in_smoke}
              family="avenir_roman"
              className={classes.step1_subheading}
            >
              {strings.company_wizard.step1.titles.step1_des2}
            </Text>
          </>
        )}
        {map(businessCard, (card, index) => {
          return (
            <Row className={classes.step_1}>
              <Row className={classes.card_blk}>
                <Row className={`${classes.main_heading}`}>
                  {index === 0 && (
                    <>
                      <Element_Required_Icon
                        className={
                          checkErrors(index, "companyName")
                            ? `${classes.select_field_required_red_company}`
                            : classes.select_field_required_company
                        }
                      />
                      <CustomAutoComplete
                        // disableOpenOnFocus
                        businessCard
                        noOptionsText={"Type a new Company Name above"}
                        placeholder={returnPlaceholder(index)}
                        value={card.companyId}
                        options={companies || []}
                        getOptionLabel={(option) => option.name || ""}
                        className={`${classes.companies}`}
                        ListboxComponent={React.forwardRef(
                          ({ children, ...rest }, ref) => (
                            <div>
                              <Text
                                size={14}
                                color={color.primary_palette.highlight_purple}
                                family="gillsans_sb"
                                className={classes.dropdown_headings}
                              >
                                {
                                  strings.company_wizard.step1.labels
                                    .company_dropdown_top
                                }
                              </Text>
                              <div className={`${classes.scroll}`}>
                                <CustomScrollbars ref={ref} {...rest}>
                                  {children}
                                </CustomScrollbars>
                              </div>
                              <Text
                                size={14}
                                color={color.primary_palette.highlight_purple}
                                family="gillsans_sb"
                                className={classes.dropdown_headings}
                              >
                                {
                                  strings.company_wizard.step1.labels
                                    .company_dropdown_bottom
                                }
                              </Text>
                            </div>
                          )
                        )}
                        onChange={(event, newInputValue) => {
                          handleCompanySelect(event, newInputValue, card);
                        }}
                        onBlur={onCompanyBlur(card, index)}
                      />
                    </>
                  )}
                  {index !== 0 && (
                    <CustomTextField
                      className={`${classes.companies}`}
                      name="companyId"
                      placeholder={returnPlaceholder(index)}
                      onBlur={handleBranchBlur(card)}
                      defaultValue={get(card, "companyId.name", "")}
                    />
                  )}
                </Row>
                <Row className={classes.position}>
                  {/* <CustomTextField
                    value={
                      userType === "teamMember"
                        ? `${get(userInfo, "firstName", "")} ${get(
                            userInfo,
                            "lastName",
                            ""
                          )}`
                        : ""
                    }
                    className={classes.flag_input}
                  /> */}
                  {/* <CustomSelect
                    className={classes.country_dropdown}
                    IconComponent={Chevron_Down_Icon}
                    renderValue={(value) => (
                      <Row>
                        {get(value, "flag", false) && (
                          <img
                            src={value.flag}
                            className={classes.country_image}
                          />
                        )}
                        <Text
                          size={10}
                          color={color.knight_armour}
                          className={classes.country_code_name}
                        >
                          {get(value, "code", "country")}
                        </Text>
                      </Row>
                    )}
                    value={get(card, "flag", "")}
                    MenuProps={{
                      getContentAnchorEl: null,
                      disableScrollLock: true,
                      anchorOrigin: {
                        vertical: "bottom",
                        horizontal: "left",
                      },
                    }}
                  >
                    <div className={classes.country_dropdown_list}>
                      <CustomScrollbars
                        className={classes.country_dropdown_scroll}
                      >
                        {map(countries_flags || [], (country) => {
                          return (
                            <Row className={classes.country_item_row}>
                              <img
                                src={country.flag}
                                className={classes.country_image}
                              />
                              <MenuItem
                                value={country}
                                name="flag"
                                onClick={handleCountryFlagChange(
                                  card,
                                  "flag",
                                  country
                                )}
                              >
                                {get(country, "code", "")}
                              </MenuItem>
                            </Row>
                          );
                        })}
                      </CustomScrollbars>
                    </div>
                  </CustomSelect> */}
                </Row>
                <Row className={classes.margin_top}>
                  <Row className={classes.address}>
                    <div style={{ position: "relative" }}>
                      {get(activeField, "name", "") === "address" &&
                        get(activeField, "id", "") === card.id && (
                          <Text
                            size={10}
                            color={color.primary_palette.franklin_purple}
                            family="gillsans_sb"
                            className={classes.address_input_label}
                          >
                            {strings.company_wizard.step1.titles.address}
                          </Text>
                        )}
                      <CustomTextField
                        placeholder="201 Fort Washington Street"
                        name="address"
                        disabled={
                          (get(card.companyId, "name", null) ? false : true) ||
                          userType === "teamMember"
                        }
                        onChange={handleBusinessCardChange(card)}
                        value={get(card, "address", "")}
                        className={`${classes.address_input} ${classes.width190}`}
                        onFocus={setActiveField("address", card.id)}
                        onBlur={handleBusinessCardBlur(card)}
                      />
                    </div>
                    <div style={{ position: "relative" }}>
                      {get(activeField, "name", "") === "address1" &&
                        get(activeField, "id", "") === card.id && (
                          <Text
                            size={10}
                            color={color.primary_palette.franklin_purple}
                            family="gillsans_sb"
                            className={classes.address_input_label}
                          >
                            {strings.company_wizard.step1.titles.address}
                          </Text>
                        )}
                      <CustomTextField
                        placeholder="Suite 17S"
                        className={`${classes.address_input} ${classes.width190}`}
                        disabled={
                          (get(card.companyId, "name", null) ? false : true) ||
                          userType === "teamMember"
                        }
                        onChange={handleBusinessCardChange(card)}
                        name="address1"
                        value={get(card, "address1", "")}
                        onFocus={setActiveField("address1", card.id)}
                        onBlur={handleBusinessCardBlur(card)}
                      />
                    </div>
                    <Row>
                      <div style={{ position: "relative", width: "50%" }}>
                        {get(activeField, "name", "") === "city" &&
                          get(activeField, "id", "") === card.id && (
                            <Text
                              size={10}
                              color={color.primary_palette.franklin_purple}
                              family="gillsans_sb"
                              className={classes.address_input_label}
                            >
                              {strings.company_wizard.step1.titles.city}
                            </Text>
                          )}
                        <Element_Required_Icon
                          className={
                            checkErrors(index, "city")
                              ? `${classes.select_field_required_red}`
                              : classes.select_field_required
                          }
                        />
                        <CustomTextField
                          placeholder="New York"
                          // className={`${classes.city_input} ${classes.cityWidth}`}
                          className={
                            checkErrors(index, "city")
                              ? `${classes.city_input_error} ${classes.cityWidth}`
                              : `${classes.city_input} ${classes.cityWidth}`
                          }
                          disabled={
                            (get(card.companyId, "name", null)
                              ? false
                              : true) || userType === "teamMember"
                          }
                          onChange={handleBusinessCardChange(card)}
                          name="city"
                          value={get(card, "city", "")}
                          onFocus={setActiveField("city", card.id)}
                          onBlur={handleBusinessCardBlur(card)}
                        />
                      </div>
                      <div style={{ position: "relative", width: "23%" }}>
                        {get(activeField, "name", "") === "state" &&
                          get(activeField, "id", "") === card.id && (
                            <Text
                              size={10}
                              color={color.primary_palette.franklin_purple}
                              family="gillsans_sb"
                              className={classes.address_input_label}
                            >
                              {strings.company_wizard.step1.titles.state}
                            </Text>
                          )}
                        <Element_Required_Icon
                          className={
                            checkErrors(index, "state")
                              ? `${classes.select_field_required_red}`
                              : classes.select_field_required
                          }
                        />
                        <CustomTextField
                          placeholder="NY"
                          // className={`${classes.txt_align_left} ${classes.address_input} ${classes.stateWidth}`}
                          className={
                            checkErrors(index, "state")
                              ? `${classes.address_input_error} ${classes.stateWidth}`
                              : `${classes.address_input} ${classes.stateWidth}`
                          }
                          disabled={
                            (get(card.companyId, "name", null)
                              ? false
                              : true) || userType === "teamMember"
                          }
                          onChange={handleBusinessCardChange(card)}
                          name="state"
                          value={get(card, "state", "")}
                          onFocus={setActiveField("state", card.id)}
                          onBlur={handleBusinessCardBlur(card)}
                        />
                      </div>
                      <div style={{ position: "relative", width: "25%" }}>
                        {get(activeField, "name", "") === "zip" &&
                          get(activeField, "id", "") === card.id && (
                            <Text
                              size={10}
                              color={color.primary_palette.franklin_purple}
                              family="gillsans_sb"
                              className={classes.address_input_label}
                            >
                              {strings.company_wizard.step1.titles.zip}
                            </Text>
                          )}
                        <CustomTextField
                          placeholder="10001"
                          className={`${classes.txt_align_left} ${classes.address_input}`}
                          disabled={
                            (get(card.companyId, "name", null)
                              ? false
                              : true) || userType === "teamMember"
                          }
                          onChange={handleBusinessCardChange(card)}
                          name="zip"
                          value={get(card, "zip", "")}
                          onFocus={setActiveField("zip", card.id)}
                          onBlur={handleBusinessCardBlur(card)}
                        />
                      </div>
                    </Row>
                  </Row>
                  <Row className={`${classes.address}`}>
                    <div style={{ position: "relative" }}>
                      {((get(activeField, "name", "") === "email" &&
                        get(activeField, "id", "") === card.id) ||
                        get(errors[card.id], "email", false)) && (
                        <Text
                          size={10}
                          color={
                            get(errors[card.id], "email", false)
                              ? color.red_carpet
                              : color.primary_palette.franklin_purple
                          }
                          family="gillsans_sb"
                          className={classes.address_input_label}
                        >
                          {get(errors[card.id], "email", false) ? (
                            <img
                              src="assets/icons/info_1.svg"
                              className={classes.info_img}
                            />
                          ) : (
                            ""
                          )}
                          {strings.company_wizard.step1.titles.email}
                        </Text>
                      )}
                      <Element_Required_Icon
                        className={
                          checkErrors(index, "email")
                            ? `${classes.select_field_required_red}`
                            : classes.select_field_required
                        }
                      />
                      <CustomTextField
                        placeholder="<EMAIL>"
                        key={userInfo}
                        name="email"
                        // disabled={
                        //   get(card.companyId, "name", null) ? false : true
                        // }
                        value={get(card, "email", "")}
                        onChange={handleBusinessCardChange(card)}
                        // className={`${classes.address_input} ${classes.width190} ${classes.paddingLeft10}`}
                        className={
                          checkErrors(index, "email")
                            ? `${classes.address_input_error} ${classes.width190} ${classes.paddingLeft10}`
                            : `${classes.address_input} ${classes.width190} ${classes.paddingLeft10}`
                        }
                        onFocus={setActiveField("email", card.id)}
                        onBlur={handleBusinessCardBlur(card)}
                      />
                    </div>
                    <div style={{ position: "relative" }}>
                      {((get(activeField, "name", "") === "website" &&
                        get(activeField, "id", "") === card.id) ||
                        get(errors[card.id], "website", false)) && (
                        <Text
                          size={10}
                          color={
                            get(errors[card.id], "website", false)
                              ? color.red_carpet
                              : color.primary_palette.franklin_purple
                          }
                          family="gillsans_sb"
                          className={classes.address_input_label}
                        >
                          {get(errors[card.id], "website", false) ? (
                            <img
                              src="assets/icons/info_1.svg"
                              className={classes.info_img}
                            />
                          ) : (
                            ""
                          )}
                          {strings.company_wizard.step1.titles.website}
                        </Text>
                      )}
                      <CustomTextField
                        placeholder="Website.com"
                        disabled={
                          (get(card.companyId, "name", null) ? false : true) ||
                          userType === "teamMember"
                        }
                        className={`${classes.address_input} ${classes.width190} ${classes.marginLeft10}`}
                        onChange={handleBusinessCardChange(card)}
                        value={get(card, "website", "")}
                        name="website"
                        onFocus={setActiveField("website", card.id)}
                        onBlur={handleBusinessCardBlur(card)}
                      />
                    </div>
                    <div style={{ position: "relative" }}>
                      {((get(activeField, "name", "") === "phone" &&
                        get(activeField, "id", "") === card.id) ||
                        get(errors[card.id], "phone", false)) && (
                        <Text
                          size={10}
                          color={color.primary_palette.franklin_purple}
                          family="gillsans_sb"
                          className={classes.address_input_label}
                        >
                          {strings.company_wizard.step1.titles.phone}
                        </Text>
                      )}
                      <Element_Required_Icon
                        className={
                          checkErrors(index, "phoneNumber")
                            ? `${classes.select_field_required_red}`
                            : classes.select_field_required
                        }
                      />
                      <CustomTextField
                        className={
                          checkErrors(index, "phoneNumber")
                            ? `${classes.phone_input_error} ${classes.phn_num}`
                            : `${classes.phone_input} ${classes.phn_num}`
                        }
                        // className={`${classes.phone_input} ${classes.phn_num}`}
                        // disabled={
                        //   (get(card.companyId, "name", null) ? false : true) ||
                        //   userType === "teamMember"
                        // }
                        onChange={handleBusinessCardChange(card)}
                        name={"phoneNumber"}
                        placeholder="************"
                        value={
                          get(card.phoneNumber, "length", 0) === 10
                            ? formatPhoneNumber(
                                get(card, "phoneNumber", ""),
                                card
                              )
                            : get(card, "phoneNumber", "")
                        }
                        onBlur={onPhoneNumberBlur(card)}
                        inputProps={{
                          maxLength: card.countryCode !== "nonUSA" ? 10 : 25,
                        }}
                        InputProps={{
                          onFocus:
                            get(card, "flag.code", "") === "US" &&
                            onPhoneNumberFocus(card),
                          endAdornment: (
                            <InputAdornment
                              position="end"
                              tabIndex="-1"
                              aria-label="toggle password visibility"
                              className={classes.password_icon}
                            >
                              <div className={classes.phoneNumberActions}>
                                <img
                                  src={
                                    card.countryCode === "nonUSA"
                                      ? "/assets/images/nonUSA.PNG"
                                      : "/assets/images/USA.PNG"
                                  }
                                  className={classes.countryImage}
                                />
                                <Chevron_Down_Icon
                                  className={classes.dropDownIcon}
                                  onClick={triggerCountryDropdown(card)}
                                />
                                {card.showCountryDropdown && (
                                  <div className={classes.dropdownOptions}>
                                    <div
                                      onClick={() => {
                                        handleCountryChange("USA", card);
                                      }}
                                    >
                                      USA
                                    </div>
                                    <div
                                      onClick={() => {
                                        handleCountryChange("nonUSA", card);
                                      }}
                                    >
                                      Non-USA
                                    </div>
                                  </div>
                                )}
                              </div>
                            </InputAdornment>
                          ),
                        }}
                      />
                      {/* <CustomSelect
                        className={classes.country_dropdown}
                        IconComponent={Chevron_Down_Icon}
                        renderValue={(value) => (
                          <Row>
                            {get(value, "flag", false) && (
                              <img
                                src={value.flag}
                                className={classes.country_image}
                              />
                            )}
                          </Row>
                        )}
                        value={get(card, "flag", "")}
                        MenuProps={{
                          getContentAnchorEl: null,
                          disableScrollLock: true,
                          anchorOrigin: {
                            vertical: "bottom",
                            horizontal: "left",
                          },
                        }}
                      >
                        <div className={classes.country_dropdown_list}>
                          <CustomScrollbars
                            className={classes.country_dropdown_scroll}
                          >
                            {map(countries_flags || [], (country) => {
                              return (
                                <Row className={classes.country_item_row}>
                                  <img
                                    src={country.flag}
                                    className={classes.country_image}
                                  />
                                  <MenuItem
                                    value={country}
                                    name="flag"
                                    onClick={handleCountryFlagChange(
                                      card,
                                      "flag",
                                      country
                                    )}
                                  >
                                    {get(country, "code", "")}
                                  </MenuItem>
                                </Row>
                              );
                            })}
                          </CustomScrollbars>
                        </div>
                      </CustomSelect> */}
                    </div>
                  </Row>
                </Row>
              </Row>
              {
                <Row>
                  {userType === "admin" && (
                    <Row className={classes.checkbox_section_hq}>
                      <CustomCheckbox
                        label={strings.company_wizard.step1.titles.headquarters}
                        className={classes.checkbox}
                        checked={get(card, "isHeadOffice", false)}
                        name="isHeadOffice"
                        id={card.id}
                        onChange={handleInputChange}
                      />
                    </Row>
                  )}
                  <Row className={classes.choose_office_section}>
                    {userType === "teamMember" && (
                      <CustomSelect
                        className={classes.choose_office}
                        IconComponent={Chevron_Down_Icon}
                        renderValue={(value) =>
                          value.city || "Choose your office"
                        }
                        value={get(card, "selectedOffice", {})}
                        MenuProps={{
                          getContentAnchorEl: null,
                          disableScrollLock: true,
                          anchorOrigin: {
                            vertical: "bottom",
                            horizontal: "left",
                          },
                        }}
                      >
                        <div className={classes.choose_option}>
                          {map(addresses, (address, index) => {
                            return (
                              <MenuItem
                                value={address}
                                onClick={handleOfficeSelect(
                                  address,
                                  "selectedOffice",
                                  card
                                )}
                              >
                                {get(address, "city", "")}
                              </MenuItem>
                            );
                          })}
                          <Row className={classes.ping_blk}>
                            <Text
                              size={14}
                              color={color.primary_palette.franklin_purple}
                              onClick={handlePingAdmin}
                            >
                              Ping Admin to add/edit office
                            </Text>
                          </Row>
                        </div>
                      </CustomSelect>
                    )}
                    {userType === "admin" &&
                      index === businessCard.length - 1 && (
                        <CustomButton
                          className={classes.add_office}
                          onClick={handleAddBusinessCard}
                        >
                          <Lil_Plus_filled />
                          {strings.company_wizard.step1.titles.add_office}
                        </CustomButton>
                      )}
                  </Row>
                </Row>
              }
              {userType === "admin" && (
                <Row className={classes.nicknameWrapper}>
                  <Text
                    size={16}
                    color={
                      checkErrors(index, "nickName")
                        ? color.primary_palette.christmas_red
                        : color.primary_palette.franklin_purple
                    }
                    family="gillsans_sb"
                  >
                    <Element_Required_Icon
                      className={
                        checkErrors(index, "nickName")
                          ? `${classes.nickName_required_error}`
                          : classes.nickName_required
                      }
                    />
                    Office Nickname
                  </Text>
                  <CustomTextField
                    // className={classes.nickname}
                    className={
                      checkErrors(index, "nickName")
                        ? `${classes.nickname_error}`
                        : `${classes.nickname}`
                    }
                    name="nickname"
                    key={card.nickname}
                    placeholder="New York"
                    onBlur={handleBusinessCardBlur(card)}
                    defaultValue={get(card, "nickname", "")}
                  />
                </Row>
              )}
            </Row>
          );
        })}
        {/* {(get(headQuarterCard, "companyId.name", "") ||
          get(headQuarterCard, "companyId._id", "")) && (
          <> */}
        <div className={classes.checkbox_section_container}>
          {returnAggrementCheckbox("admin")}
          {userType === "teamMember" && (
            <Text
              size={16}
              family="avenir_light"
              className={classes.ping_admin_txt}
            >
              {strings.company_wizard.step1.labels.ping_admin}
            </Text>
          )}
        </div>
        {/* </>
        )} */}
        {get(businessCard[0], "companyId.name", false) && (
          <Row className={`${classes.block} ${classes.block_space}`}>
            <Text
              size={20}
              color={color.primary_palette.franklin_purple}
              family="gillsans_sb"
              className={classes.step1_heading}
            >
              {userType === "teamMember"
                ? strings.company_wizard.step1.titles.step2_2
                : strings.company_wizard.step1.titles.step2}
            </Text>
            <Text
              size={16}
              color={color.secondary_palette.grays.up_in_smoke}
              family="avenir_roman"
              className={classes.step1_subheading}
            >
              {userType === "teamMember"
                ? strings.company_wizard.step1.titles.step2_des_1
                : strings.company_wizard.step1.titles.step2_des}
            </Text>
            <CustomCheckbox
              label="Same as my company information"
              className={classes.checkbox_aggrement_comp}
              name="sameAsCompany"
              checked={sameAsCompany}
              onChange={handleInputChange}
            />
            <BusinessCardTwo
              key={userType}
              aboutMe={aboutMe}
              newCompany={get(headQuarterCard, "isNewCompany", false)}
              offices={addresses}
              userData={userData}
              userInfo={userInfo}
              userType={userType}
              imageUpload={imageUpload}
              handleOfficeSelect={handleOfficeSelect}
              checkErrors={checkErrors}
              onPhoneNumberFocus={onPhoneNumberFocus}
              handleChange={handleAboutMeChange}
              aboutMePhoneNumBlur={aboutMePhoneNumBlur}
              clearPhoneNumber={clearPhoneNumber}
              clearEmail={clearEmail}
              phoneNumberLength={
                get(headQuarterCard, "flag.code", "") === "US" ? 10 : 20
              }
              businessCardData={businessCard}
              sameAsCompany={sameAsCompany}
              hideSameCompany={false}
            />
          </Row>
        )}
        {values.newErrors && values.newErrors.length !== 0 && (
          <Row
            style={{
              justifyContent: "end",
              marginRight: "100px",
            }}
          >
            <img
              src="assets/images/warning.svg"
              className={classes.warn_icon}
            />
            <Text
              size={15}
              color={color.primary_palette.christmas_red}
              family="gillsans_r"
              className={`${classes.errorMessage} `}
            >
              Please review required fields above
            </Text>
          </Row>
        )}
        {userType === "admin" ? (
          <Row className={classes.btn_blk}>
            <Row className={classes.btn_alignment}>
              <CustomButton
                variant="outlined"
                color="primary"
                // disabled={hasFormErrors}
                onClick={handleSubmit}
                className={`${classes.greenactionButtons} ${classes.second_btn_width}`}
              >
                {strings.general.titles.save_continue}
              </CustomButton>
            </Row>
          </Row>
        ) : (
          <Row className={classes.btn_blk}>
            <Row className={classes.btn_alignment_2}>
              <CustomButton
                variant="outlined"
                color="primary"
                onClick={handleSubmit}
                className={`${classes.actionButtons} ${classes.join_us_btn}`}
              >
                {strings.general.titles.join}
              </CustomButton>
            </Row>
          </Row>
        )}
        {userType === "teamMember" && (
          <div className={classes.ping_admin_wrap}>
            {" "}
            <Text
              size={16}
              color={color.secondary_palette.grays.up_in_smoke}
              family="avenir_roman"
              className={classes.tm_ping_admin_txt2}
              onClick={handlePingAdmin}
              style={{ cursor: "pointer" }}
            >
              <span className={classes.ping_admin}>
                {strings.company_wizard.step1.labels.tm_ping_admin}
              </span>
              {strings.company_wizard.step1.labels.tm_ping_admin2}
              <img
                src="assets/images/ping_flag.png"
                className={classes.ping_flag}
              />
            </Text>
          </div>
        )}
        {showAlertModal && (
          <CompanyExistsWarningModal
            setCompany={setCompany}
            handleAlertModalClose={handleAlertModalClose("showAlertModal")}
          />
        )}
      </Row>

      {showPingAdmin && (
        <PingAdmin
          handlePingAdmin={handlePingAdmin}
          handleToggleModal={handleToggleModal}
          businessCard={get(values, "businessCard[0]", {})}
        />
      )}
      {showPingAdminSuccess && (
        <PingAdminSuccess handleToggleModal={handleToggleModal} />
      )}
      {showTmSuccessModal && (
        <JoinUsSuccessModal
          businessCard={values.businessCard}
          redirectApp={redirectToSpack}
        />
      )}
    </>
  );
}

export default businessCardStyles(BusinessCardOne);
