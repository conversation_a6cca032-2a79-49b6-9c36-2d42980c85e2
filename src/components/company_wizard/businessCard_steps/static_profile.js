import React, { useEffect } from "react";
import { map } from "lodash";

import styleSheet from "../styles/static_profiles_styles";
import Row from "../../common/ui_kit/row";
import Text from "../../common/ui_kit/text";
import Facebook_Icon from "../../data_display/icons/Facebook_Outline";
import Twitter_Icon from "../../data_display/icons/Twitter_Colored";
import LinkedIn_Icon from "../../data_display/icons/Linkedin_Outline";
import Instagram_Icon from "../../data_display/icons/Instagram_Outline";
import ChevronDownIcon from "../../data_display/icons/Arrow_Chevron_Down";
import { color } from "../../../utilities/themes";

function StaticProfile(props) {
  const { classes } = props;
  const { hover_section } = props;

  const openPositions = [
    {
      rightName: "Posted",
      name: "Draughter",
      city: "New York, NY",
      date: "May 23",
    },
    {
      rightName: "Posted",
      name: "Junior Architect",
      city: "New York, NY",
      date: "May 20",
    },
    {
      rightName: "Posted",
      name: "Accountant",
      city: "London, UK",
      date: "May 24",
    },
    {
      rightName: "Posted",
      name: "Project Manager",
      city: "New York, NY",
      date: "May 10",
    },
    {
      rightName: "Posted",
      name: "Project Manager",
      city: "New York, NY",
      date: "May 10",
    },
  ];
  const franklinSection = [
    { name: "Recomendation", icon: "assets/images/star.png" },
    { name: "Work Quality", icon: "assets/images/plus.png" },
    { name: "Cost Evaluation", icon: "assets/images/dollar.png" },
    { name: "Value Analysis", icon: "assets/images/secure.png" },
  ];
  const waterCoolerData = [
    {
      image: "",
      name: "Elizabeth Ames Franklin",
      profession: "CEO - Founder",
      description:
        "Nulla laoreet tempus tellus, a vestibulum odio egestas pretium. Suspendisse pharetra libero mi, id egestas massa ullamcorper a.",
    },
    {
      image: "",
      name: "Anton London",
      profession: "Director of Marketing",
      description:
        "Nulla laoreet tempus tellus, a vestibulum odio egestas pretium. Suspendisse pharetra libero mi, id egestas massa ullamcorper a.",
    },
    {
      image: "",
      name: "Link Salas",
      profession: "Creative Director",
      description:
        "Nulla laoreet tempus tellus, a vestibulum odio egestas pretium. Suspendisse pharetra libero mi, id egestas massa ullamcorper a.",
    },
  ];
  const tabs = ["About", "People", "Offices", "Culture", "Connect"];
  const officeTabs = ["New York City", "London", "Paris", "Portland"];

  const returnOpenPositionSection = () => {
    return (
      <>
        <div className={classes.input_happy_container}>
          <input
            placeholder="Job title, location, etc"
            className={classes.open_pos_input}
            family="gillsans_r"
          />{" "}
          <img
            src="assets/icons/icon_happy_arrow.svg"
            className={classes.happy_icon}
            // alt={strings.loggin_header.titles.happy_arrow}
          />
        </div>
        <Row>
          <div className={`${classes.open_pos_left}`}>
            <Text
              size={8}
              color={color.form_colors.royal_purple_1}
              fontWeight={600}
              family="gillsans_r"
            >
              New York |
              <span className={classes.london_paris}> London | Paris</span>
            </Text>
          </div>
          <div className={classes.open_pos_right}>
            <Text
              size={8}
              color={color.primary_palette.black}
              fontWeight={300}
              family="gillsans_r"
            >
              <span className={classes.see_all}>See all</span>
            </Text>
          </div>{" "}
        </Row>
        <div className={classes.overflow_content}>
          {openPositions.map((position, index) => {
            return (
              <Row>
                <div className={classes.open_pos_left}>
                  <Text
                    size={8}
                    color={
                      index > 0
                        ? color.primary_palette.black
                        : color.form_colors.royal_purple_1
                    }
                    fontWeight={600}
                    family="gillsans_sb"
                  >
                    {position.name}
                  </Text>
                  <Text
                    size={7}
                    color={color.primary_palette.black}
                    fontWeight={300}
                    family="gillsans_light"
                    className={classes.step1_subheading}
                  >
                    {position.city}
                  </Text>
                </div>
                <div className={classes.open_pos_right}>
                  <Text
                    size={7}
                    color={color.primary_palette.black}
                    fontWeight={300}
                    family="gillsans_light"
                  >
                    {position.rightName}
                  </Text>
                  <Text
                    size={7}
                    color={color.primary_palette.black}
                    fontWeight={300}
                    family="gillsans_light"
                    className={classes.step1_subheading}
                  >
                    {position.date}
                  </Text>
                </div>{" "}
              </Row>
            );
          })}
        </div>
      </>
    );
  };

  const returnFranklinReportSection = () => {
    return franklinSection.map((position, index) => {
      return (
        <Row className={classes.franklinSection_data}>
          <div className={classes.open_pos_left_franklin}>
            <Text
              size={8}
              family="gillsans_r"
              fontWeight={600}
              color={color.primary_palette.black}
            >
              {position.name}
            </Text>
          </div>
          <div className={classes.open_pos_right_franklin}>
            <img src={position.icon} />
            <img src={position.icon} />
            <img src={position.icon} />
            <img src={position.icon} />
            {index % 2 === 0 && <img src={position.icon} />}{" "}
          </div>{" "}
        </Row>
      );
    });
  };

  const returnContactUsContent = () => {
    return (
      <div className={classes.contact_us_content}>
        <Row style={{ position: "relative" }}>
          <Text
            size={10}
            color={color.secondary_palette.purples.franklin_purple}
            family="gillsans_r"
            fontWeight={600}
          >
            New York
            <img
              src="..assets/images/contact_email.png"
              className={classes.mail_icon}
            />
          </Text>
          <img
            src="..assets/icons/minus_circle.svg"
            className={classes.contact_us_action_icons}
          />
        </Row>
        <Text
          size={8}
          family="gillsans_r"
          className={classes.contact_us_con_padding}
        >
          5 West 5th Ave - Suite 17S
        </Text>
        <Text size={8} family="gillsans_r">
          New York, New York 10001
        </Text>
        <Text size={8} family="gillsans_r">
          212.555.37546
        </Text>
        <Text
          size={8}
          family="gillsans_r"
          className={`${classes.contact_us_con_padding} ${classes.contact_us_border_bottom}`}
        >
          Link Salas • <EMAIL>
        </Text>
        <Row style={{ position: "relative" }}>
          <Text
            size={10}
            fontWeight={800}
            color={color.secondary_palette.purples.franklin_purple}
            family="gillsans_r"
            className={`${classes.contact_us_titles} ${classes.contact_us_border_bottom}`}
          >
            London
          </Text>
          <img
            src="..assets/icons/add.svg"
            className={classes.contact_us_action_icons}
          />
        </Row>
        <Row style={{ position: "relative" }}>
          <Text
            size={10}
            fontWeight={800}
            color={color.secondary_palette.purples.franklin_purple}
            family="gillsans_r"
            className={`${classes.contact_us_titles}`}
          >
            Paris
          </Text>
          <img
            src="..assets/icons/add.svg"
            className={classes.contact_us_action_icons}
          />
        </Row>
      </div>
    );
  };

  const returnCompanyFeedback = () => {
    const tweets = [
      {
        logo: "",
        name: "Nicolaus Architects",
        profileId: "@NicolausDesign",
        tweet:
          "Why just rent a house when you can rent a private ranch inside a massive reserve in the heart of Colorado’s Rocky Mountains?",
        hashTags: "#rental #dreamRental",
        date: "October 18,2018",
      },
      {
        logo: "",
        name: "Nicolaus Architects",
        profileId: "@NicolausDesign",
        tweet:
          "Stay on trend with tiny-house madness by renting your own miniature snow globe. This oasis, located in the heart of a tiny-house community in Jackson Hole",
        hashTags: "",
        date: "October 18,2018",
      },
      {
        logo: "",
        name: "Nicolaus Architects",
        profileId: "@NicolausDesign",
        tweet:
          "Stay on trend with tiny-house madness by renting your own miniature snow globe. This oasis, located in the heart of a tiny-house community in Jackson Hole",
        hashTags: "",
        date: "October 18,2018",
      },
    ];
    return (
      <>
        <Row>
          <Facebook_Icon className={classes.social_media_icons} />
          <Twitter_Icon className={classes.social_media_icons} />
          <LinkedIn_Icon className={classes.social_media_icons} />
          <Instagram_Icon className={classes.social_media_icons} />
        </Row>
        <div className={classes.tweets_section}>
          {map(tweets, (tweet) => {
            return (
              <div className={classes.tweet}>
                <Row>
                  <div className={classes.company_feedback_logo}></div>
                  <div className={classes.company_feedback_name}>
                    <Text
                      size={9}
                      family="gillsans_sb"
                      color={color.secondary_palette.purples.franklin_purple}
                      className={classes.profile_name_cf}
                    >
                      {tweet.name}
                    </Text>
                    <Text
                      size={7}
                      family="gillsans_light"
                      color={color.secondary_palette.grays.up_in_smoke}
                    >
                      {tweet.profileId}
                    </Text>
                  </div>
                </Row>
                <Text
                  size={8}
                  family="gillsans_light"
                  className={classes.tweet_content}
                >
                  {tweet.tweet}{" "}
                  <span className={classes.hashTags}>{tweet.hashTags}</span>
                </Text>
                <Row>
                  <div className={classes.twitter_share}>
                    <img src="assets/images/like.png" />
                    <img
                      src="assets/images/share_twitter.png"
                      className={classes.m_l_10}
                    />
                  </div>
                  <Text
                    size={7}
                    family="gillsans_r"
                    color={color.secondary_palette.grays.up_in_smoke}
                    className={classes.tweet_date}
                  >
                    {tweet.date}{" "}
                  </Text>
                </Row>
              </div>
            );
          })}
        </div>
      </>
    );
  };

  const returnAboutSection = () => {
    return (
      <Row>
        <div
          className={`${
            hover_section === "logoAndCover" && classes.section_highlight
          } ${hover_section === "logoAndCover" && classes.logo_cover} ${
            classes.about_content_1
          }`}
        >
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn%3AANd9GcTxFk0ZR419aRqiPCLJO3_NJ4ORTor2lFXV5lFqMx-Gxwfhmk6M&usqp=CAU"
            className={classes.about_image}
          />
          <img
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn%3AANd9GcSZt9hE0m8MIxnPgvDyLT7U40a2mOz-4HqJhLpXwZsQ_Qj5d05c&usqp=CAU"
            className={classes.about_image}
          />
        </div>
        <div
          className={`${
            hover_section === "companyPitch" && classes.section_highlight
          } 
            ${hover_section === "companyPitch" && classes.company_pitch} 
            ${classes.about_content}`}
        >
          <Text
            size={9}
            color={color.primary_palette.black}
            fontWeight={300}
            family="gillsans_light"
          >
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam
            congue mi ac consequat convallis. Etiam sed pellentesque dolor.
            Maecenas a mauris non orci bibendum aliquam. Mauris orci erat,
            fringilla non velit ac, vulputate posuere erat. Etiam elementum,
            erat a hendrerit volutpat, augue purus sollicitudin lectus, in
            pretium purus nulla id lectus. Nulla laoreet tempus tellus, a
            vestibulum odio egestas pretium. Suspendisse pharetra libero mi, id
            egestas massa ullamcorper a. Vestibulum ante ipsum primis in
            faucibus orci luctus et ultrices posuere cubilia Curae;
          </Text>
          <Text
            size={9}
            color={color.form_colors.royal_purple_1}
            family="gillsans_sb"
            className={classes.company_keywords}
          >
            Company Vibe
          </Text>
          <Row>
            <Text size={8} className={classes.about_chips}>
              <span className={classes.about_chip}>Interior Design</span>
            </Text>
            <Text size={8} className={classes.about_chips}>
              <span className={classes.about_chip}>Architecture</span>
            </Text>
            <Text size={8} className={classes.about_chips}>
              <span className={classes.about_chip}>Small-Company </span>
            </Text>
          </Row>
          <Row className={classes.accolade_image_container}>
            <img
              src={"assets/images/Drake_A.jpg"}
              className={classes.accolade_images}
            />
            <img
              src={"assets/images/Drake_A.jpg"}
              className={classes.accolade_images}
            />
            <img
              src={"assets/images/Drake_A.jpg"}
              className={classes.accolade_images}
            />
            <img
              src={"assets/images/Drake_A.jpg"}
              className={classes.accolade_images}
            />
          </Row>
          <Row className={classes.height_10}>
            <div className={classes.about_end_left}>
              <Text
                size={8}
                color={color.form_colors.textfield_color}
                family="gillsans_sb"
                fontWeight={600}
                className={classes.display_inline}
              >
                Size
              </Text>
              <Text
                size={8}
                color={color.form_colors.textfield_color}
                family="gillsans_r"
                fontWeight={300}
                className={`${classes.margin_left_10} ${classes.display_inline}`}
              >
                10 - 15 employees
              </Text>
            </div>
            <div className={classes.about_end_right}>
              <Text
                size={8}
                fontWeight={600}
                color={color.form_colors.textfield_color}
                family="gillsans_sb"
                className={classes.display_inline}
              >
                Founded
              </Text>
              <Text
                size={8}
                color={color.form_colors.textfield_color}
                family="gillsans_r"
                fontWeight={300}
                className={`${classes.margin_left_10} ${classes.display_inline}`}
              >
                2002
              </Text>
            </div>
          </Row>
          <Row>
            <div className={classes.about_end_left}>
              <Text
                size={8}
                fontWeight={600}
                color={color.form_colors.textfield_color}
                family="gillsans_sb"
                className={classes.display_inline}
              >
                Venue
              </Text>
              <Text
                size={8}
                color={color.form_colors.textfield_color}
                family="gillsans_r"
                fontWeight={300}
                className={`${classes.margin_left_10} ${classes.display_inline}`}
              >
                Architecture firm
              </Text>
            </div>
            <div className={classes.about_end_right}>
              <Text
                size={8}
                fontWeight={600}
                color={color.form_colors.textfield_color}
                family="gillsans_sb"
                className={classes.display_inline}
              >
                Clientele
              </Text>
              <Text
                size={8}
                color={color.form_colors.textfield_color}
                family="gillsans_r"
                fontWeight={300}
                className={`${classes.margin_left_10} ${classes.display_inline}`}
              >
                Residential
              </Text>
            </div>
          </Row>
        </div>
      </Row>
    );
  };

  const returnWaterCoolerSection = () => {
    return (
      <>
        <div className={`${classes.water_cooler_container}`}>
          <Text
            size={12}
            fontWeight={600}
            family="gillsans_sb"
            color={color.form_colors.royal_purple_1}
            className={classes.water_cooler_title}
          >
            WATER COOLER
          </Text>
          <Row className={classes.p_top_10}>
            {waterCoolerData.map((each, index) => {
              return (
                <div className={`${classes.pos_rel} ${classes.txt_center}`}>
                  <img
                    className={`${classes.water_cooler_image}`}
                    src={`assets/images/image${index + 1}.png`}
                  />
                  <img
                    src="assets/images/playerIcon.png"
                    className={classes.player_icon}
                  />
                  <Text
                    size={8}
                    color={color.form_colors.textfield_color}
                    className={classes.txt_center}
                    family="gillsans_light"
                    fontWeight="normal"
                  >
                    {each.name}
                  </Text>
                  <Text
                    size={8}
                    color={color.form_colors.textfield_color}
                    className={classes.txt_center}
                    fontWeight={300}
                    family="gillsans_r"
                  >
                    {each.profession}
                  </Text>
                  <Text
                    size={7}
                    color={color.greyish_brown}
                    className={`${classes.water_cooler_block}`}
                    family="gillsans_light"
                  >
                    {each.description}
                  </Text>
                </div>
              );
            })}
          </Row>
        </div>
      </>
    );
  };

  const returnOurOfficesSection = () => {
    return (
      <>
        <div
          className={`${
            hover_section === "ourOffices" &&
            `${classes.section_highlight} ${classes.our_offices}`
          } 
          ${classes.our_offices_containter} ${classes.margin_left_10}`}
        >
          <Text
            size={12}
            fontWeight={600}
            family="gillsans_sb"
            color={color.form_colors.royal_purple_1}
            className={classes.water_cooler_title}
          >
            OUR OFFICES
          </Text>
          <Row className={`${classes.city_tabs}`}>
            {officeTabs.map((tab, index) => {
              return (
                <Text
                  size={8}
                  fontWeight={index === 0 ? 600 : 300}
                  color={color.primary_palette.black}
                  family={index === 0 ? "gillsans_r" : "gillsans_light"}
                  className={` ${classes.city_tab}`}
                >
                  {tab}
                </Text>
              );
            })}
          </Row>
          <Row className={classes.our_offices_images}>
            <Row className={classes.our_offices_image_row}>
              <ChevronDownIcon className={classes.left_arrow_corosel} />
              <img
                src="assets/images/outsourcing.jpg"
                className={classes.image1}
              />
              <div className={classes.column}>
                <img
                  src="assets/images/Starry Sky - Blank Cover.png"
                  className={classes.image2}
                />
                <img
                  src="assets/images/Starry Sky - Blank Cover.png"
                  className={classes.image3}
                />
              </div>
              <ChevronDownIcon className={classes.right_arrow_corosel} />
            </Row>
          </Row>
        </div>
      </>
    );
  };

  const returnCompanyVibeSection = () => {
    const companyVibesRow1 = [
      "Flexible",
      "Challenging",
      "Collaborative",
      "Fun",
    ];
    const companyVibesRow2 = [
      "Practical",
      "Goal-Oriented",
      "Meritocracy",
      "Creative",
    ];

    return (
      <div
        className={`${
          hover_section === "companyPitch" &&
          `${classes.section_highlight} ${classes.company_pitch}`
        }   ${classes.company_vibes_container}`}
      >
        <Text
          size={13}
          color={color.secondary_palette.purples.franklin_purple}
          family="gillsans_r"
          className={classes.company_vibe_title}
        >
          COMPANY VIBE
        </Text>
        <Row className={classes.company_vibe_row1}>
          {map(companyVibesRow1, (vibe) => {
            return (
              <Text
                size={10}
                fontWeight={600}
                color={color.secondary_palette.grays.sonic_silver}
                family="gillsans_sb"
                className={classes.row_one_items}
              >
                {vibe}
              </Text>
            );
          })}
        </Row>
        <Row className={classes.company_vibe_row2}>
          {map(companyVibesRow2, (vibe) => {
            return (
              <Text
                size={10}
                fontWeight={600}
                color={color.secondary_palette.grays.sonic_silver}
                family="gillsans_r"
                className={classes.row_two_items}
              >
                {vibe}
              </Text>
            );
          })}
        </Row>
      </div>
    );
  };

  const returnOurCulture = () => {
    return (
      <div
        className={
          hover_section === "companyHighlights" &&
          `${classes.our_culture_section} ${classes.section_highlight} ${classes.company_culture}`
        }
      >
        <Text
          size={12}
          fontWeight={600}
          family="gillsans_sb"
          color={color.form_colors.royal_purple_1}
          className={classes.our_culture_title}
        >
          OUR CULTURE
        </Text>
        <div className={classes.our_culture_container}>
          <Row className={classes.layout1}>
            <div className={classes.layout1_image_container}>
              <img
                className={classes.layout1_image}
                src="https://www.thepaws.net/wp-content/uploads/2018/11/shih-tzu-poodle-mix-crossbreed-18.jpg"
              />
            </div>
            <div className={classes.layout1_content}>
              <div>
                <Text
                  size={11}
                  fontWeight={600}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_r"
                  className={classes.row_two_items}
                >
                  One-of-kind Service{" "}
                </Text>
                <Text
                  size={8}
                  fontWeight={300}
                  color={color.form_colors.textfield_color}
                  family="gillsans_light"
                >
                  What sets PM3 apart from other agencies is the company’s
                  ability to create emotional connections through its
                  campaigns—because PM3 specializes in advertising to Hispanic
                  and multicultural markets, they have the benefit of truly
                  understanding their audience. The agency uses data to gather
                  insights on the customer’s specific needs, and pairs that with
                  creative expertise to make emotionally resonant campaigns.
                </Text>
              </div>
              <div className={classes.layout1_con_section2}>
                <Text
                  size={11}
                  fontWeight={600}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_r"
                  className={classes.row_two_items}
                >
                  Winning In & Out Of the Office
                </Text>
                <Text
                  size={8}
                  fontWeight={300}
                  color={color.form_colors.textfield_color}
                  family="gillsans_light"
                >
                  Even though the team is always working hard to create awesome
                  campaigns, they know how to have fun, too. Anytime a
                  successful campaign is launched, the team celebrates by
                  grabbing margaritas at the cantina next door to the office.
                  The team also kicks back by participating in an inter-office
                  Atlanta soccer league—of which they’re the reigning Atlanta
                  champs.
                </Text>
              </div>
            </div>
          </Row>
          <Row className={classes.layout2}>
            <div className={classes.layout2_content}>
              <div>
                <Text
                  size={11}
                  fontWeight={600}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_r"
                  className={classes.row_two_items}
                >
                  Advertising's United Nations{" "}
                </Text>
                <Text
                  size={8}
                  fontWeight={300}
                  color={color.form_colors.textfield_color}
                  family="gillsans_light"
                >
                  With people from Chile, Mexico, Spain, and the majority of
                  Latin and South American countries represented, the team
                  affectionately refers to the office as the United Nations.
                  Their diverse backgrounds allow for a unique understanding of
                  the Hispanic and multicultural segment —and make for an
                  incredibly collaborative and fun team.
                </Text>
              </div>
            </div>
            <div className={classes.layout2_image}>
              <img
                className={classes.layout1_image}
                src="https://encrypted-tbn0.gstatic.com/images?q=tbn%3AANd9GcRNjEZSwwSZOAEaW3itaSyxI8MDDebYcRqQaDvQ_WLY-0VWxS9L&usqp=CAU"
              />
            </div>
          </Row>
        </div>
      </div>
    );
  };

  const returnConnectSection = () => {
    const currentTeam = [
      { image: "", name: "Elizabeth Ames", role: "Managing Partner" },
      { image: "", name: "Roger Adams", role: "Senior Partner" },
      { image: "", name: "Link Salas", role: "Senior Creative Director" },
      { image: "", name: "Alex Ramsy", role: "Chief Financial Officer" },
    ];
    const alumniTeam = [
      { image: "", name: "Fred Nicolaus", role: "Editor" },
      { image: "", name: "Thomas Klay", role: "President" },
      { image: "", name: "Sabri Altel", role: "Vice president" },
      { image: "", name: "Gina Sanchez", role: "Project Manager" },
    ];
    return (
      <div
        className={
          hover_section === "currentTeam" &&
          `${classes.our_culture_section} ${classes.section_highlight} ${classes.team_alumni}`
        }
      >
        <Text
          size={12}
          fontWeight={600}
          family="gillsans_r"
          color={color.form_colors.royal_purple_1}
          className={classes.our_culture_title}
        >
          CONNECT
        </Text>
        <div className={classes.connect_background}>
          <div className={classes.current_team_row}>
            <Text
              size={10}
              fontWeight={800}
              color={color.secondary_palette.purples.franklin_purple}
              className={`${classes.connect_titles} ${classes.connect_margin}`}
            >
              Current Team
            </Text>
            <Row>
              {map(currentTeam, (member, index) => {
                return (
                  <div className={classes.connect_block}>
                    <div className={classes.connect_image}>
                      <img
                        className={classes.connect_image}
                        src={`assets/images/team${index + 1}.jpg`}
                      />
                    </div>
                    <Text
                      size={9}
                      color={color.form_colors.textfield_color}
                      fontWeight={600}
                      className={classes.txt_center}
                    >
                      {member.name}
                    </Text>
                    <Text
                      size={8}
                      color={color.form_colors.textfield_color}
                      className={classes.txt_center}
                      fontWeight={300}
                    >
                      {member.role}
                    </Text>
                  </div>
                );
              })}
            </Row>
          </div>
          <div className={classes.current_team_row}>
            <Text
              size={10}
              fontWeight={800}
              color={color.secondary_palette.purples.franklin_purple}
              className={`${classes.connect_titles} ${classes.connect_margin}`}
            >
              Ames Adams Architects Alumni
            </Text>
            <Row>
              {map(alumniTeam, (member, index) => {
                return (
                  <div className={classes.connect_block}>
                    <div className={classes.connect_image}>
                      <img
                        className={classes.connect_image}
                        src={`assets/images/image${index + 1}.png`}
                      />
                    </div>
                    <Text
                      size={9}
                      fontWeight={600}
                      color={color.form_colors.textfield_color}
                      className={classes.txt_center}
                    >
                      {member.name}
                    </Text>
                    <Text
                      size={8}
                      fontWeight={300}
                      color={color.form_colors.textfield_color}
                      className={classes.txt_center}
                    >
                      {member.role}
                    </Text>
                  </div>
                );
              })}
            </Row>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={classes.container}>
        {/* SINCE STATIC IMAGE ASSET IS NOT AVAILABLE USED THIS URL FOR IMAGE REFERENCE */}
        <img
          className={`${
            hover_section === "logoAndCover" &&
            `${classes.section_highlight_profile} ${classes.logo_cover}`
          } ${classes.profile_image}`}
          src="assets/images/Pchase.jpg"
        />
        <Row>
          <img
            className={`${
              hover_section === "logoAndCover" &&
              `${classes.section_highlight} ${classes.logo_cover}`
            } ${classes.cover_image}`}
            src="assets/images/cover_img.jpg"
          />
        </Row>
        <Row className={classes.section_2}>
          <div className={classes.profile_image_section}></div>
          <div
            className={`${
              hover_section === "companyPitch" &&
              `${classes.section_highlight} ${classes.company_pitch}`
            } ${classes.section_2_profession}`}
          >
            <Text
              size={18}
              family="gillsans_sb"
              fontWeight={600}
              color={color.form_colors.royal_purple_1}
            >
              Ames | Adams Achitects
            </Text>
            <Text
              size={9}
              fontWeight={300}
              color={color.form_colors.textfield_color}
              family="gillsans_light"
              className={classes.step1_subheading}
            >
              From Creative Concepts to Better Living
            </Text>
          </div>
          <div className={`${classes.section_2_location}`}>
            <Text
              size={9}
              fontWeight={300}
              color={color.primary_palette.black}
              family="gillsans_light"
            >
              <span
                className={`${
                  hover_section === "companyPitch" &&
                  `${classes.section_highlight} ${classes.company_pitch}`
                }`}
              >
                Architecture, Interior Design
              </span>
            </Text>
            <Text
              size={9}
              fontWeight={300}
              color={color.primary_palette.black}
              family="gillsans_light"
              className={`${classes.step1_subheading} `}
            >
              <span
                className={`${
                  hover_section === "ourOffices" &&
                  `${classes.section_highlight} ${classes.our_offices}`
                }`}
              >
                New York | London | Paris
              </span>
            </Text>
          </div>
        </Row>
        <div className={classes.profile_section_left}>
          {/* PROFILE TABS */}
          <Row>
            {tabs.map((tab, index) => {
              return (
                <Text
                  size={10}
                  fontWeight={600}
                  color={color.secondary_palette.grays.shadow_gray}
                  family="gillsans_sb"
                  className={`${
                    index === 0 &&
                    `${classes.tabs_1_margin} ${classes.border_bottom_about}`
                  } ${classes.section_tab}`}
                >
                  <span className={index === 0 && classes.tab_highlight}>
                    {tab}
                  </span>
                </Text>
              );
            })}
          </Row>
          {/* ABOUT CONTENT */}
          {returnAboutSection()}

          {/* WATER COOLER SECTION */}
          <div
            className={`${
              hover_section === "waterCooler" &&
              `${classes.section_highlight} ${classes.overflow_hidden} ${classes.margin_left_10} ${classes.water_cooler}`
            } ${classes.water_cool_sec}`}
          >
            {returnWaterCoolerSection()}
            <Text
              size={9}
              color={color.kettleman}
              className={`${classes.water_cooler_more}`}
            >
              <img
                src="assets/icons/filled_add.svg"
                className={classes.showmore_add_icon}
              />
              SHOW MORE
            </Text>
          </div>
          {returnOurOfficesSection()}
        </div>
        {/* RIGHT SECTIONS */}
        <div className={classes.profile_section_right}>
          <div
            className={
              hover_section === "openPosition"
                ? classes.section_highlight
                : classes.margin_bottom_10
            }
          >
            <div className={classes.open_positions}>
              <Text
                size={12}
                color={color.form_colors.royal_purple_1}
                fontWeight={600}
                family="gillsans_r"
                className={classes.open_pos_title}
              >
                Open Positions
              </Text>
              {returnOpenPositionSection()}
            </div>
          </div>
          <div
            className={
              hover_section === "franklinReport"
                ? classes.section_highlight
                : classes.margin_bottom_10
            }
          >
            <div className={classes.franklin_report_card}>
              <Text
                size={12}
                color={color.secondary_palette.purples.franklin_purple}
                family="gillsans_sb"
                fontWeight={600}
                className={classes.section_titles}
              >
                Franklin Report Card
              </Text>
              <div className={classes.franklin_content}>
                {returnFranklinReportSection()}
                <Text
                  size={9}
                  color={color.secondary_palette.purples.franklin_purple}
                  family="gillsans_r"
                  fontWeight={600}
                  className={classes.txt_right}
                >
                  <span className={classes.full_report}>See Full Report</span>
                </Text>
              </div>
            </div>
          </div>
          <div
            className={
              hover_section === "ourOffices"
                ? `${classes.section_highlight} ${classes.our_offices}`
                : classes.margin_bottom_10
            }
          >
            <div className={classes.contact_us}>
              <Text
                size={12}
                color={color.secondary_palette.purples.franklin_purple}
                family="gillsans_sb"
                fontWeight={600}
                className={classes.section_titles}
              >
                Contact Us
              </Text>
              {returnContactUsContent()}
            </div>
          </div>
          <div
            className={
              hover_section === "companyPitch"
                ? classes.section_highlight
                : classes.margin_bottom_10
            }
          >
            <div className={classes.company_feeds}>
              <Text
                size={12}
                color={color.secondary_palette.purples.franklin_purple}
                family="gillsans_sb"
                fontWeight={600}
                className={classes.section_titles}
              >
                Company Feeds
              </Text>
              {returnCompanyFeedback()}
            </div>
          </div>
        </div>
        {returnCompanyVibeSection()}
        {returnOurCulture()}
        {returnConnectSection()}
      </div>
    </>
  );
}

export default styleSheet(StaticProfile);
