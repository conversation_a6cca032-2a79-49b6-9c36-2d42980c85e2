import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  get,
  find,
  map,
  reverse,
  range,
  filter,
  omit,
  every,
  debounce,
} from "lodash";
import { useHistory } from "react-router-dom";
import uuidv4 from "uuid/v4";
import {
  FormControl,
  Select,
  Chip,
  Input,
  Checkbox,
  MenuItem,
  FormControlLabel,
} from "@material-ui/core";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import CWStyles from "./styles/CW_step2_styles";
import Lil_Plus_filled from "../../components/data_display/icons/Lil_Plus_filled";
import Element_Required_Icon from "../../components/data_display/icons/ElementRequiered";
import Styled_cross from "../data_display/icons/styled_cross";
import Close from "../../components/data_display/icons/Close";
import CustomInputCount from "../../components/inputs/custom_input_count";
import CustomTextArea from "../../components/inputs/custom_text_area";
import CustomButton from "../navigations/custom_buttons";
import CustomLink from "../navigations/custom_links";
import CustomCheckbox from "../inputs/custom_checkbox";
import CustomSelect from "../inputs/custom_select";
import CustomSelectOpen from "../inputs/custom_select_open";
import CustomTextFields from "../inputs/custom_textfields";
import CustomAutocomplete from "../inputs/custom_auto_complete";
import CustomScrollbars from "../data_display/custom_scroll";
import Twitter_Outline from "../data_display/icons/Twitter_Outline";
import Twitter_Color from "../data_display/icons/Twitter_Colored";
import FaceBook_Outline_Icon from "../data_display/icons/Facebook_Outline";
import FaceBook_Color from "../data_display/icons/facebook_colored";
import LinkedIn_Color from "../data_display/icons/linkedIn_colored";
import Instagram_Color from "../data_display/icons/Instagram_colored_pink";
import Linkedin_Outline_Icon from "../data_display/icons/Linkedin_Outline";
import Youtube_Outline_Icon from "../data_display/icons/youtube_outline";
import Instagram_Outline_Icon from "../data_display/icons/Instagram_Outline";
import Youtube_Color from "../data_display/icons/youtube_colored";
import Chevron_Down_Icon from "../data_display/icons/Arrow_Chevron_Down";
import AddImageLogo from "./add_image_logo";
import { getGroupedArray } from "../../utilities/utils";
import { color, pxToRem } from "../../utilities/themes";
import {
  PROFILE_TYPES,
  PUBLIC_PROFILE,
  INSTAGRAM_AUTHENTICATION_URL,
  BASEURL,
} from "../../constants";
import { ProfileActions, ConfigActions } from "../../redux/actions";
import CustomTextField from "../inputs/custom_textfields";
import NewCompanyConfirm from "../modals/new_company_confirm_modal";
import { profileStepperApi } from "../../redux/api";
import { useCallback } from "react";
import AddImageHoverLogo from "./add_image_hover_logo";
import { WEBSITE_URL } from "../../validations/validations";

function CompanyWizard(props) {
  const { classes, handleNext } = props;
  const history = useHistory();
  /**  STATE DATA  */
  const [values, setValues] = useState({
    initialValues: {
      companyAccolades: [],
      companySnapShot: "",
      companyWebsiteUrl: "",
      isPublicDisplay: true,
    },
    errors: {},
    companySnapShotFocus: false,
    clienteleCountVal: "",
    toggleNewCompanyModal: false,
    showAccoError: false,
    collegeAllegiancesList: [],
  });
  const [collegeAllegiancesList, setCollegeAllegiancesList] = useState([]);
  const [tradeworksUrl, setTradeworksUrl] = useState("");
  const [clickedCopy, setClickedCopy] = useState(false);
  /** REDUX DATA  */
  const allTrades = useSelector((state) => state.Profile.trades);
  const [groupTrades, setGroupTrades] = useState([]);
  const collegeAllegiances = useSelector(
    (state) => state.Profile.collegeAllegiances
  );
  const companyVibes = useSelector((state) => state.Profile.companyVibes);
  const clientele = useSelector((state) => state.Profile.clientele);
  const designProclivities = useSelector((state) => state.Profile.proclivities);
  const Configs = useSelector((state) => state.Configs.config);
  const accolades = useSelector((state) => state.Profile.accolades);
  const companyPitch1 = useSelector((state) => state.Profile.companypitch);
  const venues = useSelector((state) => state.Profile.venues);
  const companyInfo = useSelector((state) => state.Profile.companyinfo);
  const configsData = useSelector((state) => state.Configs.config);
  const debounceLoadData = useCallback(
    debounce(handleInstitutesData, 1000),
    []
  );

  const dispatch = useDispatch();
  const fetchData = () => {
    setInitialColleges();
    dispatch(ProfileActions.fetchAllTrades());
    dispatch(
      ProfileActions.getCompanyProfileData([PROFILE_TYPES.COMPANY_PITCH])
    );
    // dispatch(ProfileActions.getProfileDataOf([PROFILE_TYPES.COMPANY_PITCH]));
    dispatch(ConfigActions.getConfigs());
    dispatch(
      ProfileActions.fetchCollegeAllegiances({
        name: "",
      })
    );
    dispatch(ProfileActions.fetchCompanyVibe());
    dispatch(ProfileActions.fetchAccolades());
    dispatch(ProfileActions.fetchClienteleData());
    dispatch(ProfileActions.fetchProclivitiesData());
    dispatch(ProfileActions.fetchVenue());
  };

  const setInitialData = () => {
    if (companyPitch1) {
      const formData = { ...companyPitch1 };
      const { companyAccolades, trades } = formData;
      if (companyAccolades) {
        formData.companyAccolades = map(
          formData.companyAccolades,
          (eachAccolade) => {
            return {
              id: eachAccolade.accoladesId || eachAccolade._id,
              year: eachAccolade.year,
              logo: eachAccolade.logo,
              name: eachAccolade.name,
              isUserCreated: eachAccolade.isUserCreated,
            };
          }
        );
      } else {
        formData.companyAccolades = [];
      }
      if (get(trades, "length", 0) === 0) {
        /**
         * TO MATCH UI DESIGN
         * IF NO TRADES
         * ADDING TWO EMPTY TRADE SECTIONS
         */
        setValues({
          ...values,
          initialValues: {
            ...values.initialValues,
            trades: [{ _id: uuidv4() }, { _id: uuidv4() }],
            isPublicDisplay: true,
          },
        });
      } else if (get(trades, "length", 0) % 2 !== 0) {
        /**
         * TO MATCH UI DESIGN
         * IF LENGTH OF TRADES IS ODD
         * ADDING ONE EMPTY TRADE SECTION
         */
        formData.trades = [...formData.trades, { _id: uuidv4() }];
      }
      if (!trades) {
        formData.trades = [{ _id: uuidv4() }, { _id: uuidv4() }];
      }
      // FORMATTING LOGOS TO REQUIRED STRUCTURE
      // if (formData.employees) {
      formData.employees = {
        name: formData.employees ? formData.employees : "1-5 Employees",
      };
      // }

      if (formData.companyLogo) {
        formData.companyLogo = { image: formData.companyLogo };
      }
      if (formData.coverImage) {
        formData.coverImage = { image: formData.coverImage };
      }
      if (formData.topFeatureImage1) {
        formData.topFeatureImage1 = { image: formData.topFeatureImage1 };
      }
      if (formData.topFeatureImage2) {
        formData.topFeatureImage2 = { image: formData.topFeatureImage2 };
      }
      if (formData.venueInfo && formData.venueInfo.length) {
        formData.venue = {
          _id: formData.venueInfo[0].id,
          name: formData.venueInfo[0].name,
        };
      }
      if (formData.collegeAllegiances) {
        formData.collegeAllegiances = map(
          formData.collegeAllegiances,
          (allegancy) => {
            return {
              id: allegancy.allegiancesId || allegancy._id,
              name: allegancy.name,
              logo: allegancy.logo,
              isUserCreated: allegancy.isUserCreated || false,
            };
          }
        );
      }
      setValues({
        ...values,
        initialValues: { ...formData },
      });
    }
  };

  // Method responsible to return categorized menu items
  const renderCategorizedMenu = (menuData, sectionId, value, idx) => {
    // list of color codes to be appied for category tiles based on index
    const colorCodes = [
      "#69986c",
      "#e2c236",
      "#5f94e0",
      "#9c7fb0",
      "#c8dbf1",

      "#69986c",
      "#e2c236",
      "#5f94e0",
      "#9c7fb0",
      "#c8dbf1",
    ];
    // fetching category names
    const groups = Object.keys(menuData)
      .filter((each) => each !== "noParent")
      .sort();
    // pushing nonParent items to array
    let menuItems = menuData.noParent
      ? menuData.noParent.map((each) => each)
      : [];

    groups.map((eachGroup, index) => {
      // interating group titles and pushint to menu item array
      let subGroupItems = [{ name: eachGroup, color: colorCodes[index] }];
      menuData[eachGroup].map((eachGroupItem) => {
        // iterating subgroup items
        subGroupItems.push(eachGroupItem);
      });
      // concatinating menuItems which are returned and categorized items
      menuItems = [...menuItems, ...subGroupItems];
      return null;
    });

    // mapping menuItems (objects) and returning JSX
    return menuItems.map((eachItem) => {
      return (
        <MenuItem
          style={{ background: eachItem.color }}
          onClick={
            !eachItem.color &&
            handleTradeSelect(
              sectionId,
              {
                ...eachItem,
                tradeId: eachItem._id,
              },
              idx
            )
          }
          className={eachItem.color && classes.textCapital}
        >
          {eachItem.name}
        </MenuItem>
      );
    });
  };
  //API
  useEffect(() => {
    window.scrollTo(0, 0);
    fetchData();
    if (localStorage.getItem("isNewCompany") === "true") {
      toggleCompanyModal();
    } else {
    }
  }, []);

  const setInitialColleges = async () => {
    let collegesList = [];
    const res = await profileStepperApi.fetchCollegeAllegiances({
      name: "",
    });
    const { data } = res;
    collegesList = data.data || collegesList;
    setCollegeAllegiancesList(collegesList);
    // setValues({
    //   ...values,
    //   collegeAllegiancesList: collegesList,
    // });
  };

  useEffect(() => {
    if (allTrades && allTrades.length) {
      setGroupTrades(getGroupedArray(allTrades, "parent"));
    }
  }, [allTrades]);

  useEffect(() => {
    if (companyPitch1) {
      setInitialData();
    }
  }, [companyPitch1]);

  const checkValidation = (name, value) => {
    // VALIDATION CHECK FOR REQUIRED FIELDS
    const { errors } = values;
    if (name === "companySnapShot") {
      if (!value) {
        errors.companySnapShot = { error: true, message: "Required Field" };
        setValues({ values: { ...values, errors } });
        return;
      }
      errors.companySnapShot = { error: false, message: "" };
      setValues({ values: { ...values, errors } });
      return;
    }
    if (name === "companyWebsiteUrl") {
      if (!WEBSITE_URL.test(value) && value) {
        errors.companyWebsiteUrl = { error: true, message: "Enter Valid Url" };
        setValues({ values: { ...values, errors } });
        return;
      }
      errors.companyWebsiteUrl = { error: false, message: "" };
      setValues({ values: { ...values, errors } });
      return;
    }
    if (name === "companyPitch") {
      if (!value || value.length < 60) {
        errors.companyPitch = { error: true, message: "Minimum 60 characters" };
        setValues({ values: { ...values, errors } });
        return;
      }
      errors.companyPitch = { error: false, message: "" };
      setValues({ values: { ...values, errors } });
      return;
    }
    if (name === "trades") {
      if (!value || value.name === "Select") {
        errors.trades = { error: true, message: "Required Field" };
        setValues({ values: { ...values, errors } });
        return;
      }
      errors.trades = { error: false, message: "" };
      setValues({ values: { ...values, errors } });
    }
  };

  const getInputCount = (e) => {
    const { errors } = values;
    errors.companyPitch = { error: false, message: "" };
    setValues({ ...values, errors, clienteleCountVal: e.target.value });
  };

  const toggleCompanyModal = () => {
    setValues({ ...values, toggleNewCompanyModal: !toggleNewCompanyModal });
  };

  const handleInputChange = (name, value, del) => (e) => {
    if (name === "clientele") {
      const { initialValues } = values;
      const duplicate = find(initialValues[name], {
        clientelId: value.clientelId,
      });
      if (duplicate) {
        if (!value.delete) {
          return;
        }
        initialValues[name] = filter(initialValues[name], (each) => {
          return each.clientelId !== value.clientelId;
        });
        setValues({ ...values });
      } else {
        initialValues[name] = initialValues[name] || [];
        initialValues[name] = [...initialValues[name], { ...value }];
        setValues({ ...values });
      }
      return;
    }
    if (name === "designProclivities") {
      const { initialValues } = values;
      const duplicate = find(initialValues[name], {
        designId: value.designId,
      });
      if (duplicate) {
        if (!value.delete) {
          return;
        }
        initialValues[name] = filter(initialValues[name], (each) => {
          return each.designId !== value.designId;
        });
        setValues({ ...values });
      } else {
        initialValues[name] = initialValues[name] || [];
        initialValues[name] = [...initialValues[name], { ...value }];
        setValues({ ...values });
      }
      return;
    }
    if (name === "collegeAllegiances") {
      const { initialValues } = values;
      const duplicate = find(initialValues[name], {
        id: value.id,
      });
      if (duplicate) {
        if (!del) {
          return;
        }
        initialValues[name] = filter(initialValues[name], (each) => {
          return each.id !== value.id;
        });
        setValues({ ...values });
      } else {
        initialValues[name] = initialValues[name] || [];
        initialValues[name] = [...initialValues[name], { ...value }];
        setValues({ ...values });
      }
      return;
    }
    if (name === "yearEstablished") {
      values.initialValues[name] = e.target.value;
      setValues({ ...values });
      return;
    }
    values.initialValues[name] =
      value || e.target.checked || e.target.value.trim();
    if (
      name === "companySnapShot" ||
      name === "companyPitch" ||
      name === "companyWebsiteUrl"
    ) {
      checkValidation(name, e.target.value || value);
    }
    setValues({ ...values });
  };

  /**this method handle i3 dropdown change of company vibes section */
  const handleCompanyVibeChange = (name, value, deleteVibe) => (e) => {
    const { initialValues } = values;
    let foundRecord = {};
    /**
     * checking the incoming value is existing or not
     */
    if (value.isUserCreated) {
      /**condition if value is custom input(create by user) */
      foundRecord = find(initialValues[name], { _id: value._id });
    } else {
      /**condition if value is default provided */
      if (value.vibeId) {
        foundRecord = find(initialValues[name], { vibeId: value.vibeId });
      } else {
        foundRecord = find(initialValues[name], { vibeId: value._id });
      }
    }
    if (foundRecord) {
      /**if record found */
      if (!deleteVibe) {
        /**if it is not a delete action return null */
        return;
      }
      /**if it is a delete action return updated data(filter incoming value from existing data) */

      if (value.isUserCreated) {
        initialValues[name] = filter(initialValues[name], (each) => {
          return each._id !== value._id;
        });
      } else {
        initialValues[name] = filter(initialValues[name], (each) => {
          return each.vibeId !== value.vibeId;
        });
      }
      setValues({ ...values, initialValues: { ...initialValues } });
    } else {
      /**if not duplicate incoming value adding incoming value to existing array  */
      initialValues[name] = initialValues[name] || [];
      initialValues[name] = [
        ...initialValues[name],
        { vibeId: value._id, name: value.name },
      ];
      setValues({ ...values, initialValues: { ...initialValues } });
    }
  };

  const imageUpload = (imageData, name) => {
    setValues({
      ...values,
      initialValues: { ...values.initialValues, [name]: imageData },
    });
  };

  const handleTradeSelect = (sectionId, value, idx) => (e) => {
    /**
     * UPDATING SELECTED TRADE ID IN THE TRADES ARRAY WITH INDEX AS REF
     */
    e.preventDefault();
    const { trades } = values.initialValues;
    const selectedTrades = map(trades, (eachTrade) => {
      return eachTrade.tradeId;
    });
    if (selectedTrades.includes(value.tradeId)) {
      // IF EXISTING TRADE SELECTED
      return;
    } else {
      const updatedTrades = map(trades, (trade) => {
        if (trade._id === sectionId) {
          return value;
        }
        idx === 0 && checkValidation("trades", value);
        return trade;
      });
      setValues({
        ...values,
        initialValues: { ...values.initialValues, trades: [...updatedTrades] },
      });
    }
  };

  async function handleInstitutesData(value) {
    let collegesList = [];
    const res = await profileStepperApi.fetchCollegeAllegiances({
      name: value,
    });
    const { data } = res;
    collegesList = data.data || collegesList;
    setCollegeAllegiancesList(collegesList);
    // setValues({
    //   ...values,
    //   collegeAllegiancesList: collegesList,
    // });
  }

  const handleCollegeInput = async (e) => {
    debounceLoadData(e.target.value);
  };

  const handleOnBlurSocialMedia = (e) => {
    const socialMediaFeeds = { ...values.initialValues.socialMediaFeeds };
    let value = e.target.value;
    const existingValue = socialMediaFeeds[e.target.name] || "";
    if (e.target.name !== "email" && e.target.name !== "website" && value) {
      if (existingValue.startsWith("/")) {
        value = value.replace("/", "");
      }
      if (existingValue.startsWith("@")) {
        value = value.replace("@", "");
      }
      if (e.target.name === "instagram" || e.target.name === "twitter") {
        value = value.startsWith("/") ? value.replace("/", "@") : value;
        value = value.startsWith("@") ? value : `@${value}`;
      } else {
        value = value.startsWith("/") ? value : `/${value}`;
      }
    }
    // socialMediaFeeds[e.target.name] = e.target.value;
    socialMediaFeeds[e.target.name] = value;
    setValues({
      ...values,
      initialValues: { ...values.initialValues, socialMediaFeeds },
    });
  };

  const checkErrors = (id, key) => {
    const { newErrors } = values;
    const foundRecord = find(newErrors, { index: id, key });
    if (foundRecord) {
      return foundRecord.error;
    }
    return false;
  };

  const finalValidation = () => {
    const { companySnapShot, companyPitch } = values.initialValues;
    let validationErrors = [];

    if (!companySnapShot) {
      validationErrors.push({
        error: true,
        key: "companySnapShot",
        index: 0,
      });
    }
    if (!companyPitch) {
      validationErrors.push({
        error: true,
        key: "companyPitch",
        index: 0,
      });
    }
    setValues({
      ...values,
      newErrors: [...validationErrors],
    });
  };

  const onCompanyPitchSubmit = () => {
    /**
     *
     * FORMATTING THE DATA TO REQUIRED FORMAT
     */
    const { initialValues } = values;
    if (checkFormErrors()) {
      finalValidation();
      // checkValidation("companyPitch", initialValues.companyPitch);
      return;
    }
    const { edit } = props;
    const dataToSubmit = {
      ...initialValues,
      trades: filter(initialValues.trades, (eachTrade) => {
        return eachTrade.name !== "Select";
      }).map((eachTrade) => {
        return eachTrade.tradeId;
      }),
      companyVibe: map(initialValues.companyVibe, (eachVibe) => {
        const newObj = { ...eachVibe, id: eachVibe.vibeId };
        const formatedVibe = omit(newObj, "_id");
        return formatedVibe;
      }),
      companyAccolades: map(initialValues.companyAccolades, (accolade) => {
        if (accolade.isUserCreated) {
          const newObj = omit(accolade, "id");
          return { ...newObj };
        }
        return { ...accolade };
      }),
      collegeAllegiances: map(initialValues.collegeAllegiances, (each) => {
        if (each.isUserCreated) {
          const newObj = omit(each, "id");
          return { ...newObj };
        }
        return { ...each };
      }),
      clientele: map(get(initialValues, "clientele", []), (each) => {
        return each.clientelId;
      }),
      designProclivities: map(
        get(initialValues, "designProclivities", []),
        (each) => {
          return each.designId;
        }
      ),
      venue: get(initialValues, "venue._id", ""),
      employees: get(initialValues, "employees.name", ""),
      isPublicDisplay: initialValues.isPublicDisplay === true,
    };
    const titleFlag = every(dataToSubmit.companyAccolades, "name");
    const logoFlag = every(dataToSubmit.companyAccolades, "logo");

    if (!(titleFlag && logoFlag)) {
      setValues({
        ...values,
        showAccoError: true,
      });
      return;
    } else {
      setValues({
        ...values,
        showAccoError: false,
      });
    }

    // const formatedData = omit(dataToSubmit, "_id");
    dispatch(
      ProfileActions.updateCompanyPitch(dataToSubmit, (res) => {
        if (!res.error) {
          if (edit) {
            history.push("twc/profile");
          } else {
            handleNext();
          }
        }
      })
    );
  };

  const handleFinishLater = () => {
    history.push("twc/profile");
  };

  const addTradeBtn = () => {
    // ADD MORE TRADES
    const { trades } = values.initialValues;
    if (!trades) {
      values.initialValues.trades = [];
    }
    setValues({
      ...values,
      initialValues: {
        ...values.initialValues,
        trades: [
          ...values.initialValues.trades,
          { _id: uuidv4() },
          { _id: uuidv4() },
        ],
      },
    });
  };

  const returnTradeLabel = (index) => {
    // RETURNING DYNAMIC TRADE LABELS
    switch (index) {
      case 0:
        return "Primary Trade";
      case 1:
        return "Secondary Trade";
      case 2:
        return "3rd Trade";
      default:
        return `${index + 1}th Trade`;
    }
  };

  const onCloseAccolade = (id) => {
    // DELETE ACCOLADE ON CLOSE
    const { companyAccolades } = values.initialValues;
    const updatedAccolades = companyAccolades.filter((each) => {
      return each.id !== id;
    });

    setValues({
      ...values,
      initialValues: {
        ...values.initialValues,
        companyAccolades: [...updatedAccolades],
      },
    });
  };

  const onAccoladeSelect = (event, value, deleteAccolade) => {
    // ADDING/DELETING SELECTED ACCOLADES
    event.preventDefault();
    event.stopPropagation();
    const { companyAccolades } = values.initialValues;
    if (!companyAccolades || !value) {
      return;
    }
    const id = value._id;
    const selectedAccoladeIds = map(companyAccolades, (eachAccolade) => {
      return eachAccolade.id;
    });
    if (selectedAccoladeIds.includes(id)) {
      if (!deleteAccolade) {
        // IF NOT DELETE FUNCTION RETURN NULL
        return;
      }
      const filteredAccolades = companyAccolades.filter((each) => {
        return each.id !== id;
      });
      setValues({
        ...values,
        initialValues: {
          ...values.initialValues,
          companyAccolades: [...filteredAccolades],
        },
      });
    } else {
      /**if new selection pushing to company accolades */
      setValues({
        ...values,
        initialValues: {
          ...values.initialValues,
          companyAccolades: [
            ...companyAccolades,
            { id: value._id, year: [], logo: value.logo, name: value.name },
          ],
        },
      });
    }
  };

  const onAllianceSelect = (event, value, deleteAlliance) => {
    event.preventDefault();
    event.stopPropagation();
    const { collegeAllegiances } = values.initialValues;
    if (!collegeAllegiances || !value) {
      return;
    }
    const id = value._id;
    const selectedAllianceIds = map(collegeAllegiances, (eachAccolade) => {
      return eachAccolade.id;
    });
    if (selectedAllianceIds.includes(id)) {
      if (!deleteAlliance) {
        // IF NOT DELETE FUNCTION RETURN NULL
        return;
      }
      const filteredAlliance = collegeAllegiances.filter((each) => {
        return each.id !== id;
      });
      setValues({
        ...values,
        initialValues: {
          ...values.initialValues,
          collegeAllegiances: [...filteredAlliance],
        },
      });
    } else {
      /**if new selection pushing to company accolades */
      setValues({
        ...values,
        initialValues: {
          ...values.initialValues,
          collegeAllegiances: [
            ...collegeAllegiances,
            { id: value._id, year: [], logo: value.logo, name: value.name },
          ],
        },
      });
    }
  };

  /**this method handles accolade field changes */
  const onAccoladeChange = (accolade, value) => (e) => {
    if (value) {
      /**responsible for handling year dropdown in accolade (for custominput and default input) */
      const { year } = accolade;
      if (year.includes(value)) {
        accolade.year = filter(accolade.year, (each) => {
          return each !== value;
        });

        setValues({ ...values });
      } else {
        accolade.year = [...year, value];
        setValues({ ...values, showAccoError: false });
      }
    } else {
      /**handle plain input fields other than dropdowns in accolade(custom input) */
      const { name, value } = e.target;
      accolade[name] = value;
      setValues({ ...values, showAccoError: false });
    }
  };

  /**this method is responsible for handling image upload
   * for custom uploaded accolades
   */
  const handleCustomImageUpload = async (data, name) => {
    if (name === "allegiancies") {
      const { collegeAllegiances } = values.initialValues;
      const { imageFile, id } = data;
      dispatch(
        ProfileActions.createAzureBlob(imageFile, (res) => {
          const allegancy = find(collegeAllegiances, { id });
          allegancy.logo = get(res, "data[0]", "");
          setValues({ ...values });
        })
      );
    }
    if (name === "accolade") {
      const { companyAccolades } = values.initialValues;
      const { imageFile, id } = data;
      dispatch(
        ProfileActions.createAzureBlob(imageFile, (res) => {
          const accolade = find(companyAccolades, { id });
          accolade.logo = get(res, "data[0]", "");
          setValues({ ...values });
        })
      );
    }
  };

  const onClearAccoladeInp = (accolade, nameVal) => {
    accolade[nameVal] = "";
    setValues({ ...values });
  };

  /**this method is responsible for returning selected accolades(custom accolades and pre defined) */
  const returnAccoladeSections = (yearsFrom) => {
    const { companyAccolades } = values.initialValues;
    if (companyAccolades && companyAccolades.length > 0 && accolades) {
      return (
        <div className={classes.padding_top_15}>
          {map(companyAccolades, (eachAccolade) => {
            if (eachAccolade.isUserCreated) {
              /**this case handles user created i.e (user defined/custom defined) accolades */
              return (
                <div className={classes.accolade_container1}>
                  {eachAccolade.isNew && (
                    <Text
                      size={14}
                      color={color.primary_palette.franklin_purple}
                      family="gillsans_sb"
                    >
                      ADD YOUR OWN
                    </Text>
                  )}
                  <Row style={{ position: "relative" }}>
                    <img
                      src="assets/images/Purple Trash Can.svg"
                      onClick={() => onCloseAccolade(eachAccolade.id)}
                      // className={classes.accolade_container_close}
                      style={{
                        marginLeft: "3px",
                        position: "absolute",
                        right: "0",
                        cursor: "pointer",
                      }}
                    />
                    <Element_Required_Icon
                      className={classes.smallPurpleIcon}
                    />
                    <AddImageLogo
                      defaultImage={
                        get(eachAccolade, "logo", "") ||
                        "assets/images/add_2.PNG"
                      }
                      imageUpload={imageUpload}
                      deleteIcon
                      type="accolade"
                      edit={edit}
                      name="accolade"
                      cropShape="square"
                      className={classes.accolade_container_logo_default}
                      id={eachAccolade.id}
                      imageUpload={handleCustomImageUpload}
                    />
                    {/* <Close
                      onClick={() => onCloseAccolade(eachAccolade.id)}
                      className={classes.accolade_container_close}
                      style={{ marginLeft: "3px" }}
                    /> */}
                    {/* {!eachAccolade.logo && (
                      <div style={{ width: "70px" }}>
                        <Text
                          size={14}
                          color={color.secondary_palette.grays.shadow_gray}
                          family="gillsans_light"
                          className={classes.custom_accolade_txt}
                        >
                          Add logo of Award or Publication
                        </Text>
                      </div>
                    )} */}

                    <div className={classes.custom_accolade_fields_large}>
                      <Row style={{ position: "relative", bottom: pxToRem(5) }}>
                        <Text
                          size={14}
                          color={color.primary_palette.black}
                          family="gillsans_r"
                          className={classes.accoladeHeadingTxt}
                        >
                          <Element_Required_Icon
                            className={classes.smallPurpleIconTitle}
                          />
                          Title:
                        </Text>
                        <div style={{ position: "relative" }}>
                          <CustomInputCount
                            onBlur={onAccoladeChange(eachAccolade)}
                            placeholder="Title"
                            defaultValue={eachAccolade.name}
                            name="name"
                            style={{ width: "242px" }}
                          />
                          <Close
                            onClick={() =>
                              onClearAccoladeInp(eachAccolade, "name")
                            }
                            className={classes.clear_accolade_inp}
                          />
                        </div>
                        <div className={classes.accolade_container_years}>
                          <CustomSelectOpen
                            IconComponent={Chevron_Down_Icon}
                            id="demo-mutiple-checkbox"
                            className={` ${
                              classes.accolade_container_dropdown_year
                            } ${
                              eachAccolade.year.length === 0 &&
                              classes.placeholder
                            }`}
                            input={<Input />}
                            renderValue={(selected) =>
                              selected.join(", ") || "Year"
                            }
                            multiple
                            value={eachAccolade.year}
                          >
                            <CustomScrollbars className={classes.acco_scroll}>
                              {yearsFrom &&
                                map(yearsFrom, (year) => {
                                  return (
                                    <MenuItem value={year}>
                                      <Checkbox
                                        onChange={onAccoladeChange(
                                          eachAccolade,
                                          year
                                        )}
                                        checked={eachAccolade.year.includes(
                                          year
                                        )}
                                      />
                                      <Text>{year}</Text>
                                    </MenuItem>
                                  );
                                })}
                            </CustomScrollbars>
                          </CustomSelectOpen>
                        </div>
                      </Row>
                      <Row style={{ position: "relative", bottom: pxToRem(5) }}>
                        <Text
                          size={14}
                          color={color.primary_palette.black}
                          family="gillsans_r"
                          className={classes.accoladeHeadingTxt}
                        >
                          Subtitle:
                        </Text>
                        <div style={{ position: "relative" }}>
                          <CustomInputCount
                            onBlur={onAccoladeChange(eachAccolade)}
                            placeholder="Subtitle"
                            defaultValue={eachAccolade.subtitle}
                            name="subtitle"
                          />
                          <Close
                            onClick={() =>
                              onClearAccoladeInp(eachAccolade, "subtitle")
                            }
                            className={classes.clear_accolade_inp}
                          />
                        </div>
                      </Row>
                      <Row style={{ position: "relative", bottom: pxToRem(5) }}>
                        <Text
                          size={14}
                          color={color.primary_palette.black}
                          family="gillsans_r"
                          className={classes.accoladeHeadingTxt}
                        >
                          Institution:
                        </Text>
                        <div style={{ position: "relative" }}>
                          <CustomInputCount
                            onBlur={onAccoladeChange(eachAccolade)}
                            placeholder="Institution"
                            defaultValue={eachAccolade.institution}
                            name="institution"
                          />
                          <Close
                            onClick={() =>
                              onClearAccoladeInp(eachAccolade, "institution")
                            }
                            className={classes.clear_accolade_inp}
                          />
                        </div>
                      </Row>
                    </div>
                  </Row>
                </div>
              );
            } else {
              /**this case handles pre defined accolades that are selected from the dropdown */
              return (
                <div className={classes.accolade_container}>
                  <Close
                    onClick={() => onCloseAccolade(eachAccolade.id)}
                    className={`${classes.accolade_container_close} ${classes.accolade_close_2}`}
                  />
                  <img
                    src={get(eachAccolade, "logo", "")}
                    className={classes.accolade_container_logo}
                  />
                  <div className={classes.accolade_container_new}>
                    <CustomSelectOpen
                      IconComponent={Chevron_Down_Icon}
                      id="demo-mutiple-checkbox"
                      className={` ${classes.accolade_container_dropdown_new} ${
                        eachAccolade.year.length === 0
                          ? classes.placeholder
                          : "year" + eachAccolade.year.length
                      }`}
                      input={<Input />}
                      renderValue={(selected) => selected.join(", ") || "Year"}
                      multiple
                      value={eachAccolade.year}
                    >
                      <CustomScrollbars className={classes.acco_scroll}>
                        {yearsFrom &&
                          map(yearsFrom, (year) => {
                            return (
                              <MenuItem value={year}>
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      onChange={onAccoladeChange(
                                        eachAccolade,
                                        year
                                      )}
                                      checked={eachAccolade.year.includes(year)}
                                      label={year}
                                      style={{ paddingLeft: pxToRem(10) }}
                                    />
                                  }
                                  label={year}
                                />

                                {/* <Text>{year}</Text> */}
                              </MenuItem>
                            );
                          })}
                      </CustomScrollbars>
                    </CustomSelectOpen>
                  </div>
                </div>
              );
            }
          })}
        </div>
      );
    }
    return null;
  };

  const returnCompanyVibeChips = () => {
    /**
     * RETURNING SELECTED COMPANY VIBES CHIPS
     */
    return map(values.initialValues.companyVibe, (eachVibe) => (
      <Chip
        className={classes.styled_cross}
        key={eachVibe._id}
        label={eachVibe.name}
        onClick={handleCompanyVibeChange(
          "companyVibe",
          {
            ...eachVibe,
          },
          "delete"
        )}
        onDelete={handleCompanyVibeChange("companyVibe", {
          ...eachVibe,
        })}
        deleteIcon={
          <Styled_cross
            onMouseUp={handleCompanyVibeChange(
              "companyVibe",
              {
                ...eachVibe,
              },
              "delete"
            )}
          />
        }
      ></Chip>
    ));
  };

  /**this method is responsible for returning selected college allegancies options */
  const returnCollegeAllegenciesChips = () => {
    return map(values.initialValues.collegeAllegiances, (allegancy) => {
      if (allegancy.isUserCreated) {
        /**returning selected user created allegancies */
        return (
          <Row style={{ margin: "5px 0px" }}>
            <AddImageLogo
              defaultImage={
                get(
                  allegancy,
                  "logo",
                  "https://tradeworksprodsas.blob.core.windows.net/tra-con-use-dev/company/company.PNG"
                ) ||
                "https://tradeworksprodsas.blob.core.windows.net/tra-con-use-dev/company/company.PNG"
              }
              imageUpload={imageUpload}
              deleteIcon
              edit={edit}
              type="allegiancies"
              name="allegiancies"
              cropShape="square"
              className={classes.accolade_container_logo}
              id={allegancy.id}
              imageUpload={handleCustomImageUpload}
            />
            <Close
              onClick={handleInputChange(
                "collegeAllegiances",
                {
                  ...allegancy,
                  _id: allegancy.id,
                },
                "delete"
              )}
              className={classes.accolade_container_close}
            />
            <div className={classes.custom_accolade_fields}>
              <CustomInputCount
                onBlur={handleAllegancyEdit(allegancy)}
                placeholder="College Allegiance Name"
                defaultValue={allegancy.name}
                name="name"
              />
            </div>
          </Row>
        );
      } else {
        /**returning default selected allegancies */
        return (
          <Row style={{ margin: "5px 0px" }}>
            <img
              src={
                allegancy.logo ||
                "https://tradeworksprodsas.blob.core.windows.net/tra-con-use-dev/company/company.PNG"
              }
              style={{ width: "50px", height: "50px" }}
            />
            <Chip
              className={classes.styled_cross}
              key={allegancy.id}
              label={allegancy.name}
              onClick={handleInputChange(
                "collegeAllegiances",
                {
                  ...allegancy,
                  _id: allegancy.id,
                },
                "delete"
              )}
              onDelete={handleInputChange(
                "collegeAllegiances",
                {
                  ...allegancy,
                  _id: allegancy.id,
                },
                "delete"
              )}
              deleteIcon={
                <Styled_cross
                  onMouseUp={handleInputChange(
                    "collegeAllegiances",
                    {
                      ...allegancy,
                      _id: allegancy.id,
                    },
                    "delete"
                  )}
                />
              }
            />
          </Row>
        );
      }
    });
  };

  const returnClienteleChips = () => {
    /**
     * RETURNING SELECTED CLIENTELE CHIPS
     */
    return map(values.initialValues.clientele, (eachClientele) => (
      <Chip
        className={classes.styled_cross}
        key={eachClientele.clientelId}
        label={eachClientele.name}
        onClick={handleInputChange("clientele", {
          ...eachClientele,
          delete: true,
        })}
        onDelete={handleInputChange("clientele", {
          ...eachClientele,
          delete: true,
        })}
        deleteIcon={
          <Styled_cross
            onMouseUp={handleInputChange("clientele", {
              ...eachClientele,
              delete: true,
            })}
          />
        }
      ></Chip>
    ));
  };

  const returnDesignProclivitiesChips = () => {
    /**
     * RETURNING SELECTED CLIENTELE CHIPS
     */
    return map(
      values.initialValues.designProclivities,
      (eachDesignProclivities) => (
        <Chip
          className={classes.styled_cross}
          key={eachDesignProclivities.designId}
          label={eachDesignProclivities.name}
          onClick={handleInputChange("designProclivities", {
            ...eachDesignProclivities,
            delete: true,
          })}
          onDelete={handleInputChange("designProclivities", {
            ...eachDesignProclivities,
            delete: true,
          })}
          deleteIcon={
            <Styled_cross
              onMouseUp={handleInputChange("designProclivities", {
                ...eachDesignProclivities,
                delete: true,
              })}
            />
          }
        ></Chip>
      )
    );
  };

  const checkFormErrors = () => {
    // VALIDATION CHECK FOR REQUIRED FIELDS AND FORM VALIDATIONS
    const { companySnapShot, companyPitch, trades } = values.initialValues;
    if (!companySnapShot || !companyPitch || !trades) {
      return true;
    }
    const companySnapShotError =
      companySnapShot && companySnapShot.length <= 140;
    const companyPitchError =
      companyPitch.length >= 60 && companyPitch.length <= 600 ? true : false;
    const tradesError = trades[0] ? trades[0].name !== "Select" : false;
    if (companySnapShotError && companyPitchError && tradesError) {
      return false;
    }
    return true;
  };

  const handleDisableMoreTrades = (trades) => {
    // IF A ROW IS NOT SELECTED DISABLE ADD TRADES FUNCTIONALITY
    if (trades) {
      for (let i = 0; i < trades.length; i++) {
        if (!trades[i].tradeId) {
          return true;
        } else if (trades[i].tradeId && trades[i].name === "Select") {
          return true;
        }
      }
    }
    return false;
  };

  const isPrimaryTradeSelected = (trades) => {
    // DISABLE OTHER TRADES IF PRIMARY TRADE IS EMPTY/N-A
    if (trades) {
      if (trades[0]?.tradeId && trades[0]?.name !== "Select") {
        return true;
      }
      return false;
    }
    return false;
  };

  const returnAccoladeYears = () => {
    // RETURNING YEARS FROM CURRENT TO -18 YEARS

    if (Configs) {
      const currentYear = new Date().getFullYear();
      const years = reverse(range(currentYear - 18, currentYear + 1));
      return years;
    }
    return [];
  };

  const deleteImage = (name) => {
    // DELETE IMAGE WRT NAME(LABEL) OF IMAGE
    const { initialValues } = values;
    initialValues[name] = {};
    setValues({ ...values });
  };

  const getPublicProfileLink = () => {
    if (!companyInfo) {
      return;
    }
    let host = get(window.location, "host", "");
    if (host) {
      let tradeworksUrlData = "";
      if (host.includes("localhost")) {
        host = "localhost";
        tradeworksUrlData = `${PUBLIC_PROFILE.local}${companyInfo.tradeWorkUrl}`;
      } else {
        if (host.includes("-dev-")) {
          tradeworksUrlData = `${PUBLIC_PROFILE.dev}${companyInfo.tradeWorkUrl}`;
        }
        if (host.includes("-qa-")) {
          tradeworksUrlData = `${PUBLIC_PROFILE.qa}${companyInfo.tradeWorkUrl}`;
        }
        if (host.includes("-stage-")) {
          tradeworksUrlData = `${PUBLIC_PROFILE.stage}${companyInfo.tradeWorkUrl}`;
        }
        if (host.includes("twwstage")) {
          tradeworksUrlData = `${BASEURL.URL}${companyInfo.tradeWorkUrl}`;
        } else {
          tradeworksUrlData = `${BASEURL.URL}${companyInfo.tradeWorkUrl}`;
        }
      }
      // if (tradeworksUrl == "") {
      //   console.log("tradeworksUrltradeworksUrl", tradeworksUrl);
      //   setTradeworksUrl(tradeworksUrlData);
      // }
      return tradeworksUrlData;
    }
    return null;
  };

  const handleInstaAuth = () => {
    if (configsData.instagramClientId) {
      let host = get(window.location, "host", "");
      if (host.includes("-dev-")) {
        window.location.href =
          INSTAGRAM_AUTHENTICATION_URL.P1 +
          get(configsData, "instagramClientId", "") +
          INSTAGRAM_AUTHENTICATION_URL.P2_DEV;
      }
      if (host.includes("-qa-")) {
        window.location.href =
          INSTAGRAM_AUTHENTICATION_URL.P1 +
          get(configsData, "instagramClientId", "") +
          INSTAGRAM_AUTHENTICATION_URL.P2_QA;
      }
      if (host.includes("-stage-")) {
        window.location.href =
          INSTAGRAM_AUTHENTICATION_URL.P1 +
          get(configsData, "instagramClientId", "") +
          INSTAGRAM_AUTHENTICATION_URL.P2_STAGE;
      }
      window.location.href =
        INSTAGRAM_AUTHENTICATION_URL.P1 +
        get(configsData, "instagramClientId", "") +
        INSTAGRAM_AUTHENTICATION_URL.P2_QA;
    }
  };

  /**this method is responsible for returning checked value (true/false) for a checkbox in dropdown */
  const returnChecked = (name, id) => {
    const foundRecord = find(values.initialValues[name], { vibeId: id });
    if (foundRecord) {
      return true;
    }
    return false;
  };

  const copyText = () => {
    // navigator.clipboard.writeText(getPublicProfileLink());
    // return a promise
    // function copyToClipboard(textToCopy) {
    // navigator clipboard api needs a secure context (https)
    if (navigator.clipboard && window.isSecureContext) {
      // navigator clipboard api method'
      navigator.clipboard.writeText(getPublicProfileLink());
    } else {
      // text area method
      let textArea = document.createElement("textarea");
      textArea.value = getPublicProfileLink();
      // make the textarea out of viewport
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      new Promise((res, rej) => {
        // here the magic happens
        document.execCommand("copy") ? res() : rej();
        textArea.remove();
      });
    }
    // }F
    setClickedCopy(true);
    setTimeout(() => {
      setClickedCopy(false);
    }, 3000);
  };

  /**this method adds new custom input to dropdown(user is free to add this choice to dropdown and save) */
  const handleAddCustomInput = (name) => () => {
    const { initialValues, customInput } = values;
    if (name === "companyVibe") {
      if (!customInput) {
        return;
      }
      initialValues[name] = [
        ...initialValues[name],
        { _id: uuidv4(), name: customInput, isUserCreated: true },
      ];
      setValues({
        ...values,
        customInput: "",
      });
    }
    if (name === "companyAccolades") {
      initialValues[name] = [
        ...initialValues[name],
        {
          id: uuidv4(),
          name: "",
          isUserCreated: true,
          year: [],
          logo: "",
          isNew: true,
        },
      ];
      setValues({
        ...values,
        customInput: "",
      });
    }
    if (name === "collegeAllegiances") {
      initialValues[name] = [
        ...initialValues[name],
        { id: uuidv4(), name: customInput, isUserCreated: true, logo: "" },
      ];
      setValues({
        ...values,
        customInput: "",
      });
    }
  };

  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem("tradeworks_user_token");
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}wpack`;
      window.location.href = `${host}/${token}/${companyId}`;
      // if (host.includes("localhost")) {
      //   host = "localhost:3003";
      //   window.location.href = `http://${host}/${token}/${companyId}`;
      // } else {
      //   if (host.includes("-dev-")) {
      //     host = TDW_URL.WPACK.DEV;
      //   }
      //   if (host.includes("-qa-")) {
      //     host = TDW_URL.WPACK.QA;
      //   }
      //   if (host.includes("-stage-")) {
      //     host = TDW_URL.WPACK.STAGE;
      //   } else {
      //     host = "http://twwstage.franklinreport.com/wpack";
      //   }
      //   window.location.href = `${host}/${token}/${companyId}`;
      // }
    }
  };

  const saveDataRedirect = () => {
    /**
     *
     * FORMATTING THE DATA TO REQUIRED FORMAT
     */
    const { initialValues } = values;
    if (checkFormErrors()) {
      finalValidation();
      // checkValidation("companyPitch", initialValues.companyPitch);
      return;
    }
    const { edit } = props;
    const dataToSubmit = {
      ...initialValues,
      trades: filter(initialValues.trades, (eachTrade) => {
        return eachTrade.name !== "Select";
      }).map((eachTrade) => {
        return eachTrade.tradeId;
      }),
      companyVibe: map(initialValues.companyVibe, (eachVibe) => {
        const newObj = { ...eachVibe, id: eachVibe.vibeId };
        const formatedVibe = omit(newObj, "_id");
        return formatedVibe;
      }),
      companyAccolades: map(initialValues.companyAccolades, (accolade) => {
        if (accolade.isUserCreated) {
          const newObj = omit(accolade, "id");
          return { ...newObj };
        }
        return { ...accolade };
      }),
      collegeAllegiances: map(initialValues.collegeAllegiances, (each) => {
        if (each.isUserCreated) {
          const newObj = omit(each, "id");
          return { ...newObj };
        }
        return { ...each };
      }),
      clientele: map(get(initialValues, "clientele", []), (each) => {
        return each.clientelId;
      }),
      designProclivities: map(
        get(initialValues, "designProclivities", []),
        (each) => {
          return each.designId;
        }
      ),
      venue: get(initialValues, "venue._id", ""),
      employees: get(initialValues, "employees.name", ""),
      isPublicDisplay: initialValues.isPublicDisplay === true,
    };
    // const formatedData = omit(dataToSubmit, "_id");
    dispatch(
      ProfileActions.updateCompanyPitch(dataToSubmit, (res) => {
        if (!res.error) {
          redirectToWpack();
        }
      })
    );
  };

  /**this method handle event change of custom input in dropdown */
  const handleChangeCustomInput = (e) => {
    const { value } = e.target;
    setValues({ ...values, customInput: value });
  };

  const handleAllegancyEdit = (allegancy) => (e) => {
    const { name, value } = e.target;
    allegancy[name] = value;
    setValues({ ...values });
  };

  const {
    companySnapShot,
    companyPitch,
    socialMediaFeeds,
    trades,
    venue,
    companyWebsiteUrl,
  } = values.initialValues;
  const { edit } = props;
  const { yearsFrom, companySize } = Configs ? Configs : {};
  const { errors, clienteleCountVal, toggleNewCompanyModal, showAccoError } =
    values;
  const hasFormErrors = checkFormErrors();
  const disableMoreTrades = handleDisableMoreTrades(trades);
  const primaryTradeSelected = isPrimaryTradeSelected(trades);
  const accoladeYears = returnAccoladeYears();
  return (
    <>
      <div className={classes.CompanyWizard_step2}>
        <div className={classes.main_head_left}>
          <Text className={classes.main_des}>
            {strings.company_wizard.step2.titles.main_des}
          </Text>
          <Text className={classes.main_brand}>
            {strings.company_wizard.step2.titles.main_brand}
          </Text>
        </div>
        <Row className={classes.blk_spacing}>
          <div className={classes.left_side}>
            <div className={classes.main_head_left}>
              <Text className={classes.step_2a}>
                {strings.company_wizard.step2.titles.step_2a}
              </Text>
              <Text className={classes.step_2a_des_a}>
                {strings.company_wizard.step2.titles.step_2a_des}
              </Text>
            </div>
            <div style={{ position: "relative" }}>
              <AddImageHoverLogo
                // defaultImage={values.imgSrcPhoto}
                buttonLabel={strings.company_wizard.step2.titles.Company_logo}
                imageUpload={imageUpload}
                img={values.initialValues}
                deleteImage={deleteImage}
                deleteIcon
                type="companyPitch"
                name="companyLogo"
                cropShape="round"
                className={classes.circle_img}
                edit={edit}
                deleteStyles={classes.logo_del}
              />
              <Text className={classes.helper_name}>
                JPG or PNG | 200KB to 2MB
              </Text>
              {!get(values, "initialValues.companyLogo.image") && (
                <img
                  src="assets/images/red-arrow-left.png"
                  style={{
                    height: "20px",
                    margin: "4px",
                    transform: "rotate(360deg)",
                    position: "absolute",
                    top: "60px",
                    right: "50px",
                  }}
                />
              )}
            </div>
            {/* <Text
              size={14}
              color={color.secondary_palette.grays.shadow_gray}
              family="gillsans_sb"
              fontWeight="normal"
              style={{ textAlign: "center" }}
            >
              PNG or JPG Under 2MB
            </Text> */}
            <div style={{ position: "relative" }}>
              <AddImageHoverLogo
                // defaultImage="assets/images/Gray_Frame_02.png"
                imageUpload={imageUpload}
                name="coverImage"
                deleteImage={deleteImage}
                deleteIcon
                type="companyPitch"
                img={values.initialValues}
                edit={edit}
                cropShape="cover"
                buttonLabel={strings.company_wizard.step2.titles.Cover_Image}
                className={classes.rectangle}
              />
              <Text className={classes.helper_name}>
                JPG or PNG | 300KB to 3MB
              </Text>
              {!get(values, "initialValues.coverImage.image") && (
                <img
                  src="assets/images/red-arrow-left.png"
                  style={{
                    height: "20px",
                    margin: "4px",
                    transform: "rotate(360deg)",
                    position: "absolute",
                    top: "40px",
                    right: "-40px",
                  }}
                />
              )}
            </div>
            {/* <Text
              size={14}
              color={color.secondary_palette.grays.shadow_gray}
              family="gillsans_sb"
              fontWeight="normal"
              style={{ textAlign: "center" }}
            >
              PNG or JPG Under 2MB
            </Text> */}
            <AddImageHoverLogo
              // defaultImage="assets/images/Gray_Frame_10.png"
              imageUpload={imageUpload}
              img={values.initialValues}
              deleteImage={deleteImage}
              deleteIcon
              type="companyPitch"
              name="topFeatureImage1"
              edit={edit}
              buttonLabel={strings.company_wizard.step2.titles.Featured1}
              className={classes.square}
            />
            <Text className={classes.helper_name}>
              JPG or PNG | 200KB to 2MB
            </Text>
            {/* <Text
              size={14}
              color={color.secondary_palette.grays.shadow_gray}
              family="gillsans_sb"
              fontWeight="normal"
              style={{ textAlign: "center" }}
            >
              PNG or JPG Under 2MB
            </Text> */}
            <AddImageHoverLogo
              // defaultImage="assets/images/Gray_Frame_10.png"
              imageUpload={imageUpload}
              deleteImage={deleteImage}
              deleteIcon
              edit={edit}
              type="companyPitch"
              name="topFeatureImage2"
              img={values.initialValues}
              buttonLabel={strings.company_wizard.step2.titles.Featured2}
              className={classes.square}
            />
            <Text className={classes.helper_name}>
              JPG or PNG | 200KB to 2MB
            </Text>
            {/* <Text
              size={14}
              color={color.secondary_palette.grays.shadow_gray}
              family="gillsans_sb"
              fontWeight="normal"
              style={{ textAlign: "center" }}
            >
              PNG or JPG Under 2MB
            </Text> */}
          </div>
          {edit && (
            <CustomButton
              className={classes.logo_btn_red}
              onClick={saveDataRedirect}
              style={{ position: "absolute", right: "44px", top: 0 }}
            >
              POST A JOB NOW
            </CustomButton>
          )}
          <div className={classes.right_side}>
            <div className={classes.main_head}>
              <CustomButton
                variant="outlined"
                color="primary"
                className={`${classes.greenactionButtons} ${classes.second_btn_width}`}
                style={{ float: "right", width: edit && pxToRem(240) }}
                onClick={onCompanyPitchSubmit}
              >
                {edit
                  ? "Save & Go back to Profile"
                  : strings.general.titles.save_continue}
              </CustomButton>
              <Text className={`${classes.txt_align_left} ${classes.step_2a}`}>
                {strings.company_wizard.step2.titles.step_2b}
              </Text>
              <Text
                className={`${classes.txt_align_left} ${classes.step_2a_des}`}
              >
                {strings.company_wizard.step2.titles.step_2b_des}
              </Text>
            </div>
            <div className={classes.spacing}>
              <Text className={classes.sub_heading}>
                <Element_Required_Icon
                  className={
                    checkErrors(0, "companySnapShot")
                      ? `${classes.redIcon}`
                      : classes.purpleIcon
                  }
                />
                {strings.company_wizard.step2.titles.Company_Snapshot}
              </Text>
              <Text
                className={`${classes.txt_align_left} ${classes.step_2a_des}`}
              >
                {strings.company_wizard.step2.titles.Company_Snapshot_Lead}
              </Text>
              <CustomInputCount
                enableCount
                enableInlineCount
                enableClear
                className={classes.company_snapshot}
                countValue={140}
                placeholder="Neoclassical, enduring, residential architectural design."
                defaultValue={companySnapShot}
                onBlur={handleInputChange("companySnapShot")}
              />
              <Row className={classes.pos_r}>
                {errors.companySnapShot && errors.companySnapShot.error && (
                  <Row className={classes.cw_error}>
                    <img
                      src="assets/icons/info_1.svg"
                      className={classes.info}
                    />
                    {errors.companySnapShot.message}
                  </Row>
                )}
              </Row>
            </div>
            <div
              className={`${classes.number_text} ${classes.requried_icon} ${classes.position_relative}`}
            >
              <Text className={classes.sub_heading}>
                <Element_Required_Icon
                  className={
                    checkErrors(0, "companyPitch")
                      ? `${classes.redIcon}`
                      : classes.purpleIcon
                  }
                />
                {strings.company_wizard.step2.titles.Company_Pitch}
              </Text>
              <CustomTextArea
                enableCount
                className={`${classes.compant_pitch} ${
                  get(errors, "companyPitch.error", "")
                    ? `${classes.company_pitch_error}`
                    : ""
                }`}
                countValue={600}
                defaultValue={companyPitch}
                onBlur={handleInputChange("companyPitch")}
                onKeyUp={getInputCount}
              />
              <Text
                size={16}
                color={color.secondary_palette.grays.medium_gray}
                family="gillsans_sb"
                fontWeight="normal"
                className={classes.recommended_350}
              >
                {strings.company_wizard.step2.recommended_350}{" "}
                <span
                  style={{
                    color:
                      ((clienteleCountVal && clienteleCountVal.length) ||
                        (companyPitch && companyPitch.length)) > 60
                        ? color.secondary_palette.grays.medium_gray
                        : color.primary_palette.christmas_red,
                  }}
                >
                  you have{" "}
                  {clienteleCountVal && clienteleCountVal.length
                    ? clienteleCountVal.length || 0
                    : (companyPitch && companyPitch.length) || 0}
                </span>
              </Text>
              {get(errors, "companyPitch.error", "") && (
                <Row className={classes.cw_error}>
                  <img src="assets/icons/info_1.svg" className={classes.info} />
                  {errors.companyPitch.message}
                </Row>
              )}
            </div>
            <Row className={classes.trade_field}>
              <Row style={{ flexWrap: "wrap" }}>
                {errors.trades && errors.trades.error && (
                  <Row className={`${classes.trade_error} ${classes.cw_error}`}>
                    <img
                      src="assets/icons/info_1.svg"
                      className={classes.info}
                    />
                    {errors.trades.message}
                  </Row>
                )}
                {trades &&
                  map(trades, (tradeSection, index) => {
                    return (
                      <>
                        <Row
                          className={`${
                            index < 2
                              ? classes.primary_field
                              : classes.primary_field_2
                          }`}
                        >
                          <Text
                            className={`${
                              index < 2
                                ? classes.sub_fields
                                : classes.sub_fields_2
                            }`}
                          >
                            {index == 0 ? <Element_Required_Icon /> : ""}
                            {returnTradeLabel(index)}
                          </Text>
                          <FormControl className={classes.formControl}>
                            <CustomSelect
                              IconComponent={Chevron_Down_Icon}
                              className={`${classes.employee_select} ${classes.width200}`}
                              value={tradeSection}
                              disabled={
                                index > 0 ? !primaryTradeSelected : false
                              }
                              renderValue={(value) =>
                                value.name || (
                                  <span
                                    style={{
                                      color:
                                        color.secondary_palette.grays
                                          .shadow_gray,
                                    }}
                                  >
                                    Select
                                  </span>
                                )
                              }
                              MenuProps={{
                                getContentAnchorEl: null,
                                disableScrollLock: true,
                                anchorOrigin: {
                                  vertical: "bottom",
                                  horizontal: "left",
                                },
                              }}
                            >
                              <CustomScrollbars
                                height={"240px"}
                                className={classes.employee_scroll}
                              >
                                <MenuItem
                                  onMouseUp={handleTradeSelect(
                                    tradeSection._id,
                                    {
                                      name: "Select",
                                      tradeId: uuidv4(),
                                      _id: uuidv4(),
                                    },
                                    index
                                  )}
                                >
                                  Select
                                </MenuItem>
                                {/* {allTrades &&
                              map(allTrades, (eachTrade) => {
                                return (
                                  <MenuItem
                                    onMouseUp={handleTradeSelect(
                                      tradeSection._id,
                                      {
                                        ...eachTrade,
                                        tradeId: eachTrade._id,
                                      },
                                      index
                                    )}
                                  >
                                    {eachTrade.name}
                                  </MenuItem>
                                );
                              })} */}
                                {groupTrades &&
                                  renderCategorizedMenu(
                                    groupTrades,
                                    tradeSection._id,
                                    index
                                  )}
                              </CustomScrollbars>
                            </CustomSelect>
                          </FormControl>
                        </Row>
                        {index === trades.length - 1 && (
                          <CustomButton
                            className={
                              index > 2
                                ? classes.addtrade_btn
                                : classes.addtrade_btn_2
                            }
                            disabled={disableMoreTrades}
                            onClick={addTradeBtn}
                          >
                            <Lil_Plus_filled />
                            {strings.company_wizard.step2.titles.More_Trades}
                          </CustomButton>
                        )}
                      </>
                    );
                  })}
              </Row>
            </Row>
            <Row className={classes.spacing_field}>
              <Row className={classes.employee_field}>
                <Text className={classes.sub_fields}>
                  {strings.company_wizard.step2.titles.Year_Established}
                </Text>
                <CustomSelect
                  IconComponent={Chevron_Down_Icon}
                  className={`${classes.yearEstablished} ${classes.employee_year}`}
                  renderValue={(value) =>
                    value || (
                      <span
                        style={{
                          color: color.secondary_palette.grays.shadow_gray,
                        }}
                      >
                        Select
                      </span>
                    )
                  }
                  value={values.initialValues.yearEstablished || ""}
                  MenuProps={{
                    getContentAnchorEl: null,
                    disableScrollLock: true,
                    anchorOrigin: {
                      vertical: "bottom",
                      horizontal: "left",
                    },
                  }}
                >
                  <CustomScrollbars className={classes.years_scroll}>
                    <MenuItem onClick={handleInputChange("yearEstablished")} />
                    {yearsFrom &&
                      map(yearsFrom, (year, index) => {
                        return (
                          <MenuItem
                            key={year}
                            value={year}
                            onClick={handleInputChange("yearEstablished")}
                          >
                            {year}
                          </MenuItem>
                        );
                      })}
                  </CustomScrollbars>
                </CustomSelect>{" "}
              </Row>
              <Row
                className={classes.employee_field}
                style={{ marginRight: pxToRem(50) }}
              >
                <Text className={classes.sub_fields}>
                  {strings.company_wizard.step2.titles.Employees}
                </Text>
                <FormControl className={classes.formControl}>
                  <CustomSelect
                    IconComponent={Chevron_Down_Icon}
                    className={`${classes.employee_select} ${classes.employees}`}
                    renderValue={(value) =>
                      value.name || (
                        <span
                          style={{
                            color: color.secondary_palette.grays.shadow_gray,
                          }}
                        >
                          Select
                        </span>
                      )
                    }
                    value={get(values.initialValues, "employees", "")}
                    MenuProps={{
                      getContentAnchorEl: null,
                      disableScrollLock: true,
                      anchorOrigin: {
                        vertical: "bottom",
                        horizontal: "left",
                      },
                    }}
                  >
                    <MenuItem onClick={handleInputChange("employees", {})}>
                      Select
                    </MenuItem>
                    {companySize &&
                      map(companySize, (each) => {
                        return (
                          <MenuItem
                            onClick={handleInputChange("employees", {
                              name: each.size,
                            })}
                          >
                            {each.size}
                          </MenuItem>
                        );
                      })}
                  </CustomSelect>
                </FormControl>
              </Row>
            </Row>
            <div className={classes.vibes_blk}>
              <Row className={classes.vibe_space}>
                <Text className={classes.sub_fields}>
                  {strings.company_wizard.step2.titles.Clientele}
                </Text>
                <FormControl
                  className={`${classes.comp_aco} ${classes.formControl}`}
                >
                  <CustomSelect
                    IconComponent={Chevron_Down_Icon}
                    className={`${classes.clientele_2} ${classes.employee_select}`}
                    placeholder="Select"
                    style={{ width: "312px !important" }}
                    renderValue={(value) => value}
                    value={
                      <span
                        style={{
                          color: color.secondary_palette.grays.shadow_gray,
                          fontSize: "16px",
                        }}
                      >
                        Select
                      </span>
                    }
                    MenuProps={{
                      getContentAnchorEl: null,
                      disableScrollLock: true,
                      anchorOrigin: {
                        vertical: "bottom",
                        horizontal: "left",
                      },
                    }}
                  >
                    <CustomScrollbars
                      height={"220px"}
                      // className={classes.employee_scroll}
                    >
                      {clientele &&
                        map(clientele, (each) => {
                          return (
                            <MenuItem
                              onClick={handleInputChange("clientele", {
                                ...each,
                                clientelId: each._id,
                              })}
                            >
                              {each.name}
                            </MenuItem>
                          );
                        })}
                    </CustomScrollbars>
                  </CustomSelect>
                </FormControl>
                <Text
                  className={classes.sub_extra_fields}
                  style={{ marginLeft: pxToRem(142) }}
                >
                  Choose your company specialities
                </Text>
              </Row>
              <Row className={classes.flex_wrap}>
                {values.initialValues.clientele && returnClienteleChips()}
              </Row>
            </div>
            <div className={classes.vibes_blk}>
              <Row className={classes.vibe_space}>
                <Text className={classes.sub_fields}>
                  {strings.company_wizard.step2.titles.design_proclivities}
                </Text>
                <FormControl
                  className={`${classes.comp_aco} ${classes.formControl}`}
                >
                  <CustomSelect
                    IconComponent={Chevron_Down_Icon}
                    className={`${classes.clientele} ${classes.employee_select}`}
                    placeholder="Select"
                    renderValue={(value) => value}
                    value={
                      <span
                        style={{
                          color: color.secondary_palette.grays.shadow_gray,
                          fontSize: "16px",
                        }}
                      >
                        Select
                      </span>
                    }
                    MenuProps={{
                      getContentAnchorEl: null,
                      disableScrollLock: true,
                      anchorOrigin: {
                        vertical: "bottom",
                        horizontal: "left",
                      },
                    }}
                  >
                    <CustomScrollbars
                      height={"220px"}
                      // className={classes.employee_scroll}
                    >
                      {designProclivities &&
                        map(designProclivities, (each) => {
                          return (
                            <MenuItem
                              onClick={handleInputChange("designProclivities", {
                                ...each,
                                designId: each._id,
                              })}
                            >
                              {each.name}
                            </MenuItem>
                          );
                        })}
                    </CustomScrollbars>
                  </CustomSelect>
                </FormControl>
                <Text className={classes.sub_extra_fields}>
                  Choose your company's forte
                </Text>
              </Row>
              <Row className={classes.flex_wrap}>
                {values.initialValues.designProclivities &&
                  returnDesignProclivitiesChips()}
              </Row>
            </div>

            <Row className={classes.Accolades_blk}>
              <Row>
                <Text className={classes.sub_fields}>
                  {strings.company_wizard.step2.titles.Company_Accolades}
                </Text>
                <div className={classes.pos_r}>
                  <CustomAutocomplete
                    popupIcon={
                      <Chevron_Down_Icon
                        className={classes.accolades_drop_down_icon}
                      />
                    }
                    onChange={(event, newValue) => {
                      onAccoladeSelect(event, newValue);
                    }}
                    value={values.initialValues.companyAccolades || []}
                    hasLogo
                    options={accolades || []}
                    getOptionLabel={(option) => option.name || ""}
                    placeholder="Select or Add Your Own at Bottom of Dropdown"
                    className={classes.accolades}
                    ListboxComponent={React.forwardRef(
                      ({ children, ...rest }, ref) => (
                        <div className={classes.options}>
                          <CustomScrollbars
                            className={classes.scroll}
                            ref={ref}
                            {...rest}
                          >
                            {children}
                          </CustomScrollbars>
                          <Row className={classes.dropdownInput}>
                            <input
                              placeholder="+ ADD YOUR OWN"
                              className={classes.drop_input_accolades}
                              // onChange={handleChangeCustomInput}
                              // value={values.customInput}
                              onMouseDown={handleAddCustomInput(
                                "companyAccolades"
                              )}
                            />
                            <Text
                              className={`${classes.accolades_plus} ${classes.plus}`}
                              name="companyAccolades"
                              onMouseDown={handleAddCustomInput(
                                "companyAccolades"
                              )}
                            >
                              +
                            </Text>
                          </Row>
                        </div>
                      )
                    )}
                  />
                </div>
              </Row>
              {returnAccoladeSections(accoladeYears)}
            </Row>
            <div className={classes.vibes_blk}>
              <Row className={classes.vibe_space}>
                <Text className={classes.sub_fields}>
                  {strings.company_wizard.step2.titles.venue}
                </Text>
                <FormControl
                  className={`${classes.comp_aco} ${classes.formControl}`}
                >
                  <CustomSelect
                    IconComponent={Chevron_Down_Icon}
                    className={`${classes.employee_select} ${classes.venues}`}
                    value={venue || {}}
                    renderValue={(value) =>
                      value.name || (
                        <span
                          style={{ color: color.primary_palette.fafa_gray }}
                        >
                          Select
                        </span>
                      )
                    }
                    MenuProps={{
                      getContentAnchorEl: null,
                      disableScrollLock: true,
                      anchorOrigin: {
                        vertical: "bottom",
                        horizontal: "left",
                      },
                    }}
                  >
                    <CustomScrollbars
                      height={"170px"}
                      className={classes.employee_scroll}
                    >
                      {venues &&
                        map(venues, (venue) => {
                          return (
                            <MenuItem
                              onClick={handleInputChange("venue", { ...venue })}
                            >
                              {venue.name}
                            </MenuItem>
                          );
                        })}
                    </CustomScrollbars>
                  </CustomSelect>
                </FormControl>
              </Row>
              <Text className={classes.sub_title}>
                {strings.company_wizard.step2.titles.Venu_des}
              </Text>
            </div>
            <div className={classes.vibes_blk}>
              <Row className={classes.vibe_space}>
                <Text className={classes.sub_fields}>
                  {strings.company_wizard.step2.titles.Company_Vibe}
                </Text>
                <FormControl
                  className={`${classes.comp_aco} ${classes.formControl}`}
                >
                  <CustomSelect
                    key={companyVibes}
                    IconComponent={Chevron_Down_Icon}
                    placeholder="Select or Add Your Own at Bottom of Dropdown"
                    value="Select or Add Your Own at Bottom of Dropdown"
                    className={`${classes.vibe_select} ${classes.companyVibe}`}
                    MenuProps={{
                      getContentAnchorEl: null,
                      disableScrollLock: true,
                      anchorOrigin: {
                        vertical: "bottom",
                        horizontal: "left",
                      },
                    }}
                  >
                    <CustomScrollbars className={classes.vibes_scroll}>
                      {companyVibes &&
                        map(companyVibes, (eachVibe) => {
                          return (
                            <MenuItem
                              onMouseUp={handleCompanyVibeChange(
                                "companyVibe",
                                eachVibe
                              )}
                            >
                              <Checkbox
                                checked={returnChecked(
                                  "companyVibe",
                                  eachVibe._id
                                )}
                              />
                              {eachVibe.name}
                            </MenuItem>
                          );
                        })}
                    </CustomScrollbars>
                    <Row className={classes.dropdownInput}>
                      <input
                        placeholder="+ ADD YOUR OWN"
                        className={classes.drop_input}
                        onChange={handleChangeCustomInput}
                        value={values.customInput}
                      />
                      <Text
                        className={classes.plus}
                        name="companyVibe"
                        onClick={handleAddCustomInput("companyVibe")}
                      >
                        +
                      </Text>
                    </Row>
                  </CustomSelect>
                </FormControl>
              </Row>
              <Text className={classes.sub_title}>
                {strings.company_wizard.step2.titles.Company_Vibe_des}
              </Text>
              <Row className={classes.flex_wrap}>
                {values.initialValues.companyVibe && returnCompanyVibeChips()}
              </Row>
            </div>

            <Row className={`${classes.blck} ${classes.vibe_space}`}>
              <Text className={classes.sub_fields}>
                {strings.company_wizard.step2.titles.College_Allegiances}
              </Text>
              {/* <FormControl className={`${classes.vibe} ${classes.formControl}`}>
                <CustomSelect
                  IconComponent={Chevron_Down_Icon}
                  className={classes.College_Allegiances_dropdown}
                  key={values.initialValues.collegeAllegiances}
                  MenuProps={{
                    getContentAnchorEl: null,
                    disableScrollLock: true,
                    anchorOrigin: {
                      vertical: "bottom",
                      horizontal: "left",
                    },
                  }}
                >
                  <CustomScrollbars className={classes.vibes_scroll}>
                    {collegeAllegiances &&
                      map(collegeAllegiances, (allegancy) => {
                        return (
                          <Row>
                            <img
                              src={
                                allegancy.logo ||
                                "https://tradeworksprodsas.blob.core.windows.net/tra-con-use-dev/company/company.PNG"
                              }
                              style={{ width: "20px", height: "20px" }}
                            />
                            <MenuItem
                              key={allegancy._id}
                              value={allegancy}
                              style={{ width: "100%" }}
                              onMouseUp={handleInputChange(
                                "collegeAllegiances",
                                {
                                  ...allegancy,
                                  id: allegancy._id,
                                }
                              )}
                            >
                              {allegancy.name}
                            </MenuItem>{" "}
                          </Row>
                        );
                      })}
                  </CustomScrollbars>
                  <Row className={classes.dropdownInput}>
                    <input
                      placeholder="+ Add your own"
                      className={classes.drop_input2}
                      onMouseDown={handleAddCustomInput("collegeAllegiances")}
                    />
                    <Text
                      className={classes.plus}
                      name="collegeAllegiances"
                      onMouseDown={handleAddCustomInput("collegeAllegiances")}
                    >
                      +
                    </Text>
                  </Row>
                </CustomSelect>
              </FormControl> */}
              <div className={classes.pos_r}>
                <CustomAutocomplete
                  popupIcon={
                    <Chevron_Down_Icon
                      className={classes.accolades_drop_down_icon}
                    />
                  }
                  disableOpenOnFocus
                  onChange={(event, newValue) => {
                    onAllianceSelect(event, newValue);
                  }}
                  onBlur={setInitialColleges}
                  onTextChange={handleCollegeInput}
                  value={values.initialValues.collegeAllegiances || []}
                  hasLogo
                  options={collegeAllegiancesList || []}
                  // options={collegeAllegiances || []}
                  getOptionLabel={(option) => option.name || ""}
                  placeholder="Select from DropDown or Add Your Own Below"
                  className={classes.college_accolades}
                  filterOptions={(options, state) => options}
                  ListboxComponent={React.forwardRef(
                    ({ children, ...rest }, ref) => (
                      <div className={classes.options}>
                        <CustomScrollbars
                          className={classes.scroll}
                          ref={ref}
                          {...rest}
                        >
                          {children}
                        </CustomScrollbars>
                        <Row className={classes.dropdownInput}>
                          <input
                            placeholder="+ ADD YOUR OWN"
                            className={classes.drop_input_accolades}
                            onMouseDown={handleAddCustomInput(
                              "collegeAllegiances"
                            )}
                          />
                          <Text
                            className={`${classes.accolades_plus} ${classes.plus}`}
                            name="companyAccolades"
                            onMouseDown={handleAddCustomInput(
                              "collegeAllegiances"
                            )}
                          >
                            +
                          </Text>
                        </Row>
                      </div>
                    )
                  )}
                />
              </div>
              <Row>
                <CustomCheckbox
                  className={classes.checkbox}
                  onChange={handleInputChange("isPublicDisplay")}
                  checked={values.initialValues.isPublicDisplay}
                />
                <Text className={`${classes.sub_title} ${classes.sub_space}`}>
                  {strings.company_wizard.step2.titles.College_Allegiances_des}
                </Text>
              </Row>
              <div>{returnCollegeAllegenciesChips()}</div>
            </Row>
            <div className={classes.spacing}>
              <Row>
                <Text className={classes.sub_fields} style={{ width: "116px" }}>
                  Company Url
                </Text>
                <span style={{ paddingTop: pxToRem(3) }}>www.</span>
                <CustomInputCount
                  enableCount={false}
                  style={{ width: "300px" }}
                  enableInlineCount
                  className={classes.company_snapshot}
                  placeholder="trade-works.com"
                  defaultValue={companyWebsiteUrl}
                  onBlur={handleInputChange("companyWebsiteUrl")}
                />
              </Row>
            </div>
            <Row className={classes.pos_r}>
              {errors.companyWebsiteUrl && errors.companyWebsiteUrl.error && (
                <Row className={classes.cw_error}>
                  <img src="assets/icons/info_1.svg" className={classes.info} />
                  {errors.companyWebsiteUrl.message}
                </Row>
              )}
            </Row>
            <Row className={classes.pad_t_25}>
              <div className={classes.main_head}>
                <Text
                  className={`${classes.txt_align_left} ${classes.step_2a}`}
                >
                  {strings.company_wizard.step2.titles.Social_Media}
                </Text>
                <Text
                  className={`${classes.txt_align_left} ${classes.step_2a_des}`}
                >
                  {strings.company_wizard.step2.titles.Social_Media_des}
                </Text>
              </div>
            </Row>
            <Row className={classes.padding_top_20}>
              <div className={classes.textfield}>
                <CustomTextFields
                  name="facebook"
                  placeholder="/UserName"
                  icon={
                    get(socialMediaFeeds, "facebook", "") ? (
                      <FaceBook_Color />
                    ) : (
                      <FaceBook_Outline_Icon />
                    )
                  }
                  value={get(socialMediaFeeds, "facebook", "")}
                  onChange={handleOnBlurSocialMedia}
                />
              </div>
              <div className={classes.textfield}>
                <CustomTextFields
                  name="twitter"
                  placeholder="@UserName"
                  onChange={handleOnBlurSocialMedia}
                  value={get(socialMediaFeeds, "twitter", "")}
                  icon={
                    get(socialMediaFeeds, "twitter", "") ? (
                      <Twitter_Color />
                    ) : (
                      <Twitter_Outline />
                    )
                  }
                />
              </div>
              <div className={classes.textfield}>
                <CustomTextFields
                  name="youtube"
                  placeholder="/UserName"
                  icon={
                    get(socialMediaFeeds, "youtube", "") ? (
                      <Youtube_Color />
                    ) : (
                      <Youtube_Outline_Icon />
                    )
                  }
                  onChange={handleOnBlurSocialMedia}
                  value={get(socialMediaFeeds, "youtube", "")}
                />
              </div>
            </Row>
            <Row className={classes.padding_top_20}>
              <div className={classes.textfield}>
                <CustomTextFields
                  name="linkedin"
                  placeholder="/UserName"
                  icon={
                    get(socialMediaFeeds, "linkedin", "") ? (
                      <LinkedIn_Color />
                    ) : (
                      <Linkedin_Outline_Icon />
                    )
                  }
                  onChange={handleOnBlurSocialMedia}
                  value={get(socialMediaFeeds, "linkedin", "")}
                />
              </div>
              <div className={classes.textfield}>
                {/* <div>
                  <img
                    src="assets/images/instagram_auth.png"
                    className={classes.insta_icon}
                  />
                  <CustomLink
                    className={classes.insta_underline}
                    onClick={edit && handleInstaAuth}
                  >
                    {" "}
                    Authorize instagram
                  </CustomLink>
                </div> */}
                <div className={classes.textfield}>
                  <CustomTextFields
                    name="instagram"
                    placeholder="/UserName"
                    icon={
                      get(socialMediaFeeds, "instagram", "") ? (
                        <Instagram_Color />
                      ) : (
                        <Instagram_Outline_Icon />
                      )
                    }
                    onChange={handleOnBlurSocialMedia}
                    value={get(socialMediaFeeds, "instagram", "")}
                  />
                </div>
              </div>
              <div className={classes.textfield1}>
                <CustomTextFields
                  name="pinterest"
                  icon={<Youtube_Outline_Icon />}
                  onChange={handleOnBlurSocialMedia}
                  value={get(socialMediaFeeds, "pinterest", "")}
                />
              </div>
            </Row>
            <Row className={classes.company_pitch_bg}>
              <Row className={classes.btn_width}>
                {companyInfo && companyInfo.tradeWorkUrl && (
                  <Row>
                    <div
                      className={`${classes.main_head} ${classes.padding_top_15}`}
                    >
                      <Text
                        className={`${classes.txt_align_left} ${classes.step_2a} ${classes.step_2a}`}
                      >
                        {strings.company_wizard.step2.titles.Company_url}
                      </Text>
                      <Text className={classes.link}>
                        <a>{getPublicProfileLink()}</a>
                        <span onClick={copyText} className={classes.linkCopy}>
                          COPY
                        </span>
                        &nbsp;
                        {clickedCopy && (
                          <img src="assets/icons/small_check_green.svg" />
                        )}
                      </Text>
                      <Text
                        className={`${classes.step_2a_des} ${classes.step_spacing}`}
                      >
                        {strings.company_wizard.step2.titles.change_url}
                        <span className={classes.setting}>
                          <a>
                            {
                              strings.company_wizard.step2.titles
                                .Company_setting
                            }
                          </a>
                        </span>
                      </Text>
                    </div>
                  </Row>
                )}
                <Row className={classes.btn_spacing}>
                  {!edit && (
                    <customLink
                      className={classes.later_btn}
                      onClick={handleFinishLater}
                    >
                      {strings.general.titles.finish_later}
                    </customLink>
                  )}
                  <Row className={classes.first_btn}>
                    {!edit && (
                      <CustomButton
                        variant="outlined"
                        color="primary"
                        onClick={handleNext}
                        className={`${classes.actionButtons} ${classes.first_btn_width}`}
                      >
                        {strings.general.titles.skip}
                      </CustomButton>
                    )}
                    {showAccoError && (
                      <Row
                        className={`${classes.cw_error} ${classes.fill_all_error}`}
                      >
                        <img
                          src="assets/icons/info_1.svg"
                          className={classes.info}
                        />
                        Please Add Title and Logo for Accolades
                      </Row>
                    )}
                    {hasFormErrors && (
                      <Row
                        className={`${classes.cw_error} ${classes.fill_all_error}`}
                      >
                        <img
                          src="assets/icons/info_1.svg"
                          className={classes.info}
                        />
                        {strings.company_wizard.step2.titles.errorMsg}
                      </Row>
                    )}
                    <CustomButton
                      variant="outlined"
                      color="primary"
                      className={`${classes.greenactionButtons} ${classes.second_btn_width} `}
                      // disabled={hasFormErrors}
                      style={{ width: edit && pxToRem(240) }}
                      onClick={onCompanyPitchSubmit}
                    >
                      {edit
                        ? "Save & Go back to Profile"
                        : strings.general.titles.save_continue}
                    </CustomButton>
                  </Row>
                </Row>
              </Row>
            </Row>
          </div>
        </Row>
      </div>
      {toggleNewCompanyModal && (
        <NewCompanyConfirm handleToggleModal={toggleCompanyModal} />
      )}
    </>
  );
}
export default CWStyles(CompanyWizard);
