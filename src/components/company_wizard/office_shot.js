import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { filter, map, get } from "lodash";
import uuidv4 from "uuid/v4";
import { useHistory } from "react-router-dom";

import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import AddImageLogo from "./add_image_logo";
import strings from "../../utilities/strings";
import officeshotStyles from "./styles/office_shot_styles";
import CustomButton from "../navigations/custom_buttons";
import { color, pxToRem } from "../../utilities/themes";

import { BASEURL, PROFILE_TYPES } from "../../constants";
import { ProfileActions } from "../../redux/actions";

function Officeshot(props) {
  const { classes, handleNext } = props;
  const history = useHistory();
  // SINCE DATA DEPENDENCY WITH BUSINESS CARD WAS IMPLEMENTED HARDCODED SAMPLE DATA
  const [values, setValues] = useState({
    officeShotsData: [],
    errors: {},
  });

  const officeshots = useSelector((state) => state.Profile.officeshots);
  const businesscard = useSelector((state) => state.Profile.refBusinessCard);
  const dispatch = useDispatch();
  // const fetchData = () => {
  //   dispatch(
  //     ProfileActions.getCompanyProfileData([
  //       PROFILE_TYPES.OFFICE_SHOTS,
  //       PROFILE_TYPES.BUSINESS_CARD,
  //     ])
  //   );
  // };
  useEffect(() => {
    if (officeshots) {
      setInitialData();
    }
  }, []);

  const setInitialData = () => {
    if (officeshots.length) {
      const formattedData = map(officeshots, (each) => {
        const updatedUrls = map(each.officeUrl, (eachUrls) => {
          return { image: eachUrls };
        });
        return { ...each, officeUrl: updatedUrls, id: each._id };
      });
      setValues({ ...values, officeShotsData: formattedData });
    }
  };

  // useEffect(() => {
  //   fetchData();
  // }, []);

  useEffect(() => {
    if (businesscard && get(officeshots, "length", 0) === 0) {
      const { businessCard } = businesscard;
      const layoutsArr = map(businessCard, (card) => {
        return {
          id: uuidv4(),
          location: get(card, "nickname", "") || get(card, "city", ""),
          businessCardId: get(card, "businessCardId", ""),
          officeUrl: [],
        };
      });
      setValues({ ...values, officeShotsData: [...layoutsArr] });
    }
  }, [officeshots, businesscard]);

  const deleteImage = (name, id, positionIndex) => {
    const { officeShotsData } = values;
    const layout = filter(officeShotsData, (address) => {
      return address.id === id;
    })[0];
    if (layout) {
      layout.officeUrl = layout.officeUrl || [];
      layout.officeUrl[positionIndex] = {};
    }
    setValues({ ...values });
  };

  const imageUpload = (imageData) => {
    const { index, id } = imageData;
    const { officeShotsData } = values;
    const layout = filter(officeShotsData, (address) => {
      return address.id === id;
    })[0];
    if (layout) {
      layout.officeUrl = layout.officeUrl || [];
      layout.officeUrl[index] = imageData;
    }
    setValues({ ...values });
  };

  const handleUploadError = (obj) => {
    const { errors } = values;
    const { error, message, layout } = obj;
    if (!error) {
      values.errors = {};
      setValues({ ...values });
      return;
    } else {
      errors[layout] = { error, message };
      setValues({ ...values, errors });
    }
  };

  const returnLayout = (type, data) => {
    const { errors } = values;

    switch (type) {
      case 1:
        return (
          <>
            <Row className={classes.block_relative}>
              <Row className={classes.justify}>
                <AddImageLogo
                  defaultImage="assets/images/defaultImg1.png"
                  index={0}
                  deleteIcon
                  id={data.id}
                  type="officeShots"
                  deleteImage={deleteImage}
                  refreshIcon
                  className={classes.newYork_1}
                  marginRight_9={classes.marginRight_9}
                  layoutImage={data.officeUrl}
                  imageUpload={imageUpload}
                  layout={1}
                  noErrorDisplay
                  handleUploadError={handleUploadError}
                  cropShape="rect2"
                />
                <Row className={classes.newYork_block}>
                  <AddImageLogo
                    defaultImage="assets/images/defaultImg2.png"
                    index={1}
                    layout={1}
                    deleteIcon
                    id={data.id}
                    refreshIcon
                    deleteImage={deleteImage}
                    type="officeShots"
                    noErrorDisplay
                    className={classes.newYork_2}
                    layoutImage={data.officeUrl}
                    imageUpload={imageUpload}
                    handleUploadError={handleUploadError}
                    cropShape="rect2"
                  />
                  <AddImageLogo
                    defaultImage="assets/images/defaultImg2.png"
                    index={2}
                    layout={1}
                    deleteIcon
                    id={data.id}
                    refreshIcon
                    deleteImage={deleteImage}
                    type="officeShots"
                    noErrorDisplay
                    className={classes.newYork_3}
                    layoutImage={data.officeUrl}
                    imageUpload={imageUpload}
                    handleUploadError={handleUploadError}
                    cropShape="rect2"
                  />
                </Row>
              </Row>
              {get(errors[data.id], `error`, false) && (
                <Row
                  className={`${classes.errorStyles} ${classes.error_msg_container}`}
                >
                  <img
                    src="assets/images/warning.svg"
                    className={classes.warn_icon}
                  />
                  <Text
                    size={15}
                    color={color.primary_palette.christmas_red}
                    family="gillsans_r"
                    className={`${classes.errorMessage} `}
                  >
                    {get(
                      errors[data.id],
                      "message",
                      "Failed please try again!"
                    )}
                  </Text>{" "}
                </Row>
              )}
            </Row>
          </>
        );
      case 2:
        return (
          <>
            <Row className={classes.justify}>
              <Row className={classes.paris_block}>
                <AddImageLogo
                  defaultImage="assets/images/defaultImg2.png"
                  index={0}
                  deleteIcon
                  id={data.id}
                  refreshIcon
                  layoutImage={data.officeUrl}
                  deleteImage={deleteImage}
                  type="officeShots"
                  className={classes.paris_1}
                  imageUpload={imageUpload}
                  layout={2}
                  noErrorDisplay
                  cropShape="rect2"
                  handleUploadError={handleUploadError}
                />
                <AddImageLogo
                  defaultImage="assets/images/defaultImg2.png"
                  index={1}
                  deleteIcon
                  id={data.id}
                  refreshIcon
                  layoutImage={data.officeUrl}
                  deleteImage={deleteImage}
                  type="officeShots"
                  className={classes.paris_2}
                  imageUpload={imageUpload}
                  cropShape="rect2"
                  layout={2}
                  noErrorDisplay
                  handleUploadError={handleUploadError}
                />
              </Row>
              <AddImageLogo
                defaultImage="assets/images/defaultImg1.png"
                index={2}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                type="officeShots"
                className={classes.paris_3}
                marginLeft_9={classes.marginLeft_9}
                imageUpload={imageUpload}
                layout={2}
                cropShape="rect2"
                noErrorDisplay
                handleUploadError={handleUploadError}
              />
            </Row>
            {get(errors[data.id], `error`, false) && (
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  {get(errors[data.id], "message", "Failed please try again!")}
                </Text>{" "}
              </Row>
            )}
          </>
        );
      case 3:
        return (
          <>
            <Row className={classes.justify}>
              <AddImageLogo
                defaultImage="assets/images/defaultImg3.png"
                index={0}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                type="officeShots"
                className={classes.london_1}
                imageUpload={imageUpload}
                layout={3}
                noErrorDisplay
                handleUploadError={handleUploadError}
                cropShape="rect"
              />
              <AddImageLogo
                defaultImage="assets/images/defaultImg3.png"
                index={1}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                imageUpload={imageUpload}
                type="officeShots"
                className={classes.london_1}
                margin10={classes.margin_10}
                layout={3}
                noErrorDisplay
                handleUploadError={handleUploadError}
                cropShape="rect"
              />
              <AddImageLogo
                defaultImage="assets/images/defaultImg3.png"
                index={2}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                imageUpload={imageUpload}
                type="officeShots"
                className={classes.london_1}
                layout={3}
                noErrorDisplay
                handleUploadError={handleUploadError}
                cropShape="rect"
              />
            </Row>
            {get(errors[data.id], `error`, false) && (
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  {get(errors[data.id], "message", "Failed please try again!")}
                </Text>{" "}
              </Row>
            )}
          </>
        );
      case 4:
        return (
          <>
            <Row className={classes.justify}>
              <AddImageLogo
                defaultImage="assets/images/defaultImg3.png"
                index={0}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                imageUpload={imageUpload}
                type="officeShots"
                className={classes.sidney_1}
                layout={4}
                cropShape="rect"
                noErrorDisplay
                margin10={classes.margin_10}
                handleUploadError={handleUploadError}
              />
              <AddImageLogo
                defaultImage="assets/images/defaultImg1.png"
                index={1}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                imageUpload={imageUpload}
                type="officeShots"
                className={classes.sidney_2}
                layout={4}
                cropShape="rect2"
                noErrorDisplay
                handleUploadError={handleUploadError}
              />
            </Row>
            {get(errors[data.id], `error`, false) && (
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  {get(errors[data.id], "message", "Failed please try again!")}
                </Text>{" "}
              </Row>
            )}
          </>
        );
      case 5:
        return (
          <>
            <Row className={classes.justify}>
              <AddImageLogo
                defaultImage="assets/images/defaultImg1.png"
                index={1}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                imageUpload={imageUpload}
                type="officeShots"
                className={classes.sidney_2}
                cropShape="rect2"
                layout={5}
                noErrorDisplay
                margin10={classes.margin_10}
                handleUploadError={handleUploadError}
              />
              <AddImageLogo
                defaultImage="assets/images/defaultImg3.png"
                index={0}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                imageUpload={imageUpload}
                type="officeShots"
                className={classes.sidney_1}
                cropShape="rect"
                layout={5}
                noErrorDisplay
                handleUploadError={handleUploadError}
              />
            </Row>
            {get(errors[data.id], `error`, false) && (
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  {get(errors[data.id], "message", "Failed please try again!")}
                </Text>{" "}
              </Row>
            )}
          </>
        );
      case 6:
        return (
          <>
            <Row className={classes.justify}>
              <AddImageLogo
                defaultImage="assets/images/defaultImg3.png"
                index={0}
                deleteIcon
                id={data.id}
                cropShape="rect"
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                imageUpload={imageUpload}
                type="officeShots"
                className={classes.tokyo_1}
                layout={6}
                margin10={classes.margin_10}
                noErrorDisplay
                handleUploadError={handleUploadError}
              />
              <Row className={classes.sidney_blk}>
                <AddImageLogo
                  defaultImage="assets/images/defaultImg2.png"
                  index={2}
                  deleteIcon
                  id={data.id}
                  refreshIcon
                  layoutImage={data.officeUrl}
                  deleteImage={deleteImage}
                  imageUpload={imageUpload}
                  cropShape="rect2"
                  margin10={classes.margin_10}
                  type="officeShots"
                  className={classes.tokyo_middle}
                  layout={6}
                  noErrorDisplay
                  handleUploadError={handleUploadError}
                />
                <AddImageLogo
                  defaultImage="assets/images/defaultImg2.png"
                  index={3}
                  deleteIcon
                  id={data.id}
                  refreshIcon
                  margin10={classes.margin_10}
                  cropShape="rect2"
                  layoutImage={data.officeUrl}
                  deleteImage={deleteImage}
                  imageUpload={imageUpload}
                  type="officeShots"
                  className={classes.tokyo_middle}
                  layout={6}
                  noErrorDisplay
                  handleUploadError={handleUploadError}
                />
              </Row>
              <AddImageLogo
                defaultImage="assets/images/defaultImg3.png"
                index={1}
                deleteIcon
                id={data.id}
                refreshIcon
                layoutImage={data.officeUrl}
                deleteImage={deleteImage}
                imageUpload={imageUpload}
                type="officeShots"
                className={classes.tokyo_1}
                cropShape="rect"
                layout={6}
                noErrorDisplay
                handleUploadError={handleUploadError}
              />
            </Row>
            {get(errors[data.id], `error`, false) && (
              <Row
                className={`${classes.errorStyles} ${classes.error_msg_container}`}
              >
                <img
                  src="assets/images/warning.svg"
                  className={classes.warn_icon}
                />
                <Text
                  size={15}
                  color={color.primary_palette.christmas_red}
                  family="gillsans_r"
                  className={`${classes.errorMessage} `}
                >
                  {get(errors[data.id], "message", "Failed please try again!")}
                </Text>{" "}
              </Row>
            )}
          </>
        );
      default:
        return null;
    }
  };

  const returnAllLayouts = () => {
    /**
     * RETURN LAYOUTS BASED ON OFFICE ADDRESS GIVEN
     * TOTAL LAYOUTS 4
     * LAYOUT WILL BE GIVEN IN SEQUENCE
     * IF EXCEEDED CYCLE REPEATS
     */
    const { officeShotsData } = values;
    let counter = 1;
    return (
      <Row className={classes.main_grid}>
        {map(officeShotsData, (eachAddress, index) => {
          if (counter === 7) {
            counter = 1;
          }
          counter += 1;
          return (
            <>
              {
                <Text
                  className={
                    index > 0
                      ? classes.country_name_paddingTop
                      : classes.country_name
                  }
                >
                  {eachAddress.location}
                </Text>
              }
              {returnLayout(counter - 1, eachAddress)}
              {
                <Text
                  className={
                    index > 0
                      ? classes.helper_name_paddingTop
                      : classes.helper_name
                  }
                >
                  JPG or PNG | 200KB to 2MB
                </Text>
              }
            </>
          );
        })}
      </Row>
    );
  };

  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let token = localStorage.getItem("tradeworks_user_token");
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}wpack`;
      window.location.href = `${host}/${token}/${companyId}`;
      // if (host.includes("localhost")) {
      //   host = "localhost:3003";
      //   window.location.href = `http://${host}/${token}/${companyId}`;
      // } else {
      //   if (host.includes("-dev-")) {
      //     host = TDW_URL.WPACK.DEV;
      //   }
      //   if (host.includes("-qa-")) {
      //     host = TDW_URL.WPACK.QA;
      //   }
      //   if (host.includes("-stage-")) {
      //     host = TDW_URL.WPACK.STAGE;
      //   } else {
      //     host = "http://twwstage.franklinreport.com/wpack";
      //   }
      //   window.location.href = `${host}/${token}/${companyId}`;
      // }
    }
  };

  const saveDataRedirect = () => {
    const { officeShotsData } = values;
    const { edit } = props;
    const dataToSubmit = map(officeShotsData, (eachAddress, index) => {
      delete eachAddress._id;
      return { ...eachAddress, rank: index + 1 };
    });
    dispatch(
      ProfileActions.updateOfficeShots(dataToSubmit, (res) => {
        if (!res.error) {
          redirectToWpack();
        }
      })
    );
  };

  const submitOfficeShot = () => {
    const { officeShotsData } = values;
    const { edit } = props;
    const dataToSubmit = map(officeShotsData, (eachAddress, index) => {
      delete eachAddress._id;
      return { ...eachAddress, rank: index + 1 };
    });
    dispatch(
      ProfileActions.updateOfficeShots(dataToSubmit, (res) => {
        if (!res.error) {
          if (edit) {
            history.push("twc/profile");
          } else {
            handleNext();
          }
        }
      })
    );
  };

  const handleFinishLater = () => {
    history.push("twc/profile");
  };

  const { edit } = props;
  return (
    <>
      {edit && (
        <CustomButton
          className={classes.logo_btn_red}
          // disabled={disableAddButton()}
          onClick={saveDataRedirect}
          style={{ float: "right" }}
        >
          POST A JOB NOW
        </CustomButton>
      )}
      <Row className={classes.office_shot}>
        <Row className={classes.center_text}>
          <Text className={classes.main_des}>
            {strings.company_wizard.step4.titles.step4_title}
          </Text>
          <Text className={classes.main_brand}>
            {strings.company_wizard.step4.titles.step4_title_des}
          </Text>
        </Row>
        <Row className={classes.padding_t_36}>
          <Row className={classes.main_head}>
            <Text className={classes.step_2a}>
              {strings.company_wizard.step4.titles.Step4}
            </Text>
            <Text className={classes.step_2a_des}>
              {strings.company_wizard.step4.titles.step4_des}
            </Text>
          </Row>
          <Row className={classes.margin_left_17}>
            <Text className={classes.offices}>
              {strings.company_wizard.step4.titles.offices}
            </Text>
          </Row>
        </Row>
        {returnAllLayouts()}
        <Row className={classes.btn_blk}>
          {!edit && (
            <customLink
              className={classes.later_btn}
              onClick={handleFinishLater}
            >
              {strings.general.titles.finish_later}
            </customLink>
          )}
          <Row className={classes.btn_blk_right}>
            {!edit && (
              <CustomButton
                variant="outlined"
                color="primary"
                onClick={handleNext}
                className={`${classes.actionButtons} ${classes.first_btn_width}`}
              >
                {strings.general.titles.skip}
              </CustomButton>
            )}
            <CustomButton
              variant="outlined"
              color="primary"
              className={`${classes.greenactionButtons} ${classes.second_btn_width}`}
              onClick={submitOfficeShot}
              style={{ width: edit && pxToRem(240) }}
            >
              {edit
                ? "Save & Go back to Profile"
                : strings.general.titles.save_continue}
            </CustomButton>
          </Row>
        </Row>
      </Row>
    </>
  );
}

export default officeshotStyles(Officeshot);
