import React, { useState, useRef, useEffect } from "react";
import ReactPlayer from "react-player";
import { get, map } from "lodash";
import { MenuItem, Input } from "@material-ui/core";

import CustomSelect from "../inputs/custom_select";
import CustomTextField from "../../components/inputs/custom_textfields";
import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import WaterCoolerStyles from "./styles/water_cooler_styles";
import CustomTextArea from "../inputs/custom_text_area";
import CustomModal from "../../components/inputs/custom_modal";
import CustomScrollbars from "../data_display/custom_scroll";
import Chevron_Down_Icon from "../data_display/icons/Arrow_Chevron_Down";
import CropperModal from "../modals/cropper_modal";
import RadioButton from "../inputs/custom_radio_button";

import { MAX_UPLOAD_SIZE } from "../../constants";
import { color } from "../../utilities/themes";

function WaterCoolerBlock(props) {
  const uploadRef = useRef(null);

  const {
    classes,
    block,
    onBlur,
    index,
    errorStyles,
    handleDeleteWaterCooler,
    totalRanges,
    onPositionChange,
  } = props;
  const [uploadedImage, setUploadedImage] = useState({});
  const [values, setValues] = useState({
    playVideo: false,
    playerLink: null,
    uploadedImage: {},
    errors: { summary: {} },
    openCropperModal: false,
    hover: false,
  });
  const { errors, playVideo, playerLink, openCropperModal, hover } = values;

  useEffect(() => {
    if (block.headShotLink) {
      setUploadedImage({ ...uploadedImage, image: block.headShotLink });
    }
  }, []);

  const imageUpload = (e) => {
    e.preventDefault();
    let file = e.target.files[0];
    if (!file) {
      return;
    }
    uploadRef.current.value = "";
    if (!file.type.includes("image")) {
      errors.image = {
        error: true,
        message: "Upload JPG or PNG, Max 2MB",
      };
      setValues({ ...values, errors });
      return;
    }

    if (file.size > MAX_UPLOAD_SIZE) {
      const { errors } = values;
      errors.image = {
        error: true,
        message: "Upload JPG or PNG, Max 2MB",
      };
      setValues({ ...values, errors });
      return;
    }

    setUploadedImage({ ...uploadedImage, cImage: URL.createObjectURL(file) });
    setValues({
      ...values,
      openCropperModal: true,
      errors: {
        ...errors,
        image: {},
      },
    });
  };

  const handlePlayVideo = (link) => () => {
    setValues({ ...values, playVideo: !values.playVideo, playerLink: link });
  };

  const handleSummaryError = (obj) => {
    /* 
        MULTIPLE TEXT AREA FIELDS
        error.summary ={index:error} --> index: current index, error: boolean
    */
    const { summary } = errors;
    summary[obj.index] = obj.error;
    props.handleSummaryError({ index: obj.index, error: obj.error });
    setValues({
      ...values,
      errors: {
        ...errors,
        summary: { ...errors.summary, [obj.index]: obj.error },
      },
    });
  };

  const handleImageClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    uploadRef.current.click();
  };

  const returnRangesDropdown = () => {
    return totalRanges.map((range, idx) => {
      return (
        <MenuItem
          value={idx + 1}
          name="position"
          id={get(block, "_id", block.id)}
          onMouseUp={onPositionChange(block, idx + 1)}
        >
          <Text size={13} family="avenir_black_r">
            {idx + 1}
          </Text>
        </MenuItem>
      );
    });
  };

  const setCroppedImage = (imageUrl, imageFile) => {
    setUploadedImage({ image: imageUrl, imageFile });
    if (props.imageUpload) {
      props.imageUpload({ image: imageUrl, imageFile }, block);
    }
    return;
  };

  const handleCropperModal = () => {
    setValues({ ...values, openCropperModal: !values.openCropperModal });
  };

  const clearImage = () => {
    setUploadedImage({ image: "", imageFile: "" });
  };

  const handleHover = (hover) => (e) => {
    setValues({ ...values, hover });
  };

  const returnImage = () => {
    if (uploadedImage.image) {
      return uploadedImage.image;
    }
    if (hover) {
      return "assets/images/WATERCOOLER_UPLOAD_INSTRUCTION.png";
    }
    return "assets/images/water_cooler.png";
  };

  const hasError = Object.values(errors.summary).includes(true);
  return (
    <>
      <Row className={classes.indiv_blk}>
        <Row className={classes.container_row}>
          <div className={classes.pos_rel}>
            <img
              src={returnImage()}
              className={
                uploadedImage.image
                  ? classes.circle_img_border
                  : classes.circle_img
              }
              onMouseEnter={handleHover(true)}
              onMouseLeave={handleHover(false)}
              onClick={handleImageClick}
            />
            {block.uploadType === "video" && (
              <img
                src="assets/images/playerIcon.png"
                className={classes.player_icon}
                onClick={block.videoLink && handlePlayVideo(block.videoLink)}
              />
            )}
          </div>
          {get(errors, "image.error", false) && (
            <div className={classes.error_msg_container}>
              <img
                src="assets/images/warning.svg"
                className={classes.warn_icon}
              />
              <Text
                size={15}
                color={color.primary_palette.christmas_red}
                family="gillsans_r"
                className={`${classes.errorMessage} ${errorStyles}`}
              >
                {get(errors, "image.message", "")}
              </Text>
            </div>
          )}
          <Row className={classes.radio_btn_wrap}>
            <RadioButton
              label="Video"
              value="video"
              className={classes.radio_btn}
              checked={block.uploadType === "video"}
              onChange={onBlur(block, "uploadType")}
            />
            <RadioButton
              label="Only Still Image"
              value="image"
              className={classes.radio_btn}
              checked={block.uploadType === "image"}
              onChange={onBlur(block, "uploadType")}
            />
          </Row>
          <Row className={classes.form_blk}>
            <form>
              <CustomTextField
                defaultValue={block.teamMemberName}
                className={classes.inputField}
                label={strings.company_wizard.step3.titles.team_Member_Name}
                onBlur={onBlur(block, "teamMemberName")}
              />
              <CustomTextField
                defaultValue={block.title}
                className={classes.inputField}
                label={strings.company_wizard.step3.titles.team_Title}
                onBlur={onBlur(block, "title")}
              />
              <CustomTextField
                defaultValue={block.videoLink}
                key={block.videoLink}
                disabled={block.uploadType !== "video"}
                className={`${block.videoLinkErr && classes.video_err} ${
                  classes.inputField
                }`}
                label={strings.company_wizard.step3.titles.Video_Link}
                onBlur={onBlur(block, "videoLink")}
              />
              <div style={{ position: "relative" }}>
                <Text className={classes.video_summary}>
                  {strings.company_wizard.step3.titles.Video_Summary}
                </Text>
                <CustomTextArea
                  enableCount
                  callBack={handleSummaryError}
                  index={index}
                  countValue={130}
                  hasError={hasError}
                  defaultValue={block.videoSummery}
                  className={classes.video_text}
                  onBlur={onBlur(block, "videoSummery")}
                />
              </div>
              <Row className={classes.actions_wrap}>
                <Row className={classes.order_wrap}>
                  <Text
                    size={15}
                    family="avenir_black_r"
                    color={color.primary_palette.franklin_purple}
                    fontWeight={900}
                  >
                    ORDER
                  </Text>
                  <CustomSelect
                    IconComponent={Chevron_Down_Icon}
                    id="demo-mutiple-checkbox"
                    input={<Input />}
                    value={get(block, "position", "")}
                    renderValue={(value) => value || ""}
                    className={classes.order_dropdown}
                  >
                    {returnRangesDropdown()}
                  </CustomSelect>
                </Row>
                <Row className={classes.delete_wrap}>
                  <Text
                    size={15}
                    family="avenir_black_r"
                    fontWeight={900}
                    className={!block.teamMemberName && classes.disable_delete}
                    onClick={handleDeleteWaterCooler(block._id)}
                  >
                    DELETE
                  </Text>
                  <img
                    onClick={
                      block.teamMemberName && handleDeleteWaterCooler(block._id)
                    }
                    src="assets/icons/Trash_Can.svg"
                    className={classes.icon}
                    className={!block.teamMemberName && classes.disable_delete}
                  />
                </Row>
              </Row>
            </form>
          </Row>
        </Row>
        <CustomModal
          open={playVideo}
          aria-labelledby="modal-title"
          aria-describedby="modal-description"
          className={classes.modal_popup}
          onClose={handlePlayVideo("")}
        >
          <ReactPlayer
            url={playerLink}
            controls
            height={"262px"}
            width={"429px"}
            playing
            className={classes.videoPlayer}
          />
        </CustomModal>
        <input
          type="file"
          ref={uploadRef}
          onChange={imageUpload}
          accept="image/*"
          style={{ display: "none" }}
        />
      </Row>
      <CustomModal open={openCropperModal}>
        <CropperModal
          shape={"round"}
          image={get(uploadedImage, "cImage", "")}
          setCroppedImage={setCroppedImage}
          chooseNew={handleImageClick}
          closeOnSave
          onClose={handleCropperModal}
          clearImage={clearImage}
          errorMessage={get(errors, "image.message", "")}
        />
      </CustomModal>
    </>
  );
}

export default WaterCoolerStyles(WaterCoolerBlock);
