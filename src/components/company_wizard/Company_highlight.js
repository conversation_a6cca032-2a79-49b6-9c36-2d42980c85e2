import React, { useState, useEffect } from "react";
import { useHistory, Redirect } from "react-router-dom";
import { filter, map, find, get, omit } from "lodash";
import { useSelector, useDispatch } from "react-redux";

import LayoutOne from "./company_layouts/layout1";
import LayoutTwo from "./company_layouts/layout2";
import LayoutThree from "./company_layouts/layout3";
import LayoutFour from "./company_layouts/layout4";
import LayoutFive from "./company_layouts/layout5";
import Row from "../common/ui_kit/row";
import Text from "../common/ui_kit/text";
import strings from "../../utilities/strings";
import highlightStyle from "./styles/highlight_style";
import CustomCheckbox from "../inputs/custom_red_checkbox";
import CustomButton from "../navigations/custom_buttons";
import JoinUsSuccessModal from "../modals/new_companyModal";
import {
  PROFILE_TYPES,
  COMPANY_CULTURE_DESC_MINLENGTH,
  BASEURL,
} from "../../constants";
import { ProfileActions } from "../../redux/actions";
import { pxToRem } from "../../utilities/themes";

function CompanyHighlight(props) {
  const [values, setValues] = useState({
    selectedLayouts: [],
    layouts: [],
    errors: {},
    tmcWarning: false,
    showSuccessModal: false,
    displayWelcome: false,
  });
  const history = useHistory();
  const { classes } = props;
  const { layouts, tmcWarning, displayWelcome } = values;
  const selectedLayouts = map(layouts, (eachLayout) => {
    return eachLayout.layoutType;
  });

  const companyHighlights = useSelector(
    (state) => state.Profile.companyhighlights
  );

  const businessCardData = useSelector(
    (state) => state.Profile.refBusinessCard
  );

  const dispatch = useDispatch();
  const fetchData = () => {
    dispatch(
      ProfileActions.getCompanyProfileData([PROFILE_TYPES.COMPANY_HIGHLIGHTS])
    );
  };

  const setInitialData = () => {
    if (companyHighlights.length) {
      const formattedData = map(companyHighlights, (eachLayout) => {
        const { url } = eachLayout;
        const formattedUrl = url.map((eachUrl) => {
          return { image: eachUrl };
        });
        return { ...eachLayout, url: [...formattedUrl] };
      });
      setValues({ ...values, layouts: [...formattedData] });
    } else {
      // IF NEW OR NO DATA IS AVAILABLE SELECTING LAYOUT 1 BY DEFAULT
      setValues({
        ...values,
        layouts: [{ layoutType: "layout1", id: 1 }],
      });
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (companyHighlights) {
      setInitialData();
    }
  }, [companyHighlights]);

  const handleSelectLayouts = (id) => (e) => {
    const { layouts } = values;
    const selectedLayoutType = e.target.name;

    const layoutTypes = map(layouts, (eachLayout) => {
      return eachLayout.layoutType;
    });

    if (layoutTypes.includes(selectedLayoutType)) {
      const updatedArray = filter(layouts, (eachLayout) => {
        return eachLayout.layoutType !== selectedLayoutType;
      });
      setValues({ ...values, layouts: [...updatedArray] });
    } else {
      setValues({
        ...values,
        layouts: [...layouts, { id: id, layoutType: selectedLayoutType }],
      });
    }
  };

  const handleValidation = (foundLayout, name) => {
    foundLayout.errors = foundLayout.errors || {};
    switch (name) {
      case "title": {
        if (foundLayout.title) {
          return { ...foundLayout.errors, [name]: false };
        }
        return { ...foundLayout.errors, [name]: true };
      }
      case "paragraph1": {
        if (
          foundLayout.paragraph1.length < 100 ||
          foundLayout.paragraph1.length > 450
        ) {
          return {
            ...foundLayout.errors,
            [name]: true,
            tmc: foundLayout.paragraph1.length > 450,
          };
        }
        return { ...foundLayout.errors, [name]: false, tmc: false };
      }
      case "paragraph2": {
        if (
          foundLayout.paragraph2.length < 100 ||
          foundLayout.paragraph2.length > 450
        ) {
          return {
            ...foundLayout.errors,
            [name]: true,
            tmc: foundLayout.paragraph2.length > 450,
          };
        }
        return { ...foundLayout.errors, [name]: false, tmc: false };
      }
      case "content": {
        const lengths = {
          layout1: 1100,
          layout3: 750,
          layout4: 450,
          layout5: 450,
        };
        if (
          foundLayout.content.length < 100 ||
          foundLayout.content.length > lengths[foundLayout.layoutType]
        ) {
          return {
            ...foundLayout.errors,
            [name]: true,
            tmc: foundLayout.content.length > lengths[foundLayout.layoutType],
          };
        }
        return { ...foundLayout.errors, [name]: false, tmc: false };
      }
      case "url": {
        if (foundLayout.url.length > 0) {
          return { ...foundLayout.errors, [name]: false };
        }
        return { ...foundLayout.errors, [name]: true };
      }
      default:
        return { ...foundLayout.errors, [name]: false };
    }
  };

  const handleInputChange = (type) => (e) => {
    const foundLayout = find(layouts, { layoutType: type });
    if (foundLayout) {
      foundLayout[e.target.name] = e.target.value.trim();
      foundLayout.errors = handleValidation(foundLayout, e.target.name);
    }
    const tmcLayouts = filter(layouts, (layout) => {
      return layout.errors && layout.errors.tmc;
    });
    setValues({ ...values, layouts, tmcWarning: tmcLayouts.length > 0 });
  };

  const imageUpload = (imageData) => {
    const { index, id } = imageData;
    const { layouts } = values;
    const layout = find(layouts, { layoutType: id });
    if (layout) {
      layout.url = layout.url || [];
      layout.url[index] = imageData;
    }
    setValues({ ...values });
  };

  const validateMandatoryImage = (layout, index) => {
    if (layout) {
      if (layout.content && layout.title) {
        if (!layout.url) {
          return true;
        }
        if (index && !layout.url[index]) {
          return true;
        }
        if (layout.url.length === 0) {
          return true;
        }
      }
    }
    return false;
  };

  const checkFormErrors = () => {
    const { layouts } = values;
    for (let layoutIndex = 0; layoutIndex < layouts.length; layoutIndex++) {
      const layout = layouts[layoutIndex];
      const title = get(layout, "title", "").length;
      const layoutType = get(layout, "layoutType", "");
      const content = get(layout, "content", "").length;
      const paragraph1 = get(layout, "paragraph1", "").length;
      const paragraph2 = get(layout, "paragraph2", "").length;
      const url = get(layout, "url", []);
      const errors = Object.values(get(layout, "errors", {}));
      if (errors.includes(true)) {
        // INPUT CHANGE VALIDATIONS
        return true;
      }

      if (title > 150 || !title) {
        // TITLE VALIDATION
        return true;
      }
      if (layoutType === "layout1") {
        if (content > 1100 || !content) {
          return true;
        }
        if (url.length === 0) {
          // IF IMAGES NOT UPLOADED
          return true;
        }
      }
      if (layoutType === "layout2") {
        if (url.length < 2) {
          // IF IMAGES NOT UPLOADED
          return true;
        }
        if (
          paragraph1 > 450 ||
          paragraph2 > 450 ||
          !paragraph1 ||
          !paragraph2
        ) {
          return true;
        }
      }
      if (layoutType === "layout3") {
        if (url.length < 2) {
          // IF IMAGES NOT UPLOADED
          return true;
        }
        if (!content || content > 750) {
          return true;
        }
      }
      if (layoutType === "layout4" || layoutType === "layout5") {
        if (url.length === 0) {
          // IF IMAGES NOT UPLOADED
          return true;
        }
        if (!content || content > 750) {
          return true;
        }
      }
      if (layoutIndex === layouts.length - 1) return false;
    }
  };
  const handleFinishLater = () => {
    // POSTING BUSINESSCARD DATA IF SKIPPING
    const { businessCard, aboutMe } = businessCardData;
    const formattedBusinessCard = filter(businessCard, (card) => {
      return card.companyId.name;
    }).map((card) => {
      const omitData = omit(card, "_id");
      return {
        ...omitData,
        companyId: get(card, "companyId._id", ""),
        companyName: get(card, "companyId.name", ""),
        selectedOffice: get(card, "selectedOffice.city", ""),
        id: "",
      };
    });
    const dataToSubmit = {
      ...businessCardData,
      businessCard: formattedBusinessCard,
      aboutMe: {
        ...businessCardData.aboutMe,
        myOffice: get(aboutMe, "myOffice.city", ""),
      },
    };
    dispatch(
      ProfileActions.updateBusinessCard(dataToSubmit, (res) => {
        if (!res.error) {
          dispatch(
            ProfileActions.updateCompanyHighlights([], (res) => {
              if (!res.error) {
                setValues({ ...values, displayWelcome: true });
              }
            })
          );
        }
      })
    );
  };

  const handleSubmit = () => {
    const { layouts } = values;
    const { edit } = props;
    if (!edit) {
      const { businessCard, aboutMe } = businessCardData;
      const formattedBusinessCard = filter(businessCard, (card) => {
        return card.companyId.name;
      }).map((card) => {
        const omitData = omit(card, "_id");
        return {
          ...omitData,
          companyId: get(card, "companyId._id", ""),
          companyName: get(card, "companyId.name", ""),
          selectedOffice: get(card, "selectedOffice.city", ""),
          id: "",
        };
      });
      const dataToSubmit = {
        ...businessCardData,
        businessCard: formattedBusinessCard,
        aboutMe: {
          ...businessCardData.aboutMe,
          myOffice: get(aboutMe, "myOffice.city", ""),
        },
      };
      dispatch(
        ProfileActions.updateCompanyHighlights(layouts, (res) => {
          if (!res.error) {
            setValues({ ...values, displayWelcome: true });
          }
        })
      );
      // dispatch(
      //   ProfileActions.updateBusinessCard(dataToSubmit, (res) => {
      //     if (!res.error) {
      //       dispatch(
      //         ProfileActions.updateCompanyHighlights(layouts, (res) => {
      //           if (!res.error) {
      //             setValues({ ...values, displayWelcome: true });
      //           }
      //         })
      //       );
      //     }
      //   })
      // );
    } else {
      dispatch(
        ProfileActions.updateCompanyHighlights(layouts, (res) => {
          if (!res.error) {
            history.push("twc/profile");
          }
        })
      );
    }
  };

  const handleSkip = () => {
    setValues({ ...values, displayWelcome: true });
  };

  const redirectToWpack = () => {
    let host = get(window, "location.host", "");
    let token = localStorage.getItem("tradeworks_user_token");
    localStorage.setItem("isPostNewJob", true);
    localStorage.removeItem("isFromCats");
    let companyId = localStorage.getItem("companyId");
    if (host && companyId && companyId != "") {
      host = `${BASEURL.URL}/wpack`;
      window.location.href = `${host}/${token}/${companyId}`;
      // if (host.includes("localhost")) {
      //   host = "localhost:3003";
      //   window.location.href = `http://${host}/${token}/${companyId}`;
      // } else {
      //   if (host.includes("-dev-")) {
      //     host = TDW_URL.WPACK.DEV;
      //   }
      //   if (host.includes("-qa-")) {
      //     host = TDW_URL.WPACK.QA;
      //   }
      //   if (host.includes("-stage-")) {
      //     host = TDW_URL.WPACK.STAGE;
      //   } else {
      //     host = "http://twwstage.franklinreport.com/wpack";
      //   }
      //   window.location.href = `${host}/${token}/${companyId}`;
      // }
    }
  };

  const saveDataRedirect = () => {
    const { layouts } = values;
    const { edit } = props;
    if (!edit) {
      const { businessCard, aboutMe } = businessCardData;
      const formattedBusinessCard = filter(businessCard, (card) => {
        return card.companyId.name;
      }).map((card) => {
        const omitData = omit(card, "_id");
        return {
          ...omitData,
          companyId: get(card, "companyId._id", ""),
          companyName: get(card, "companyId.name", ""),
          selectedOffice: get(card, "selectedOffice.city", ""),
          id: "",
        };
      });
      const dataToSubmit = {
        ...businessCardData,
        businessCard: formattedBusinessCard,
        aboutMe: {
          ...businessCardData.aboutMe,
          myOffice: get(aboutMe, "myOffice.city", ""),
        },
      };
      dispatch(
        ProfileActions.updateCompanyHighlights(layouts, (res) => {
          if (!res.error) {
            setValues({ ...values, displayWelcome: true });
          }
        })
      );
      // dispatch(
      //   ProfileActions.updateBusinessCard(dataToSubmit, (res) => {
      //     if (!res.error) {
      //       dispatch(
      //         ProfileActions.updateCompanyHighlights(layouts, (res) => {
      //           if (!res.error) {
      //             setValues({ ...values, displayWelcome: true });
      //           }
      //         })
      //       );
      //     }
      //   })
      // );
    } else {
      dispatch(
        ProfileActions.updateCompanyHighlights(layouts, (res) => {
          if (!res.error) {
            redirectToWpack();
          }
        })
      );
    }
  };

  const redirectToEditProfile = () => {
    history.push("twc/profile");
    localStorage.removeItem("isSetNewCompany");
  };
  const redirectToProfile = () => {
    history.push("twc/profile");
    localStorage.removeItem("isSetNewCompany");
  };
  const { edit } = props;
  const submitErrors = checkFormErrors();
  return (
    <>
      <Row className={classes.company_highlight}>
        <Row className={classes.center_text}>
          <Text className={classes.main_des}>
            {strings.company_wizard.step5.titles.step5_title}
          </Text>
          <Text className={classes.main_brand}>
            {strings.company_wizard.step5.titles.step5_title_des}
          </Text>
        </Row>
        {edit && (
          <>
            <CustomButton
              className={classes.logo_btn_red}
              onClick={saveDataRedirect}
              style={{ float: "right" }}
            >
              POST A JOB NOW
            </CustomButton>
            <CustomButton
              variant="outlined"
              color="primary"
              disabled={submitErrors}
              className={classes.greenactionButtons}
              onClick={handleSubmit}
              style={{
                width: edit && pxToRem(240),
                position: "absolute",
                right: "140px",
                top: "32px",
              }}
            >
              {edit
                ? "Save & Go back to Profile"
                : strings.general.titles.save_continue}
            </CustomButton>
          </>
        )}
        <Row className={classes.main_head}>
          <Text className={classes.step_2a}>
            {strings.company_wizard.step5.titles.Step5}
          </Text>
          <Text className={classes.step_2a_des}>
            {strings.company_wizard.step5.titles.step5_des}
          </Text>
        </Row>
        <Row className={classes.main_head}>
          <Text className={classes.step_a_b}>
            {strings.company_wizard.step5.titles.step_A}
          </Text>
        </Row>
        <Row className={classes.layouts}>
          <Row className={classes.layout_spacing}>
            <CustomCheckbox
              name="layout1"
              label="layout 1"
              onChange={handleSelectLayouts(1)}
              checked={selectedLayouts.includes("layout1")}
            />
            <Row>
              <img
                src={
                  selectedLayouts.includes("layout1")
                    ? "assets/images/Layout_1_Purple.png"
                    : "assets/images/Layout_1_Gray.png"
                }
                className={classes.layout_img}
              />
            </Row>
          </Row>
          <Row className={classes.layout_spacing}>
            <CustomCheckbox
              name="layout2"
              label="layout 2"
              onChange={handleSelectLayouts(2)}
              checked={selectedLayouts.includes("layout2")}
            />
            <Row>
              <img
                src={
                  selectedLayouts.includes("layout2")
                    ? "assets/images/Layout_2_Purple.png"
                    : "assets/images/Layout_2_Gray.png"
                }
                className={classes.layout_img}
              />
            </Row>
          </Row>
          <Row className={classes.layout_spacing}>
            <CustomCheckbox
              name="layout3"
              label="layout 3"
              onChange={handleSelectLayouts(3)}
              checked={selectedLayouts.includes("layout3")}
            />
            <Row>
              <img
                src={
                  selectedLayouts.includes("layout3")
                    ? "assets/images/Layout_3_Purple.png"
                    : "assets/images/Layout_3_Gray.png"
                }
                className={classes.layout_img}
              />
            </Row>
          </Row>
          <Row className={classes.layout_spacing}>
            <CustomCheckbox
              name="layout4"
              label="layout 4"
              onChange={handleSelectLayouts(4)}
              checked={selectedLayouts.includes("layout4")}
            />
            <Row>
              <img
                src={
                  selectedLayouts.includes("layout4")
                    ? "assets/images/Layout_4_Purple.png"
                    : "assets/images/Layout_4_Gray.png"
                }
                className={classes.layout_img_pattern}
              />
            </Row>
          </Row>
          <Row className={classes.layout_spacing}>
            <CustomCheckbox
              name="layout5"
              label="layout 5"
              onChange={handleSelectLayouts(5)}
              checked={selectedLayouts.includes("layout5")}
            />
            <Row>
              <img
                src={
                  selectedLayouts.includes("layout5")
                    ? "assets/images/Layout_5_Purple.png"
                    : "assets/images/Layout_5_Gray.png"
                }
                className={classes.layout_img_pattern}
              />
            </Row>
          </Row>
        </Row>
        <Row className={classes.main_head}>
          <Text className={classes.step_a_b}>
            {strings.company_wizard.step5.titles.step_B}
          </Text>
        </Row>
        <Row className={classes.company_highlight}>
          {selectedLayouts.includes("layout1") && (
            <Row className={classes.justify}>
              <LayoutOne
                layouts={layouts}
                imageUpload={imageUpload}
                edit={edit}
                id="layout1"
                handleChange={handleInputChange}
                validateMandatoryImage={validateMandatoryImage}
              />
            </Row>
          )}
          {selectedLayouts.includes("layout2") && (
            <Row className={classes.justify}>
              <LayoutTwo
                layouts={layouts}
                imageUpload={imageUpload}
                edit={edit}
                id="layout2"
                handleChange={handleInputChange}
                validateMandatoryImage={validateMandatoryImage}
              />
            </Row>
          )}
          {selectedLayouts.includes("layout3") && (
            <Row className={classes.justify}>
              <LayoutThree
                layouts={layouts}
                handleChange={handleInputChange}
                edit={edit}
                imageUpload={imageUpload}
                id="layout3"
                validateMandatoryImage={validateMandatoryImage}
              />
            </Row>
          )}
          {selectedLayouts.includes("layout4") && (
            <Row className={classes.justify}>
              <LayoutFour
                layouts={layouts}
                handleChange={handleInputChange}
                edit={edit}
                imageUpload={imageUpload}
                id="layout4"
                validateMandatoryImage={validateMandatoryImage}
              />
            </Row>
          )}
          {selectedLayouts.includes("layout5") && (
            <Row className={classes.justify}>
              <LayoutFive
                layouts={layouts}
                edit={edit}
                handleChange={handleInputChange}
                imageUpload={imageUpload}
                id="layout5"
                validateMandatoryImage={validateMandatoryImage}
              />
            </Row>
          )}
        </Row>
        <Row className={classes.btn_blk}>
          <Row>
            {!edit && (
              <customLink
                className={classes.later_btn}
                onClick={handleFinishLater}
              >
                {strings.general.titles.finish_later}
              </customLink>
            )}
          </Row>
          <Row className={classes.btn_alignment}></Row>
          <Row className={classes.btn_alignment}>
            {submitErrors && (
              <Row className={`${classes.cw_error} ${classes.fill_all_error}`}>
                <img src="assets/icons/info_1.svg" className={classes.info} />{" "}
                &nbsp;
                {strings.company_wizard.step2.titles.errorMsgLayout}
              </Row>
            )}
            <CustomButton
              variant="outlined"
              color="primary"
              disabled={submitErrors}
              className={`${classes.actionButtons} ${classes.skip_btn}`}
              onClick={handleSkip}
            >
              {strings.general.titles.skip}
            </CustomButton>
            {tmcWarning && (
              <img
                src="assets/images/TMI_WARNING.png"
                className={classes.tmcWarning}
              />
            )}
            <CustomButton
              variant="outlined"
              color="primary"
              disabled={submitErrors}
              className={classes.greenactionButtons}
              onClick={handleSubmit}
              style={{ width: edit && pxToRem(240) }}
            >
              {edit
                ? "Save & Go back to Profile"
                : strings.general.titles.save_continue}
            </CustomButton>
          </Row>
        </Row>
      </Row>
      {displayWelcome && (
        <JoinUsSuccessModal
          redirectToEditProfile={redirectToEditProfile}
          redirectToProfile={redirectToProfile}
        />
      )}
    </>
  );
}

export default highlightStyle(CompanyHighlight);
