import { withStyles } from "@material-ui/core/styles";

import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  office_shot: {
    display: "block",
  },
  center_text: {
    display: "block",
    width: "100%",
    textAlign: "center",
  },
  errorMessage: { marginLeft: pxToRem(5) },
  main_des: {
    ...tradework_theme.typography.styles.gillsans_light,
    fontSize: pxToRem(35),
    textTransform: "uppercase",
  },
  main_brand: {
    ...tradework_theme.typography.styles.avenir_roman,
    fontSize: pxToRem(20),
  },
  main_head: {
    display: "block",
    paddingLeft: pxToRem(90),
    paddingRight: pxToRem(90),
  },
  step_2a: {
    fontSize: pxToRem(25),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.gillsans_sb,
    textTransform: "uppercase",
    height: pxToRem(30),
  },
  step_2a_des: {
    fontSize: pxToRem(16),
    color: color.secondary_palette.grays.up_in_smoke,
    ...tradework_theme.typography.styles.avenir_roman,
    paddingBottom: pxToRem(5),
    paddingLeft: pxToRem(5),
  },
  offices: {
    color: color.form_colors.royal_purple_1,
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_black_r,
    paddingRight: pxToRem(90),
    paddingLeft: pxToRem(90),
    paddingTop: pxToRem(12),
  },
  country_name: {
    fontSize: pxToRem(25),
    ...tradework_theme.typography.styles.avenir_light,
    textTransform: "uppercase",
    color: color.greyish_brown,
    width: pxToRem(635),
    margin: "0 auto",
  },
  helper_name: {
    fontSize: pxToRem(14),
    ...tradework_theme.typography.styles.avenir_sb,
    color: color.primary_palette.black,
    width: pxToRem(635),
    margin: "0 auto",
  },
  helper_name_paddingTop: {
    fontSize: pxToRem(14),
    ...tradework_theme.typography.styles.avenir_sb,
    color: color.primary_palette.black,
    paddingTop: pxToRem(48),
    width: pxToRem(635),
    margin: "0 auto",
  },
  country_name_paddingTop: {
    fontSize: pxToRem(25),
    ...tradework_theme.typography.styles.avenir_light,
    textTransform: "uppercase",
    color: color.greyish_brown,
    paddingTop: pxToRem(48),
    width: pxToRem(635),
    margin: "0 auto",
  },
  country_grid: {
    paddingBottom: pxToRem(48),
    display: "block",
    margin: "0 auto",
    width: "79%",
  },
  main_grid: {
    width: "50%",
    margin: "0 auto",
    display: "block",
  },
  later_btn: {
    justifyContent: "flex-start",
    color: color.finish_color,
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_roman,
    cursor: "pointer",
    textDecoration: "underline",
    display: "flex",
    flexGrow: 1,
    "&:hover": {
      color: color.primary_palette.franklin_purple,
    },
  },
  actionButtons: {
    padding: 0,
    color: color.primary_palette.franklin_purple,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.franklin_purple,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.franklin_purple} !important`,
      border: `1px solid ${color.primary_palette.franklin_purple}`,
    },
  },
  greenactionButtons: {
    padding: 0,
    color: color.primary_palette.pine_green,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.pine_green,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.pine_green} !important`,
      border: `1px solid ${color.primary_palette.pine_green}`,
    },
  },
  // london_block
  london_1: {
    width: pxToRem(204),
    height: pxToRem(248),
  },
  // paris_block
  paris_block: {
    display: "block",
  },
  paris_1: {
    width: pxToRem(204),
    height: pxToRem(120),
  },
  paris_2: {
    width: pxToRem(204),
    height: pxToRem(120),
  },
  paris_3: {
    width: pxToRem(415),
    height: pxToRem(248),
  },
  // new_york_block
  newYork_block: {
    display: "block",
  },
  newYork_1: {
    width: pxToRem(415),
    height: pxToRem(248),
  },
  newYork_2: {
    width: pxToRem(204),
    height: pxToRem(120),
  },
  newYork_3: {
    width: pxToRem(203),
    height: pxToRem(120),
  },

  // sidney_blk
  sidney_blk: {
    display: "block",
  },
  sidney_1: {
    width: pxToRem(204),
    height: pxToRem(248),
  },
  sidney_2: {
    width: pxToRem(415),
    height: pxToRem(248),
  },
  tokyo_1: {
    width: pxToRem(204),
    height: pxToRem(248),
  },
  tokyo_middle: {
    width: pxToRem(204),
    height: pxToRem(120),
  },
  margin_10: {
    marginRight: pxToRem(9),
    marginLeft: pxToRem(9),
  },
  marginLeft_9: {
    marginLeft: pxToRem(9),
  },
  marginRight_9: {
    marginRight: pxToRem(9),
  },
  padding_t_36: {
    paddingTop: pxToRem(36),
  },
  margin_left_17: {
    marginLeft: "17%",
  },
  btn_blk: {
    width: pxToRem(627),
    margin: "0 auto",
    paddingTop: pxToRem(79),
    paddingBottom: pxToRem(123),
  },
  btn_blk_right: {
    justifyContent: "flex-end",
    flexGrow: 1,
  },
  first_btn_width: {
    width: pxToRem(75),
    height: pxToRem(25),
  },
  second_btn_width: {
    width: pxToRem(187),
    height: pxToRem(25),
    margin: 0,
  },
  justify: {
    justifyContent: "center",
  },
  block_relative: {
    display: "block",
    position: "relative",
  },
  errorStyles: {
    justifyContent: "center",
    paddingTop: pxToRem(10),
  },
  logo_btn_red: {
    border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    borderRadius: pxToRem(27),
    fontSize: pxToRem(15),
    position: "absolute",
    top: "90px",
    right: "40px",
    marginRight: pxToRem(150),
    ...tradework_theme.typography.styles.avenir_sb,
    color: `${color.primary_palette.christmas_red} !important`,
    textTransform: "uppercase",
    padding: `${pxToRem(1)} ${pxToRem(9)}`,
    height: pxToRem(25),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
      verticalAlign: "middle",
      marginRight: pxToRem(10),
    },
    "&:hover": {
      color: `${color.primary_palette.christmas_red} !important`,
      backgroundColor: "#FFFFFF !important",
      border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    },
  },
});

export default styles;
