import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  txt_center: { textAlign: "center" },
  txt_right: { textAlign: "right" },
  padding_open_pos: {
    marginTop: `${pxToRem(3)}`,
    paddingBottom: `${pxToRem(3)}`,
  },
  container: {
    position: "relative",
    width: "95%",
    border: `solid ${pxToRem(1.1)} ${color.primary_palette.black}`,
    marginBottom: pxToRem(20),
    paddingBottom: pxToRem(10),
  },
  cover_image: {
    width: "100%",
    height: pxToRem(130),
  },
  step1_subheading: {
    paddingBottom: pxToRem(9),
  },
  profile_image_container: {
    width: "20%",
    textAlign: "center",
    position: "relative",
    top: pxToRem(20),
  },
  profile_image: {
    width: pxToRem(70),
    height: pxToRem(70),
    border: `solid ${pxToRem(1.1)} ${color.primary_palette.black}`,
    borderRadius: "100%",
    position: "absolute",
    top: pxToRem(105),
    left: pxToRem(20),
  },
  section_2: {
    height: pxToRem(60),
  },
  profile_image_section: {
    width: "20%",
  },
  section_2_profession: {
    width: "60%",
    margin: `${pxToRem(10)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(5)}`,
  },
  section_2_location: {
    paddingTop: pxToRem(15),
    paddingRight: pxToRem(5),
    width: "40%",
    textAlign: "right",
  },
  profile_section_right: {
    float: "right",
    width: "30%",
    marginRight: pxToRem(10),
  },
  profile_section_left: {
    float: "left",
    width: "65%",
    marginleft: pxToRem(10),
  },
  open_positions: {
    width: "100%",
    height: pxToRem(170),
    border: `solid ${pxToRem(1.1)} ${color.primary_palette.black}`,
    paddingBottom: pxToRem(5),
  },
  franklin_report_card: {
    width: "100%",
    position: "relative",
    height: pxToRem(105),
    border: `solid ${pxToRem(1.1)} ${color.primary_palette.black}`,
  },
  full_report: {
    borderBottom: `solid ${pxToRem(1.1)} ${
      color.secondary_palette.purples.franklin_purple
    }`,
    marginRight: pxToRem(5),
  },
  contact_us: {
    width: "100%",
    height: pxToRem(120),
    position: "relative",
    border: `solid ${pxToRem(1.1)} ${color.primary_palette.black}`,
  },
  contact_us_content: {
    width: "87%",
    height: "80%",
    margin: pxToRem(10),
  },
  contact_us_titles: {
    width: "100%",
    padding: `${pxToRem(4)} ${pxToRem(0)}`,
  },
  contact_us_con_padding: {
    padding: `${pxToRem(2)} ${pxToRem(0)}`,
  },
  contact_us_border_bottom: {
    borderBottom: `${pxToRem(0.5)} solid ${
      color.secondary_palette.grays.shadow_gray
    }`,
  },
  company_feeds: {
    width: "100%",
    height: pxToRem(200),
    position: "relative",
    border: `solid ${pxToRem(1.1)} ${color.primary_palette.black}`,
  },
  open_pos_title: {
    padding: `${pxToRem(7)} ${pxToRem(7)} ${pxToRem(0)} ${pxToRem(7)}`,
  },
  section_titles: {
    padding: `${pxToRem(0)} ${pxToRem(5)} ${pxToRem(0)} ${pxToRem(5)}`,
    background: "white",
    display: "inline",
    position: "absolute",
    top: pxToRem(-8),
    marginLeft: pxToRem(5),
  },
  open_pos_input: {
    // marginLeft: pxToRem(5),
    // marginRight: pxToRem(5),
    width: "95%",
    fontSize: pxToRem(7),
    height: pxToRem(10),
    ...tradework_theme.typography.styles.avenir_sb,
  },
  open_pos_left: {
    width: "70%",
    textAlign: "left",
    paddingLeft: pxToRem(5),
  },
  open_pos_right: {
    width: "30%",
    textAlign: "right",
    paddingRight: pxToRem(5),
  },
  franklin_content: {
    width: "100%",
    textAlign: "center",
    marginTop: pxToRem(10),
  },
  open_pos_left_franklin: {
    width: "50%",
    display: "flex",
    paddingLeft: pxToRem(10),
  },
  open_pos_right_franklin: {
    width: "50%",
    display: "flex",
    justifyContent: "flex-start",
    "& img": {
      width: pxToRem(12),
      height: pxToRem(12),
      verticalAlign: "top",
    },
  },
  section_highlight_profile: {
    width: pxToRem(70),
    height: pxToRem(70),
    borderRadius: "100%",
    border: `solid ${pxToRem(3)} ${
      color.primary_palette.highlight_purple
    } !important`,
    marginBottom: pxToRem(10),
  },
  section_highlight: {
    borderRadius: pxToRem(1),
    marginBottom: pxToRem(10),
    border: `solid ${pxToRem(3)} ${color.profile_parts.logo_cover} !important`,
  },
  logo_cover:{
    border: `solid ${pxToRem(3)} ${color.profile_parts.logo_cover} !important`,
  },  
  company_pitch:{
    border: `solid ${pxToRem(3)} ${color.profile_parts.company_pitch} !important`,
  },  
  water_cooler:{
    border: `solid ${pxToRem(3)} ${color.profile_parts.water_cooler} !important`,
  },  
  our_offices:{
    border: `solid ${pxToRem(3)} ${color.profile_parts.our_offices} !important`,
  },  
  company_culture:{
    border: `solid ${pxToRem(3)} ${color.profile_parts.company_culture} !important`,
  },  
  team_alumni:{
    border: `solid ${pxToRem(3)} ${color.profile_parts.team_alumni} !important`,
  },
  overflow_hidden: {
    overflow: "hidden",
  },
  social_media_icons: {
    fontSize: pxToRem(15),
    paddingLeft: pxToRem(10),
    paddingTop: pxToRem(15),
  },
  section_tab: {
    padding: `${pxToRem(7)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(0)}`,
    width: "30%",
    textAlign: "center",
  },
  tab_highlight: {
    borderBottom: `solid ${pxToRem(2)} ${
      color.primary_palette.highlight_purple
    }`,
  },
  tabs_1_margin: {
    marginLeft: pxToRem(20),
  },
  margin_bottom_10: {
    marginBottom: pxToRem(10),
  },
  display_inline: {
    display: "inline",
  },
  border_bottom_about: {
    borderBottom: `${pxToRem(2)} ${color.primary_palette.highlight_purple}`,
  },
  about_images: {
    width: "50%",
    height: pxToRem(225),
    marginTop: pxToRem(5),
    marginLeft: pxToRem(7),
  },
  p_top_10: { paddingTop: pxToRem(10) },
  about_image: {
    height: "50%",
    width: "100%",
  },
  about_content: {
    width: "55%",
    height: pxToRem(245),
    marginTop: pxToRem(5),
    padding: `${pxToRem(5)} ${pxToRem(5)}`,
  },
  about_content_1: {
    width: "40%",
    height: pxToRem(230),
    padding: pxToRem(5),
    margin: `${pxToRem(5)} ${pxToRem(5)}`,
  },
  accolade_images: {
    height: pxToRem(30),
    marginLeft: pxToRem(5),
    width: "20%",
  },
  accolade_image_container: {
    borderBottom: `${pxToRem(1)} solid ${
      color.secondary_palette.grays.border_gray
    }`,
    paddingBottom: pxToRem(10),
    margin: `${pxToRem(15)} ${pxToRem(5)} ${pxToRem(0)} ${pxToRem(5)}`,
  },
  about_end_left: {
    width: "60%",
  },
  about_end_right: {
    width: "40%",
  },
  about_chips: {
    width: "40%",
  },
  about_chip: {
    background: `${color.form_colors.chip_color}`,
    padding: `${pxToRem(0)} ${pxToRem(2)} ${pxToRem(2)} ${pxToRem(2)}`,
    borderRadius: pxToRem(50),
    fontFamily: "gillsans_r",
    color: color.greyish_brown,
  },
  company_keywords: {
    padding: `${pxToRem(5)} ${pxToRem(0)} ${pxToRem(3)} ${pxToRem(0)}`,
  },
  margin_left_10: {
    marginLeft: pxToRem(5),
  },
  franklinSection_data: {
    paddingBottom: pxToRem(5),
    height: pxToRem(15),
  },
  water_cooler_container: {
    width: "100%",
    height: pxToRem(155),
    marginLeft: pxToRem(10),
  },
  player_icon: {
    width: pxToRem(20),
    height: pxToRem(20),
    position: "absolute",
    right: pxToRem(30),
    top: pxToRem(60),
  },
  our_offices_containter: {
    width: "100%",
    height: pxToRem(180),
    paddingLeft: pxToRem(10),
  },
  water_cooler_title: {
    width: "100%",
    height: pxToRem(12),
    paddingBottom: pxToRem(2),
    borderBottom: `${pxToRem(1)} solid ${
      color.secondary_palette.grays.cape_hope
    }`,
  },
  water_cooler_block: {
    paddingLeft: pxToRem(5),
    paddingRight: pxToRem(5),
    textAlign: "center",
  },
  water_cooler_image: {
    width: pxToRem(70),
    height: pxToRem(70),
    border: `solid ${pxToRem(1.1)} ${color.primary_palette.black}`,
    borderRadius: "100%",
    margin: `${pxToRem(5)} ${pxToRem(0)} ${pxToRem(5)} ${pxToRem(0)}`,
  },
  water_cooler_more: {
    textAlign: "center",
    padding: `${pxToRem(15)} ${pxToRem(0)} ${pxToRem(10)} ${pxToRem(0)} `,
  },
  city_tabs: {
    justifyContent: "flex-end",
    paddingTop: pxToRem(3),
  },
  font_bold: {
    fontWeight: "bold",
  },
  city_tab: {
    padding: `${pxToRem(0)} ${pxToRem(10)} `,
  },
  our_offices_images: {
    height: pxToRem(150),
    width: "100%",
    padding: pxToRem(5),
  },
  our_offices_image_row: { margin: `${pxToRem(5)} ${pxToRem(0)} ` },
  image1: { width: "50%", marginRight: pxToRem(5) },
  image2: { width: "100%", height: "49%" },
  image3: { width: "100%", height: "49%" },
  column: { display: "block" },
  left_arrow_corosel: {
    transform: "rotate(90deg)",
    position: "relative",
    top: "40%",
  },
  right_arrow_corosel: {
    transform: "rotate(270deg)",
    position: "relative",
    top: "40%",
  },
  company_vibe_title: {
    textAlign: "center",
    marginRight: pxToRem(15),
  },
  company_vibes_container: {
    clear: "both",
    width: "80%",
    margin: "0 auto",
    paddingTop: `${pxToRem(5)} ${pxToRem(0)}`,
    marginBottom: pxToRem(5),
  },
  company_vibe_row1: {
    width: "100%",
    justifyContent: "center",
    padding: `${pxToRem(5)} ${pxToRem(0)}`,
  },
  company_vibe_row2: {
    width: "100%",
    justifyContent: "center",
  },
  row_one_items: {
    paddingRight: "2%",
    display: "inline",
  },
  row_two_items: {
    paddingRight: "3%",
    display: "inline",
  },
  our_culture_title: {
    height: pxToRem(12),
    paddingBottom: pxToRem(2),
    borderBottom: `${pxToRem(1)} solid ${
      color.secondary_palette.grays.cape_hope
    }`,
    marginLeft: pxToRem(10),
    width: "95%",
  },
  our_culture_container: {
    margin: "0 auto",
    width: "68%",
    paddingTop: pxToRem(10),
  },
  our_culture_section: {
    margin: `${pxToRem(10)} ${pxToRem(12)}`,
    padding: `${pxToRem(10)} ${pxToRem(0)}`,
  },
  layout1: {
    height: "60%",
    width: "100%",
  },
  layout1_image_container: {
    width: "50%",
  },
  layout1_image: {
    width: "100%",
    height: "100%",
  },
  layout1_content: {
    width: "50%",
    lineHeight: 1.7,
    padding: `${pxToRem(0)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(10)}`,
  },
  layout1_con_section2: {
    marginTop: "5%",
  },
  layout2: {
    height: "40%",
    width: "100%",
    marginTop: "5%",
  },
  layout2_content: {
    width: "50%",
    lineHeight: 1.7,
    padding: "5% 0%",
  },
  layout2_image: {
    width: "50%",
    marginLeft: pxToRem(10),
  },
  layout2_con_section1: {
    paddingRight: pxToRem(5),
  },
  connect_image: {
    height: pxToRem(65),
    width: pxToRem(65),
    borderRadius: "100%",
    margin: "0 auto",
    marginBottom: pxToRem(5),
  },
  current_team_row: {
    width: "65%",
    margin: "0 auto",
  },
  connect_titles: { width: "100%" },
  connect_margin: { margin: `${pxToRem(10)} ${pxToRem(0)}` },
  connect_block: {
    width: "25%",
  },
  connect_background: {
    backgroundColor: color.primary_palette.fafa_gray,
    paddingTop: pxToRem(5),
  },
  happy_icon: {
    height: pxToRem(10),
    width: pxToRem(10),
    position: "absolute",
    right: pxToRem(7),
    top: pxToRem(7),
  },
  input_happy_container: {
    position: "relative",
    marginBottom: pxToRem(5),
    padding: `${pxToRem(0)} ${pxToRem(4)}`,
  },
  mail_icon: {
    height: pxToRem(7),
    width: pxToRem(10),
    margin: `${pxToRem(5)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(4)}`,
  },
  contact_us_action_icons: {
    height: pxToRem(8),
    width: pxToRem(20),
    position: "absolute",
    right: 0,
    top: pxToRem(4),
  },
  showmore_add_icon: {
    height: pxToRem(8),
    width: pxToRem(8),
    borderRadius: "100%",
    marginRight: pxToRem(3),
  },
  overflow_content: {
    overflowY: "scroll",
    marginRight: pxToRem(7),
    marginTop: pxToRem(5),
    maxHeight: pxToRem(110),
    "&::-webkit-scrollbar": {
      width: pxToRem(10),
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: color.secondary_palette.grays.background_gray,
      borderRadius: pxToRem(20),
    },
  },
  company_feedback_name: {
    display: "grid !important",
    position: "relative",
    top: pxToRem(5),
  },
  company_feedback_logo: {
    height: pxToRem(25),
    width: pxToRem(25),
    borderRadius: "100%",
    background: "black",
    margin: `${pxToRem(0)} ${pxToRem(3)}`,
  },
  profile_name_cf: {
    height: pxToRem(2),
  },
  tweet: {
    margin: `${pxToRem(7)} ${pxToRem(2)} ${pxToRem(7)} ${pxToRem(8)}`,
    borderBottom: `${pxToRem(1)} solid ${
      color.secondary_palette.grays.up_in_smoke
    }`,
  },
  tweet_content: {
    marginTop: pxToRem(3),
    paddingBottom: pxToRem(5),
  },
  hashTags: {
    color: `${color.secondary_palette.purples.franklin_purple} !important`,
  },
  tweet_date: {
    textAlign: "right",
    width: "60%",
  },
  tweets_section: {
    overflowY: "scroll",
    marginRight: pxToRem(7),
    maxHeight: pxToRem(160),
    "&::-webkit-scrollbar": {
      width: pxToRem(10),
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: color.secondary_palette.grays.background_gray,
      borderRadius: pxToRem(20),
    },
  },
  london_paris: {
    fontWeight: "normal",
    fontSize: pxToRem(8),
    color: `${color.form_colors.royal_purple_1} !important`,
  },
  height_10: {
    height: pxToRem(10),
  },
  m_l_10: {
    marginLeft: pxToRem(10),
  },
  see_all: {
    color: color.form_colors.royal_purple_1,
    fontWeight: 600,
    borderBottom: `${pxToRem(1)} solid ${color.form_colors.royal_purple_1}`,
  },
  twitter_share: {
    width: "40%",
    position: "relative",
    top: pxToRem(-4),
    "& img": {
      width: pxToRem(10),
      height: pxToRem(10),
    },
  },
  pos_rel: {
    position: "relative",
  },
  water_cool_sec: {
    width: "100%",
  },
});
export default styles;
