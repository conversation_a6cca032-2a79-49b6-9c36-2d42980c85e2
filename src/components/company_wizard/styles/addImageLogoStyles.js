import { withStyles } from "@material-ui/core/styles";

import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  refreshIcon: {
    height: pxToRem(10),
    marginRight: pxToRem(5),
  },
  error_msg_container: {
    justifyContent: "center",
  },
  errorMessage: {
    textTransform: "capitalize",
    marginLeft: pxToRem(5),
  },
  circle: {
    textAlign: "center",
  },
  input_circle: {
    display: "none",
  },
  add_btn_wrap: {
    position: "relative",
    "& svg": {
      position: "relative",
      left: pxToRem(2),
      width: pxToRem(15),
      "& path": {
        "&:nth-child(1)": {
          fill: `${color.primary_palette.franklin_purple} ! important`,
        },
      },
    },
  },
  logo_btn: {
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    borderRadius: pxToRem(27),
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.avenir_sb,
    color: color.secondary_palette.grays.dim_grey,
    textTransform: "uppercase",
    padding: `${pxToRem(1)} ${pxToRem(9)}`,
    height: pxToRem(25),
    cursor: "pointer",

    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.franklin_purple} !important`,
      border: `1px solid ${color.primary_palette.franklin_purple}`,
    },
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
      verticalAlign: "middle",
      marginRight: pxToRem(10),
      "& path": {
        "&:nth-child(1)": {
          fill: `${color.secondary_palette.grays.background_gray} ! important`,
        },
      },
    },
  },

  img_spacing: {
    paddingBottom: pxToRem(20),
  },
  label_pos: {
    position: "relative",
    cursor: "pointer",
  },
  trash_img: {
    width: pxToRem(26),
    height: pxToRem(26),
    position: "absolute",
    bottom: pxToRem(22),
    right: pxToRem(4),
  },
  trash_img_office_shots: {
    width: pxToRem(31),
    height: pxToRem(31),
    position: "absolute",
    bottom: pxToRem(50),
    right: pxToRem(6),
  },
  refresh_img: {
    width: pxToRem(31),
    height: pxToRem(31),
    position: "absolute",
    bottom: pxToRem(10),
    right: pxToRem(6),
  },
  plus: {
    width: pxToRem(26),
    height: pxToRem(26),
    position: "absolute",
    bottom: pxToRem(12),
    right: pxToRem(4),
  },
  pencil_edit: {
    position: "absolute",
    height: pxToRem(40),
    width: pxToRem(40),
    left: pxToRem(0),
    bottom: pxToRem(10),
  },
  img_mandatory_err: {
    width: pxToRem(250),
    margin: "0 auto",
    "& img": {
      position: "relative",
      marginRight: pxToRem(5),
      top: pxToRem(3),
    },
  },
});

export default styles;
