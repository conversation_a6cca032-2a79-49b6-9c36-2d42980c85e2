import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  container_row: {
    display: "block",
    textAlign: "center",
    margin: "0 auto",
    width: pxToRem(209),
  },
  step_2a: {
    fontSize: pxToRem(25),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.gillsans_sb,
    paddingBottom: pxToRem(2),
    textTransform: "uppercase",
    height: pxToRem(28),
  },
  step_2a_des: {
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    ...tradework_theme.typography.styles.avenir_sb,
    paddingBottom: pxToRem(5),
  },
  main_des: {
    ...tradework_theme.typography.styles.gillsans_light,
    fontSize: pxToRem(35),
    textTransform: "uppercase",
    height: pxToRem(45),
  },
  main_brand: {
    ...tradework_theme.typography.styles.avenir_roman,
    fontSize: pxToRem(20),
  },
  center_text: {
    display: "block",
    width: "100%",
    textAlign: "center",
  },
  Water_cooler: {
    display: "block",
    paddingLeft: pxToRem(90),
    paddingRight: pxToRem(90),
  },
  main_head: {
    display: "block",
    paddingTop: pxToRem(60),
    paddingLeft: pxToRem(100),
  },
  form_blk: {
    display: "block",
  },
  inputField: {
    display: "block",
    ...tradework_theme.typography.styles.avenir_light,
    color: color.primary_palette.black,
    fontSize: pxToRem(14),
    "& .MuiInputLabel-formControl": {
      color: color.placeholder_wc,
    },
    "& label + .MuiInput-formControl": {
      marginTop: `${pxToRem(11)} !important`,
    },
    "& .MuiFormLabel-root": {
      width: "100%",
      textAlign: "center",
      ...tradework_theme.typography.styles.avenir_light,
      fontSize: pxToRem(14),
      marginTop: pxToRem(-1),
    },
    "& .MuiInputLabel-shrink": {
      color: color.primary_palette.franklin_purple,
      fontSize: pxToRem(11),
      ...tradework_theme.typography.styles.avenir_black_r,
      textAlign: "left",
    },
    "& .MuiInputBase-root": {
      width: "100%",
    },
    "& .MuiInputBase-input": {
      textOverflow: "ellipsis",
      textAlign: "center",
      ...tradework_theme.typography.styles.avenir_roman,
      fontSize: pxToRem(14),
    },
  },
  video_summary: {
    ...tradework_theme.typography.styles.avenir_roman,
    color: color.primary_palette.franklin_purple,
    fontSize: pxToRem(13),
    paddingTop: pxToRem(18),
    textAlign: "left",
  },
  video_text: {
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    resize: "none",
    outline: "none",
    height: `${pxToRem(120)} !important`,
    width: pxToRem(203),
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    overflow: "auto !important",
  },
  radio_btn_wrap: {
    "& .PrivateSwitchBase-root": {
      padding: "2px !important",
    },
    "& .MuiIconButton-label": {
      width: "10px !important",
    },
    "& .MuiTypography-body1": {
      fontFamily: "avenir_light !important",
      fontSize: pxToRem(14),
    },
  },
  indiv_blk: {
    width: "33.33%",
    float: "left",
    height: pxToRem(575),
    display: "block",
    marginBottom: pxToRem(30),
  },
  logo_btn: {
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    borderRadius: pxToRem(27),
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.avenir_sb,
    color: `${color.secondary_palette.grays.dim_grey} !important`,
    textTransform: "uppercase",
    padding: `${pxToRem(1)} ${pxToRem(9)}`,
    height: pxToRem(25),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
      verticalAlign: "middle",
      marginRight: pxToRem(10),
    },
    "&:hover": {
      backgroundColor: color.shaded_gray_wc,
      color: `${color.primary_palette.franklin_purple} !important`,
      border: `1px solid ` + color.primary_palette.franklin_purple,
    },
  },
  logo_btn_red: {
    border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    borderRadius: pxToRem(27),
    fontSize: pxToRem(15),
    marginRight: pxToRem(150),
    ...tradework_theme.typography.styles.avenir_sb,
    color: `${color.primary_palette.christmas_red} !important`,
    textTransform: "uppercase",
    padding: `${pxToRem(1)} ${pxToRem(9)}`,
    height: pxToRem(25),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
      verticalAlign: "middle",
      marginRight: pxToRem(10),
    },
    "&:hover": {
      color: `${color.primary_palette.christmas_red} !important`,
      backgroundColor: "#FFFFFF !important",
      border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    },
  },
  later_btn: {
    justifyContent: "flex-start",
    color: color.finish_color,
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_roman,
    cursor: "pointer",
    textDecoration: "underline",
    display: "flex",
    flexGrow: 1,
    "&:hover": {
      color: color.primary_palette.franklin_purple,
    },
  },
  actionButtons: {
    padding: 0,
    color: color.primary_palette.franklin_purple,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.franklin_purple,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.franklin_purple} !important`,
      border: `1px solid ${color.primary_palette.franklin_purple}`,
    },
  },
  greenactionButtons: {
    padding: 0,
    color: color.primary_palette.pine_green,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.pine_green,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.pine_green} !important`,
      border: `1px solid ${color.primary_palette.pine_green}`,
    },
  },
  circle_img: {
    width: pxToRem(190),
    height: pxToRem(190),
  },
  circle_img_border: {
    width: pxToRem(180),
    height: pxToRem(180),
    borderRadius: "50%",
    cursor: "pointer",
    border: `solid ${pxToRem(5)} ${color.primary_palette.black}`,
  },
  player_icon: {
    position: "absolute",
    bottom: pxToRem(10),
    right: pxToRem(10),
    height: pxToRem(42),
    width: pxToRem(42),
    borderRadius: "100%",
    cursor: "pointer",
  },
  btn_blk: {
    paddingTop: pxToRem(20),
    display: "block",
  },
  btn_sty: {
    // paddingBottom: pxToRem(30),
    justifyContent: "center",
    clear: "both",
    position: "relative",
    top: pxToRem(16),
  },
  btn_blk_width: {
    paddingTop: pxToRem(20),
    paddingBottom: pxToRem(90),
    width: "90%",
    margin: "0 auto",
  },
  margin_l_60: {
    marginLeft: pxToRem(60),
  },
  first_btn_width: {
    width: pxToRem(75),
  },
  second_btn_width: {
    width: pxToRem(187),
  },
  pos_rel: {
    position: "relative",
  },
  btn_flex: {
    justifyContent: "flex-end",
    flexGrow: 1,
  },
  error_msg_container: {
    width: pxToRem(220),
    display: "flex",
    margin: "0 auto",
    // marginTop: pxToRem(20),
  },
  errorMessage: {
    marginLeft: pxToRem(5),
  },
  actions_wrap: {
    position: "relative",
    top: pxToRem(20),
  },
  order_wrap: {
    width: "50%",
    "& p": {
      paddingRight: pxToRem(5),
      paddingTop: pxToRem(6),
    },
  },
  delete_wrap: {
    width: "50%",
    justifyContent: "flex-end",
    "& p": {
      paddingRight: pxToRem(5),
      cursor: "pointer",
      paddingTop: pxToRem(6),
    },
    "& img": {
      cursor: "pointer",
    },
  },
  order_dropdown: {
    width: pxToRem(40),
    height: pxToRem(18),
    marginTop: pxToRem(5),
    borderRadius: pxToRem(2),
    padding: `${pxToRem(1)} ${pxToRem(5)}`,
    "& p": {
      fontSize: `${pxToRem(13)} !important`,
      fontFamily: "avenir_black_r !important",
      fontWeight: "500 !important",
    },
    "& .MuiSvgIcon-root": {
      fontSize: `${pxToRem(11)} !important`,
      marginTop: pxToRem(10),
      "& path": {
        stroke: color.primary_palette.black,
      },
    },
    "& .MuiSelect-select": {
      backgroundColor: `${color.primary_palette.white} !important`,
    },
  },
  disable_delete: {
    opacity: 0.66,
  },
  video_err: {
    "& .MuiFormLabel-root": {
      color: `${color.primary_palette.christmas_red} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.primary_palette.christmas_red
      }`,
      "&:after": {
        borderBottom: `${pxToRem(1)} solid ${
          color.primary_palette.christmas_red
        }`,
      },
    },
  },
});

export default styles;
