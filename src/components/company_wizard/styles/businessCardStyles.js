import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  step_1: {
    display: "block",
    width: pxToRem(490),
    marginLeft: "10.2%",
  },
  business_card_width: {
    margin: `${pxToRem(16)} ${pxToRem(30)} ${pxToRem(0)} ${pxToRem(30)}`,
  },
  business_card_width_edit: {
    margin: `${pxToRem(40)} ${pxToRem(30)} ${pxToRem(0)} ${pxToRem(30)}`,
  },
  text_center: {
    textAlign: "center",
  },
  left_side: {
    flexGrow: 1,
    width: "45%",
    display: "block",
    lineHeight: "normal",
  },
  right_side: {
    flexGrow: 1,
    display: "block",
    width: "60%",
  },
  left_side_edit: {
    flexGrow: 1,
    width: "48%",
    display: "block",
    lineHeight: "normal",
    marginLeft: "5%",
  },
  right_side_edit: {
    flexGrow: 1,
    display: "block",
    width: "52%",
  },
  marginBottom30: {
    marginBottom: pxToRem(30),
  },
  left_text: {
    paddingLeft: pxToRem(10),
    paddingBottom: pxToRem(19),
    textTransform: "uppercase",
  },
  upper_case: {
    textTransform: "uppercase",
    height: pxToRem(45),
  },
  country_code_name: {
    lineHeight: pxToRem(30),
  },
  card_blk: {
    width: pxToRem(450),
    position: "relative",
    border: `solid ${pxToRem(2)} ${color.primary_palette.black}`,
    backgroundColor: color.primary_palette.white,
    paddingRight: pxToRem(20),
    paddingLeft: pxToRem(20),
    paddingTop: pxToRem(32),
    display: "block",
    margin: "0 auto",
    marginTop: pxToRem(10),
    boxShadow: `${pxToRem(2)} ${pxToRem(2)} ${pxToRem(4)} ${pxToRem(
      0
    )} rgba(0, 0, 0, 0.5)`,
  },
  countryImage: {
    width: "25px",
    height: "16px",
    paddingRight: "14px",
  },
  dropDownIcon: {
    height: "5px",
    width: "10px",
    cursor: "pointer",
    position: "absolute",
    top: pxToRem(16),
    right: pxToRem(0),
  },
  phoneNumberActions: {
    marginTop: pxToRem(10),
  },
  dropdownOptions: {
    position: "absolute",
    border: "1px solid #bfbfbf",
    borderRadius: "4px",
    boxShadow: "1px 1px 2px 0 rgba(0, 0, 0, 0.15)",
    fontSize: "14px",
    height: "34px",
    width: "74px",
    padding: "5px 2px 5px 2px",
    background: "white",
    right: "-44px",
    bottom: 0,
    top: "32px",
    cursor: "pointer",
    zIndex: 1000,
  },
  btn_flex: {
    justifyContent: "flex-end",
    flexGrow: 1,
  },
  scroll: {
    "& .MuiAutocomplete-listbox": {
      height: `${pxToRem(400)} !important`,
      "& li": {
        margin: `${pxToRem(0)} ${pxToRem(20)} !important`,
        borderBottom: `${pxToRem(1)} solid ${
          color.secondary_palette.grays.coluded_vision
        }`,
        borderTop: `${pxToRem(1)} solid ${
          color.secondary_palette.grays.coluded_vision
        }`,
      },
    },
  },
  scroll_area_profile: {
    overflowY: "scroll",
    overflowX: "hidden",
    height: `${pxToRem(700)} !important`,
    "& .thumbstyle": {
      width: pxToRem(12),
      boxShadow: `${pxToRem(0)} ${pxToRem(2)} ${pxToRem(4)} ${pxToRem(
        0
      )} rgba(0, 0, 0, 0.5) !important`,
      backgroundImage: "none !important",
      backgroundColor: `${color.shaded_gray_wc} !important`,
      borderRadius: "50% !important",
      height: `${pxToRem(13)} !important`,
    },
    "& .trackstyle": {
      width: pxToRem(1),
      backgroundColor: `${color.secondary_palette.grays.stonewall_grey} !important`,
      border: `solid ${pxToRem(1)} ${
        color.secondary_palette.grays.stonewall_grey
      }`,
    },
    "&::-webkit-scrollbar": {
      width: pxToRem(5),
      backgroundColor: color.secondary_palette.grays.stonewall_grey,
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: color.secondary_palette.grays.background_gray,
      borderRadius: pxToRem(20),
    },
  },
  actionButtons: {
    padding: 0,
    color: color.primary_palette.pine_green,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.pine_green,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.pine_green} !important`,
      border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    },
  },
  greenactionButtons: {
    padding: 0,
    color: color.primary_palette.pine_green,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.pine_green,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.pine_green} !important`,
      border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    },
  },
  join_us_btn: {
    width: pxToRem(127),
    justifyContent: "center !important",
  },
  second_btn_width: {
    width: pxToRem(187),
  },
  main_heading: {
    display: "block",
    margin: "0 auto",
    width: "100%",
    textAlign: "center",
  },
  country_down: {
    border: 0,
    backgroundColor: "transparent",
    "& p": {
      fontSize: pxToRem(10),
      color: color.knight_armour,
      ...tradework_theme.typography.styles.avenir_light,
    },
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(11),
      "& path": {
        stroke: color.secondary_palette.grays.medium_gray,
      },
    },
  },
  address: {
    flexGrow: 1,
    display: "block",
    width: "50%",
    paddingBottom: pxToRem(16),
  },
  address_input_label: {
    position: "absolute",
    zIndex: 9,
  },
  txt_align_left: {
    "& .MuiInputBase-input": {
      textAlign: "left !important",
    },
  },
  width190: {
    width: pxToRem(206),
  },
  marginLeft10: {
    marginLeft: pxToRem(10),
  },
  cityWidth: {
    width: pxToRem(100),
  },
  stateWidth: {
    width: pxToRem(32),
    paddingLeft: pxToRem(13),
  },
  address_input: {
    marginTop: pxToRem(5),
    // width: "100%",
    textAlign: "left",
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    "& .MuiInputBase-input": {
      padding: `${pxToRem(4)} !important`,
      "&::placeholder": {
        color: color.secondary_palette.grays.shadow_gray,
      },
      // "&:focus": {
      //   backgroundColor: color.harbor_afternoon,
      //   textAlign: "left",
      //   borderLeft: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
      // },
      textOverflow: "ellipsis",
      // textAlign: "center",
      fontSize: pxToRem(16),
      // fontWeight: 300,
      // color: color.liquor_ice,
      height: pxToRem(20),
      margin: `${pxToRem(6)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(
        0
      )} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.secondary_palette.grays.shadow_gray
      }`,
      "&:before": {
        borderBottom: `0 !important`,
      },
      "&:hover": {
        borderBottom: `${pxToRem(1)} solid ${color.primary_palette.black}`,
      },
    },
  },
  address_input_error: {
    marginTop: pxToRem(5),
    // width: "100%",
    textAlign: "left",
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    "& .MuiInputBase-input": {
      padding: `${pxToRem(4)} !important`,
      "&::placeholder": {
        color: color.secondary_palette.grays.shadow_gray,
      },
      // "&:focus": {
      //   backgroundColor: color.harbor_afternoon,
      //   textAlign: "left",
      //   borderLeft: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
      // },
      textOverflow: "ellipsis",
      // textAlign: "center",
      fontSize: pxToRem(16),
      // fontWeight: 300,
      // color: color.liquor_ice,
      height: pxToRem(20),
      margin: `${pxToRem(6)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(
        0
      )} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.primary_palette.christmas_red
      }`,
      "&:before": {
        borderBottom: `0 !important`,
      },
      "&:hover": {
        borderBottom: `${pxToRem(1)} solid ${color.primary_palette.black}`,
      },
    },
  },
  phone_input: {
    marginTop: pxToRem(5),
    // width: "100%",
    textAlign: "left",
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    "& .MuiInputBase-input": {
      padding: `${pxToRem(4)} !important`,
      // "&::placeholder": {
      //   color: color.secondary_palette.grays.shadow_gray,
      // },
      // "&:focus": {
      //   backgroundColor: color.harbor_afternoon,
      //   textAlign: "left",
      //   borderLeft: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
      // },
      textOverflow: "ellipsis",
      // textAlign: "center",
      fontSize: pxToRem(16),
      // fontWeight: 300,
      // color: color.liquor_ice,
      height: pxToRem(20),
      margin: `${pxToRem(6)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(
        0
      )} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.secondary_palette.grays.shadow_gray
      }`,
      "&:before": {
        borderBottom: `0 !important`,
      },
      "&:hover": {
        borderBottom: `${pxToRem(1)} solid ${color.primary_palette.black}`,
      },
    },
  },
  phone_input_error: {
    marginTop: pxToRem(5),
    // width: "100%",
    textAlign: "left",
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    "& .MuiInputBase-input": {
      padding: `${pxToRem(4)} !important`,
      // "&::placeholder": {
      //   color: color.secondary_palette.grays.shadow_gray,
      // },
      // "&:focus": {
      //   backgroundColor: color.harbor_afternoon,
      //   textAlign: "left",
      //   borderLeft: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
      // },
      textOverflow: "ellipsis",
      // textAlign: "center",
      fontSize: pxToRem(16),
      // fontWeight: 300,
      // color: color.liquor_ice,
      height: pxToRem(20),
      margin: `${pxToRem(6)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(
        0
      )} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.primary_palette.christmas_red
      }`,
      "&:before": {
        borderBottom: `0 !important`,
      },
      "&:hover": {
        borderBottom: `${pxToRem(1)} solid ${color.primary_palette.black}`,
      },
    },
  },
  city_input: {
    marginTop: pxToRem(5),
    // width: "100%",
    textAlign: "left",
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    "& .MuiInputBase-input": {
      padding: `${pxToRem(4)} !important`,
      paddingLeft: `${pxToRem(14)} !important`,
      "&::placeholder": {
        color: color.secondary_palette.grays.shadow_gray,
      },
      // "&:focus": {
      //   backgroundColor: color.harbor_afternoon,
      //   textAlign: "left",
      //   borderLeft: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
      // },
      textOverflow: "ellipsis",
      // textAlign: "center",
      fontSize: pxToRem(16),
      // fontWeight: 300,
      // color: color.liquor_ice,
      height: pxToRem(20),
      margin: `${pxToRem(6)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(
        0
      )} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.secondary_palette.grays.shadow_gray
      }`,
      "&:before": {
        borderBottom: `0 !important`,
      },
      "&:hover": {
        borderBottom: `${pxToRem(1)} solid ${color.primary_palette.black}`,
      },
    },
  },
  city_input_error: {
    marginTop: pxToRem(5),
    // width: "100%",
    textAlign: "left",
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    "& .MuiInputBase-input": {
      padding: `${pxToRem(4)} !important`,
      paddingLeft: `${pxToRem(14)} !important`,
      "&::placeholder": {
        color: color.secondary_palette.grays.shadow_gray,
      },
      // "&:focus": {
      //   backgroundColor: color.harbor_afternoon,
      //   textAlign: "left",
      //   borderLeft: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
      // },
      textOverflow: "ellipsis",
      // textAlign: "center",
      fontSize: pxToRem(16),
      // fontWeight: 300,
      // color: color.liquor_ice,
      height: pxToRem(20),
      margin: `${pxToRem(6)} ${pxToRem(0)} ${pxToRem(0)} ${pxToRem(
        0
      )} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.primary_palette.christmas_red
      }`,
      "&:before": {
        borderBottom: `0 !important`,
      },
      "&:hover": {
        borderBottom: `${pxToRem(1)} solid ${color.primary_palette.black}`,
      },
    },
  },
  phn_num: {
    marginLeft: pxToRem(12),
    "& .MuiInputBase-input": {
      ...tradework_theme.typography.styles.avenir_light,
    },
  },
  dropdown_headings: {
    margin: `${pxToRem(5)} ${pxToRem(0)} ${pxToRem(5)} ${pxToRem(20)}`,
  },
  address_block: {
    display: "block",
    textAlign: "left",
    marginTop: pxToRem(5),
  },
  address_text_bg: {
    backgroundColor: color.secondary_palette.blues.business_card_blue,
    borderLeft: `solid ${pxToRem(1)} ${color.primary_palette.black}`,
  },
  address_text: {
    marginTop: pxToRem(5),
  },
  margin_top: {
    marginTop: pxToRem(14),
  },
  checkbox_section_hq: {
    flexGrow: 1,
    // marginLeft: pxToRem(21),
    height: pxToRem(20),
    padding: pxToRem(5),
  },
  nicknameWrapper: {
    // marginLeft: pxToRem(5),
    marginTop: pxToRem(5),
  },
  checkbox_section: {
    flexGrow: 1,
    height: pxToRem(20),
    padding: pxToRem(5),
  },
  checkbox_section_container: {
    marginLeft: "8.3%",
    width: "70%",
    padding: pxToRem(10),
    marginTop: pxToRem(15),
  },
  checkbox_section_container_2: {
    marginLeft: "8.3%",
    width: "100%",
    padding: pxToRem(10),
    marginTop: pxToRem(15),
  },
  choose_office_section: {
    flexGrow: 1,
    justifyContent: "flex-end",
    "& p": {
      color: color.secondary_palette.purples.franklin_purple,
      fontSize: pxToRem(14),
      textTransform: "uppercase",
      fontWeight: 600,
      ...tradework_theme.typography.styles.gillsans_sb,
    },
  },
  checkbox: {
    "& .MuiTypography-root": {
      fontSize: pxToRem(14),
      color: color.primary_palette.franklin_purple,
      ...tradework_theme.typography.styles.gillsans_sb,
      paddingLeft: pxToRem(7),
    },
    "& .MuiButtonBase-root": {
      padding: 0,
      paddingLeft: pxToRem(7),
    },
  },
  checkbox_aggrement: {
    marginLeft: pxToRem(2),
    "& .MuiTypography-root": {
      fontSize: pxToRem(16),
      textTransform: "initial",
      color: color.primary_palette.black,
      ...tradework_theme.typography.styles.gillsans_r,
      paddingLeft: pxToRem(7),
    },
    "& .MuiButtonBase-root": {
      padding: 0,
      paddingLeft: pxToRem(10),
    },
  },
  errorCheck: {
    "& .MuiFormControlLabel-label": {
      color: color.primary_palette.christmas_red,
    },
  },
  redIcon: {
    color: `${color.primary_palette.christmas_red} !important`,
  },
  checkbox_aggrement_comp: {
    "& .MuiTypography-root": {
      fontSize: pxToRem(16),
      textTransform: "initial",
      color: color.greyish_brown,
      ...tradework_theme.typography.styles.avenir_light,
      paddingLeft: pxToRem(7),
      marginLeft: pxToRem(-2),
    },
    "& .MuiButtonBase-root": {
      marginLeft: pxToRem(82),
      padding: 0,
      paddingLeft: pxToRem(10),
    },
  },
  checkbox_aggrement_2: {
    // width: pxToRem(500),
    // lineHeight: 1,
    marginLeft: pxToRem(2),
    "& .MuiTypography-root": {
      fontSize: pxToRem(16),
      width: pxToRem(600),
      lineHeight: `${pxToRem(18)} !important`,
      textTransform: "initial",
      color: color.greyish_brown,
      fontFamily: "avenir_light",
      paddingLeft: pxToRem(7),
    },
    "& .MuiFormControlLabel-label": {
      paddingTop: pxToRem(20),
    },
    "& .MuiButtonBase-root": {
      padding: 0,
      paddingLeft: pxToRem(10),
    },
  },
  select_field_required: {
    fontSize: pxToRem(14),
    color: color.primary_palette.franklin_purple,
    position: "absolute",
    left: pxToRem(0),
    top: pxToRem(20),
    zIndex: 9,
  },
  select_field_required_red: {
    fontSize: pxToRem(14),
    color: color.primary_palette.christmas_red,
    position: "absolute",
    left: pxToRem(0),
    top: pxToRem(20),
    zIndex: 9,
  },
  nickName_required: {
    fontSize: pxToRem(16),
    color: color.primary_palette.franklin_purple,
    zIndex: 9,
  },
  nickName_required_error: {
    fontSize: pxToRem(16),
    color: color.primary_palette.christmas_red,
    zIndex: 9,
  },
  select_field_required_company: {
    fontSize: pxToRem(14),
    color: color.primary_palette.franklin_purple,
    position: "absolute",
    left: pxToRem(20),
    top: pxToRem(46),
    zIndex: 9,
  },
  select_field_required_red_company: {
    fontSize: pxToRem(14),
    color: color.primary_palette.christmas_red,
    position: "absolute",
    left: pxToRem(14),
    top: pxToRem(46),
    zIndex: 9,
  },
  checkBox_required: {
    fontSize: pxToRem(14),
    color: color.primary_palette.franklin_purple,
    position: "absolute",
    // left: pxToRem(0),
    // top: pxToRem(20),
    zIndex: 9,
  },
  checkBox_required_red: {
    fontSize: pxToRem(14),
    color: color.primary_palette.christmas_red,
    position: "absolute",
    // left: pxToRem(0),
    // top: pxToRem(20),
    zIndex: 9,
  },
  nickNameTxtAlign: {
    bottom: "-18px",
    left: "119px",
    position: "absolute",
  },
  select_field_required_email: {
    fontSize: pxToRem(14),
    color: color.primary_palette.franklin_purple,
    position: "absolute",
    left: pxToRem(-18),
    top: pxToRem(40),
    zIndex: 9,
  },
  select_field_required_email_red: {
    fontSize: pxToRem(14),
    color: color.primary_palette.christmas_red,
    position: "absolute",
    left: pxToRem(-18),
    top: pxToRem(40),
    zIndex: 9,
  },
  paddingLeft10: {
    paddingLeft: pxToRem(10),
  },
  step1_heading: {
    textTransform: "uppercase",
    margin: "0 auto",
    width: "80%",
  },
  step1_subheading: {
    margin: "0 auto",
    width: "79.5%",
  },
  positionRelative: {
    position: "relative",
  },
  formatAlign: {
    position: "absolute",
    bottom: pxToRem(60),
    left: pxToRem(-45),
  },
  add_office: {
    color: `${color.primary_palette.franklin_purple} !important`,
    fontSize: pxToRem(14),
    ...tradework_theme.typography.styles.gillsans_sb,
    padding: 0,
    textTransform: "capitalize",
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
      marginTop: pxToRem(6),
      marginRight: pxToRem(5),
      "& path": {
        "&:nth-child(1)": {
          fill: color.secondary_palette.grays.background_gray,
        },
      },
    },
  },
  flexEnd: {
    justifyContent: "flex-end",
  },
  card_blk_next: {
    width: pxToRem(498),
    position: "relative",
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    backgroundColor: color.primary_palette.fafa_gray,
    paddingRight: pxToRem(15),
    paddingLeft: pxToRem(15),
    paddingTop: pxToRem(16),
    display: "block",
    margin: "0 auto",
  },
  mainText: {
    lineHeight: pxToRem(37),
  },
  address_1: {
    flexGrow: 1,
    display: "block",
    textAlign: "center",
    paddingTop: pxToRem(23),
    paddingBottom: pxToRem(30),
  },
  flex_Grow: {
    flexGrow: 1,
    justifyContent: "center",
  },
  btn_alignment: {
    flexGrow: 1,
    justifyContent: "flex-end",
  },
  btn_alignment_2: {
    flexGrow: 1,
    justifyContent: "center",
  },
  btn_blk: {
    width: "80%",
    margin: "0 auto",
    marginBottom: "7px",
    paddingTop: pxToRem(20),
  },
  btn_blk2: {
    width: "80%",
    margin: "0 auto",
    marginBottom: "7px",
    paddingTop: pxToRem(20),
    position: "relative",
    bottom: pxToRem(160),
    right: pxToRem(50),
  },
  later_btn: {
    justifyContent: "flex-start",
    color: color.finish_color,
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_roman,
    cursor: "pointer",
    textDecoration: "underline",
    display: "flex",
    flexGrow: 1,
    "&:hover": {
      color: color.primary_palette.franklin_purple,
    },
  },
  block: {
    display: "block",
  },
  justifty_center: {
    justifyContent: "center",
  },
  input_block: {
    paddingTop: pxToRem(30),
    position: "relative",
  },
  marginLeft17: {
    marginLeft: pxToRem(-17),
  },
  textfield: {
    margin: 0,
    "& .MuiInputBase-input": {
      fontSize: pxToRem(16),
      color: color.primary_palette.black,
      ...tradework_theme.typography.styles.gillsans_r,
    },
    "& .MuiInputBase-root": {
      width: pxToRem(353),
      paddingLeft: pxToRem(0),
    },
  },
  textfield1: {
    width: pxToRem(353),
    "& .MuiInputBase-input": {
      fontSize: pxToRem(16),
      padding: pxToRem(0),
      color: color.primary_palette.black,
      ...tradework_theme.typography.styles.gillsans_r,
    },
  },
  text_width: {
    width: "120px",
    alignItems: "center",
    justifyContent: "start",
    display: "flex",
  },
  text_width_email: {
    width: "102px",
    alignItems: "center",
    justifyContent: "start",
    display: "flex",
  },
  choose_office: {
    width: pxToRem(341),
    border: `solid ${pxToRem(0.5)} ${color.primary_palette.black}`,
    color: color.black_oak,
    height: pxToRem(17),
    marginTop: pxToRem(5),
    borderRadius: pxToRem(2),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(10),
    },
  },
  circle_img: {
    width: pxToRem(168),
    height: pxToRem(168),
    borderRadius: "50%",
  },
  circle_img_tm: {
    margin: `${pxToRem(45)} ${pxToRem(0)}`,
    marginBottom: pxToRem(0),
  },
  block_input: {
    display: "block",
    width: "76%",
    margin: "0 auto",
  },
  block_space: {
    paddingTop: pxToRem(25),
  },
  pt20: {
    paddingTop: pxToRem(20),
  },
  business_text: {
    display: "block",
    textAlign: "center",
  },
  countryField: {
    width: "100%",
    "& .MuiInputBase-input": {
      paddingBottom: `${pxToRem(0)} !important`,
    },
  },
  flagIcon: {
    width: pxToRem(14),
    height: pxToRem(9),
    marginTop: pxToRem(4),
  },
  dropDownIcon: {
    width: pxToRem(11),
    height: pxToRem(5),
    cursor: "pointer",
  },
  countryName: {
    paddingLeft: pxToRem(3),
    paddingRight: pxToRem(3),
  },
  dropdownOptions: {
    position: "absolute",
    border: `${pxToRem(1)} solid ${color.secondary_palette.grays.silver_gray}`,
    borderRadius: pxToRem(4),
    boxShadow: `${pxToRem(1)} ${pxToRem(1)} ${pxToRem(2)} ${pxToRem(
      0
    )} rgba(0, 0, 0, 0.15)`,
    fontSize: pxToRem(14),
    height: pxToRem(34),
    width: pxToRem(74),
    color: "#000",
    padding: `${pxToRem(5)} ${pxToRem(2)}`,
    background: "white",
    right: pxToRem(-4),
    bottom: 0,
    top: pxToRem(26),
    cursor: "pointer",
    zIndex: "999",
  },
  companies: {
    width: `100%`,
    "& .MuiFormControl-root": {
      "&:hover": {
        border: `${pxToRem(0)} !important`,
      },
    },
    "& .MuiAutocomplete-input": {
      padding: `${pxToRem(0)} !important`,
      fontSize: `${pxToRem(26)} !important`,
      textAlign: "center",
      ...tradework_theme.typography.styles.NeutraText,
      "&::placeholder": {
        color: color.secondary_palette.grays.shadow_gray,
        ...tradework_theme.typography.styles.NeutraText,
      },
    },
    "& .MuiInputBase-input": {
      padding: `${pxToRem(0)} !important`,
      fontSize: pxToRem(37),
      textAlign: "center",
    },
    "& .MuiFormControl-marginNormal": {
      marginTop: `${pxToRem(0)} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.secondary_palette.grays.shadow_gray
      }`,
      "&:before": {
        borderBottom: `0 !important`,
      },
      "&:hover": {
        borderBottom: `${pxToRem(1)} solid ${color.primary_palette.black}`,
      },
    },
  },
  employee_select: {
    borderTop: `${pxToRem(0)} !important`,
    borderLeft: `${pxToRem(0)} !important`,
    borderRight: `${pxToRem(0)} !important`,
    marginTop: pxToRem(-6),
    marginLeft: pxToRem(5),
    "& p": {
      ...tradework_theme.typography.styles.avenir_light,
      fontSize: pxToRem(14),
      width: "100%",
      display: "block",
      textAlign: "center",
      marginTop: pxToRem(8),
    },
    "& .MuiSvgIcon-root": {
      fontSize: `${pxToRem(10)} !important`,
      top: pxToRem(5),
      "& path": {
        stroke: color.secondary_palette.grays.shadow_gray,
      },
    },
    "& .MuiSelect-select": {
      backgroundColor: `${color.primary_palette.white} !important`,
      padding: `${pxToRem(0)} !important`,
      marginLeft: pxToRem(5),
      textAlign: "center",
      ...tradework_theme.typography.styles.avenir_light,
      color: color.primary_palette.black,
    },
  },
  scrollbar_thumb: {
    width: pxToRem(12),
    boxShadow: `${pxToRem(0)} ${pxToRem(2)} ${pxToRem(3)} ${pxToRem(
      0
    )} rgba(0, 0, 0, 0.5)`,
    backgroundColor: color.shaded_gray_wc,
    borderRadius: "50%",
    backgroundPosition: "center",
    backgroundSize: "contain",
    position: "absolute",
    backgroundRepeat: "no-repeat",
    height: `${pxToRem(13)} !important`,
    zIndex: "1000",
    transform: `translateY(${pxToRem(0)})`,
    right: pxToRem(-5),
  },
  scrollbar_track: {
    width: pxToRem(1),
    height: "100%",
    backgroundColor: color.secondary_palette.grays.stonewall_grey,
    border: `solid ${pxToRem(1)} ${
      color.secondary_palette.grays.stonewall_grey
    }`,
    right: pxToRem(5),
  },
  country_item_row: {
    textOverflow: "ellipsis",
    width: "100%",
  },
  flag_input: {
    width: "100%",
    "& .MuiInputBase-input": {
      textAlign: "center",
      fontSize: pxToRem(20),
      fontWeight: 500,
    },
    "& .MuiInput-underline": {
      border: "0px !important",
      "&:before": {
        display: "none",
      },
      "&:after": {
        display: "none",
      },
    },
  },
  country_dropdown_list: {
    width: pxToRem(80),
  },
  country_dropdown_scroll: {
    height: `${pxToRem(120)} !important`,
  },
  country_dropdown: {
    width: pxToRem(69),
    bottom: 0,
    position: "absolute",
    right: 0,
    backgroundColor: "transparent !important",
    "& :before": {
      borderBottom: `${pxToRem(1)} solid rgba(0, 0, 0, 0.42)`,
    },
    "& :after": {
      borderBottom: `${pxToRem(2)} solid ${
        color.primary_palette.franklin_purple
      }`,
    },
    border: "none !important",
    "& .MuiSvgIcon-root": {
      height: pxToRem(5),
      width: pxToRem(11),
      marginBottom: pxToRem(3),
    },
  },
  country_image: {
    height: pxToRem(15),
    width: pxToRem(15),
    marginLeft: pxToRem(18),
    marginRight: pxToRem(5),
    marginTop: pxToRem(6),
  },
  position: {
    position: "relative",
  },
  padding_top_15: {
    paddingTop: pxToRem(15),
  },
  modal_content: {
    background: "white",
    border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
    borderRadius: 0,
    padding: `${pxToRem(10)} ${pxToRem(20)}`,
    "&:focus": {
      border: `solid ${pxToRem(2)} ${color.primary_palette.franklin_purple}`,
      borderRadius: 0,
    },
  },
  info_img: {
    verticalAlign: "middle",
    marginTop: pxToRem(-2),
    paddingRight: pxToRem(3),
    width: pxToRem(9),
    height: pxToRem(9),
  },
  ping_admin_txt: {
    marginLeft: pxToRem(5),
    paddingTop: pxToRem(20),
  },
  ping_admin_wrap: {
    marginLeft: "10%",
    paddingTop: pxToRem(428),
  },
  tm_ping_admin_txt: {
    display: "inline-block",
  },
  tm_ping_admin_txt2: {
    display: "inline-block",
    marginLeft: pxToRem(3),
    width: "60%",
  },
  ping_flag: {
    width: pxToRem(15),
    height: pxToRem(16),
    marginLeft: pxToRem(5),
  },
  ping_admin: {
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.avenir_sb,
  },
  ping_blk: {
    paddingLeft: pxToRem(8),
    borderTop: `solid ${pxToRem(0.5)} ${
      color.secondary_palette.grays.shadow_gray
    }`,
    paddingTop: pxToRem(5),
    cursor: "pointer",
  },
  paddingData: {
    padding: pxToRem(20),
  },
  nickname: {
    width: pxToRem(300),
    marginLeft: pxToRem(6),
    marginTop: pxToRem(3),
    "& .MuiInput-input": {
      fontSize: `${pxToRem(16)} !important`,
      color: `${color.greyish_brown} !important`,
      fontFamily: "gillsans_r !important",
    },
    "& .MuiInputBase-input": {
      padding: `${pxToRem(0)} !important`,
    },
  },
  nickname_error: {
    marginLeft: pxToRem(6),
    marginTop: pxToRem(3),
    "& .MuiInput-input": {
      fontSize: `${pxToRem(16)} !important`,
      color: `${color.greyish_brown} !important`,
      fontFamily: "gillsans_r !important",
    },
    "& .MuiInputBase-input": {
      padding: `${pxToRem(0)} !important`,
    },
    "& .MuiInput-underline": {
      borderBottom: `${pxToRem(1)} solid ${
        color.primary_palette.christmas_red
      }`,
      "&:before": {
        borderBottom: `0 !important`,
      },
      "&:hover": {
        borderBottom: `${pxToRem(1)} solid ${color.primary_palette.black}`,
      },
    },
  },
  emailError: {
    color: `${color.primary_palette.christmas_red} !important`,
  },
  emailInputError: {
    borderBottom: `${pxToRem(1)} solid ${
      color.primary_palette.christmas_red
    } !important`,
  },
  linkStyle: {
    textDecoration: "underline",
    cursor: "pointer",
  },
  clear_icon: {
    height: pxToRem(9),
    width: pxToRem(9),
    position: "absolute",
    bottom: pxToRem(12),
    right: pxToRem(108),
    cursor: "pointer",
  },
  clear_icon2: {
    height: pxToRem(9),
    width: pxToRem(9),
    position: "absolute",
    bottom: pxToRem(22),
    right: pxToRem(108),
    cursor: "pointer",
  },
});

export default styles;
