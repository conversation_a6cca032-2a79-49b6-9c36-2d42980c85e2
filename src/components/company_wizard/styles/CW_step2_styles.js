import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  logo_del: {
    left: pxToRem(120),
    bottom: pxToRem(20),
  },
  CompanyWizard_step2: {
    marginTop: 0,
    marginBottom: 0,
    marginLeft: pxToRem(80),
    marginRight: pxToRem(80),
    position: "relative",
  },
  logo_btn_red: {
    border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    borderRadius: pxToRem(27),
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.avenir_sb,
    color: `${color.primary_palette.christmas_red} !important`,
    textTransform: "uppercase",
    padding: `${pxToRem(1)} ${pxToRem(9)}`,
    height: pxToRem(25),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
      verticalAlign: "middle",
      marginRight: pxToRem(10),
    },
    "&:hover": {
      color: `${color.primary_palette.christmas_red} !important`,
      backgroundColor: "#FFFFFF !important",
      border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    },
  },
  width200: {
    width: pxToRem(200),
  },
  main_head: {
    textAlign: "center",
    width: "100%",
  },
  main_head_left: {
    textAlign: "center",
    width: "100%",
  },
  main_des: {
    ...tradework_theme.typography.styles.gillsans_light,
    fontSize: pxToRem(35),
  },
  main_brand: {
    ...tradework_theme.typography.styles.avenir_roman,
    fontSize: pxToRem(20),
    lineHeight: "normal !important",
    fontWeight: "normal !important",
  },
  left_side: {
    flexGrow: 0,
    width: "35%",
    paddingRight: pxToRem(100),
    position: "relative",
    "&:after": {
      content: "''",
      position: "absolute",
      right: 0,
      width: pxToRem(1),
      height: "80%",
      backgroundColor: color.primary_palette.franklin_purple,
      top: 0,
    },
  },
  right_side: {
    flexGrow: 0,
    width: "65%",
    paddingLeft: pxToRem(75),
  },
  blk_spacing: {
    paddingTop: pxToRem(30),
    position: "relative",
  },
  step_2a: {
    fontSize: pxToRem(20),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.gillsans_sb,
    paddingBottom: pxToRem(5),
    textTransform: "uppercase",
  },
  step_2a_des: {
    fontSize: pxToRem(16),
    color: color.secondary_palette.grays.up_in_smoke,
    ...tradework_theme.typography.styles.avenir_roman,
    paddingBottom: pxToRem(5),
    lineHeight: "normal !important",
    fontWeight: "normal !important",
  },
  step_2a_des_a: {
    fontSize: pxToRem(16),
    color: color.secondary_palette.grays.up_in_smoke,
    ...tradework_theme.typography.styles.avenir_roman,
    paddingBottom: pxToRem(5),
    lineHeight: "normal !important",
    fontWeight: "normal !important",
    margin: "0 auto",
    textAlign: "left",
  },
  circle: {
    textAlign: "center",
  },
  scroll: {
    minHeight: `${pxToRem(300)} !important`,
  },
  options: {
    height: pxToRem(385),
    border: `0px !important`,
    "& .MuiAutocomplete-listbox": {
      height: "360px !important",
      maxHeight: "360px !important",
    },
  },
  accolades: {
    width: `${pxToRem(416)} !important`,
    "& .MuiFormControl-root": {
      "&:hover": {
        border: `0px !important`,
      },
    },
    "& .MuiAutocomplete-input": {
      padding: "0px !important",
    },
    "& .MuiFormControl-marginNormal": {
      marginTop: "0px !important",
    },
  },
  college_accolades: {
    width: `${pxToRem(576)} !important`,
    "& .MuiFormControl-root": {
      paddingTop: pxToRem(10),
      "&:hover": {
        border: `0px !important`,
      },
    },
    "& .MuiAutocomplete-input": {
      padding: "0px !important",
      paddingTop: pxToRem(10),
    },
    "& .MuiAutocomplete-input": {
      height: pxToRem(26),
    },
    "& .MuiFormControl-marginNormal": {
      marginTop: "0px !important",
    },
  },
  accolades_drop_down_icon: {
    fontSize: pxToRem(12),
    paddingBottom: pxToRem(2),
    "& path": {
      stroke: `${color.secondary_palette.grays.shadow_gray} !important`,
    },
  },
  custom_accolade_txt: {
    lineHeight: "normal",
    margin: `${pxToRem(0)} ${pxToRem(5)}`,
    minWidth: pxToRem(80),
  },
  accolade_container_dropdown: {
    width: pxToRem(110),
    height: pxToRem(4),
    "& .MuiSvgIcon-root": {
      backgroundColor: "white !important",
      zIndex: 10,
      height: pxToRem(10),
      width: pxToRem(10),
      paddingBottom: pxToRem(4),
    },
    "& .MuiSelect-select": {
      paddingBottom: pxToRem(18),
      width: pxToRem(70),
      paddingRight: 0,
      fontSize: pxToRem(16),
      textOverflow: "ellipsis",
      ...tradework_theme.typography.styles.gillsans_r,
      "&:focus": {
        backgroundColor: "transparent !important",
      },
      "& .MuiSelect-root ": {
        backgroundColor: "transparent",
      },
    },
  },
  accolade_container_years: { marginTop: 0 },
  accolade_container_logo: { height: pxToRem(50), width: pxToRem(50) },
  accolade_container_logo_default: { height: pxToRem(63), width: pxToRem(63) },
  accolade_container: {
    height: pxToRem(90),
    width: pxToRem(70),
    marginRight: pxToRem(20),
    "& .MuiSvgIcon-root": {
      fontSize: `${pxToRem(10)} !important`,
      "& path": {
        stroke: color.secondary_palette.grays.shadow_gray,
      },
    },
  },
  accolade_container_close: {
    height: pxToRem(9),
    width: pxToRem(9),
    float: "right",
    cursor: "pointer",
  },
  accolade_close_2: { position: "relative", right: pxToRem(9) },
  circle_img: {
    width: pxToRem(130),
    height: pxToRem(130),
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    backgroundColor: color.secondary_palette.grays.background_gray,
    borderRadius: "50%",
    margin: "15px auto",
  },
  input_circle: {
    display: "none",
  },
  logo_btn: {
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    borderRadius: pxToRem(27),
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.avenir_sb,
    color: color.secondary_palette.grays.dim_grey,
    textTransform: "uppercase",
    paddingTop: pxToRem(2),
    paddingBottom: pxToRem(2),
    paddingRight: pxToRem(25),
    paddingLeft: pxToRem(25),
    height: pxToRem(25),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
      verticalAlign: "middle",
      marginRight: pxToRem(10),
    },
  },
  img_spacing: {
    paddingBottom: pxToRem(50),
  },
  rectangle: {
    width: pxToRem(360),
    height: pxToRem(120),
    margin: "15px auto",
  },
  square: {
    width: pxToRem(270),
    height: pxToRem(228),
    margin: "15px auto",
  },
  styled_cross: {
    backgroundColor: "transparent !important",
    height: "auto",
    "& .MuiChip-deleteIcon": {
      width: pxToRem(11),
      height: pxToRem(13),
      verticalAlign: "sub",
      // "& path": {
      //   fill: "#B2B2B2",
      // },
    },
    "& MuiChip-clickable": {
      "&:hover": {
        backgroundColor: "transparent !important",
      },
    },
    "& .MuiChip-deletable": {
      "&:focus": {
        backgroundColor: "transparent !important",
      },
    },
    "& .MuiChip-clickable": {
      "&:active": {
        boxShadow: `0px !important`,
      },
    },
    "& .MuiChip-label": {
      fontSize: pxToRem(16),
      textTransform: "capitalize",
      ...tradework_theme.typography.styles.gillsans_r,
      color: "#4a4a4a",
    },
  },
  sub_heading: {
    fontSize: pxToRem(16),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.avenir_bold,
    "& .MuiSvgIcon-root": {
      verticalAlign: "middle",
      fontSize: pxToRem(19),
      // color: color.form_colors.blueberry_purple,
      marginLeft: pxToRem(-22),
      marginRight: pxToRem(4),
    },
  },
  company_snapshot: {
    width: "100%",
    margin: 0,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    ...tradework_theme.typography.styles.NeutraText,
    border: 0,
    "& .MuiInputBase-input": {
      padding: `${pxToRem(0)} !important`,
    },
    "& .MuiInput-underline": {
      "&:hover": {
        "&:not(.Mui-disabled)": {
          "&:after": {
            borderBottom: "transparent !important",
          },
          "&:before": {
            borderBottom: "0px !important",
          },
        },
      },
    },
  },
  compant_pitch: {
    width: "97%",
    height: `${pxToRem(126)} !important`,
    outline: "none",
    resize: "none",
    padding: pxToRem(10),
    marginTop: pxToRem(10),
    marginBottom: pxToRem(5),
    ...tradework_theme.typography.styles.NeutraText,
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    overflow: "auto !important",
  },
  company_pitch_error: {
    border: "2px solid #C01F2A",
  },
  sub_fields: {
    color: color.primary_palette.black,
    fontSize: pxToRem(18),
    ...tradework_theme.typography.styles.gillsans_sb,
  },
  sub_extra_fields: {
    color: color.secondary_palette.grays.medium_gray,
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.gillsans_r,
    marginLeft: pxToRem(74),
  },
  sub_fields_2: {
    color: color.primary_palette.black,
    fontSize: pxToRem(18),
    ...tradework_theme.typography.styles.gillsans_sb,
    paddingRight: pxToRem(8),
  },
  sub_input: {
    width: "auto !important",
    margin: 0,
  },
  employee_field: {
    flexGrow: 1,
    width: "33%",
  },
  yearEstablished: {
    width: `${pxToRem(70)}!important`,
    marginLeft: pxToRem(5),
    textAlign: "center",
    borderTop: "0px !important",
    borderLeft: "0px !important",
    borderRight: "0px !important",
    "& p": {
      ...tradework_theme.typography.styles.avenir_light,
      fontSize: pxToRem(16),
      width: "100%",
      display: "block",
      textAlign: "center",
      marginTop: pxToRem(8),
    },
  },
  employees: {
    minWidth: pxToRem(200),
    textAlign: "center",
    "& .MuiSelect-select": {
      backgroundColor: `${color.primary_palette.white} !important`,
      padding: "0px !important",
      ...tradework_theme.typography.styles.avenir_light,
      fontSize: pxToRem(16),
    },
  },
  clientele: {
    width: `${pxToRem(240)} !important`,
    textAlign: "center",
    "& p": {
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
    },
  },
  clientele_2: {
    width: `${pxToRem(312)} !important`,
    textAlign: "center",
    "& p": {
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
    },
  },
  employee_year: {
    width: `${pxToRem(184)} !important`,
    marginTop: pxToRem(-6),
    "& p": {
      ...tradework_theme.typography.styles.gillsans_r,
      fontSize: pxToRem(16),
    },
    "& .MuiSvgIcon-root": {
      position: "relative",
      fontSize: `${pxToRem(10)} !important`,
      top: pxToRem(7),
      "& path": {
        stroke: color.secondary_palette.grays.shadow_gray,
      },
    },
    "& .MuiSelect-select": {
      backgroundColor: `${color.primary_palette.white} !important`,
      padding: "0px !important",
      ...tradework_theme.typography.styles.avenir_light,
      fontSize: pxToRem(16),
    },
  },
  employee_select: {
    borderTop: "0px !important",
    borderLeft: "0px !important",
    borderRight: "0px !important",
    marginTop: pxToRem(-6),
    marginLeft: pxToRem(5),
    "& p": {
      ...tradework_theme.typography.styles.gillsans_r,
      fontSize: pxToRem(16),
      width: "100%",
      display: "block",
      textAlign: "center",
      marginTop: pxToRem(8),
      overflow: "hidden",
      whiteSpace: "nowrap",
      textOverflow: "ellipsis",
    },
    "& .MuiSvgIcon-root": {
      position: "relative",
      fontSize: `${pxToRem(10)} !important`,
      top: pxToRem(5),
      "& path": {
        stroke: color.secondary_palette.grays.shadow_gray,
      },
    },
    "& .MuiSelect-select": {
      backgroundColor: `${color.primary_palette.white} !important`,
      padding: "0px !important",
      marginLeft: pxToRem(5),
      textAlign: "center",
      ...tradework_theme.typography.styles.avenir_light,
      color: color.primary_palette.black,
    },
  },
  vibe_select: {
    borderTop: "0px !important",
    borderLeft: "0px !important",
    borderRight: "0px !important",
    marginTop: pxToRem(-6),
    marginLeft: pxToRem(5),
    "& p": {
      ...tradework_theme.typography.styles.avenir_sb,
      color: color.secondary_palette.grays.light_gray,
      fontSize: pxToRem(16),
      width: "100%",
      display: "block",
      textAlign: "center",
      marginTop: pxToRem(8),
      overflow: "hidden",
      whiteSpace: "nowrap",
      textOverflow: "ellipsis",
    },
    "& .MuiSvgIcon-root": {
      position: "relative",
      fontSize: `${pxToRem(10)} !important`,
      top: pxToRem(5),
      "& path": {
        stroke: color.secondary_palette.grays.shadow_gray,
      },
    },
    "& .MuiSelect-select": {
      backgroundColor: `${color.primary_palette.white} !important`,
      padding: "0px !important",
      marginLeft: pxToRem(5),
      textAlign: "center",
      ...tradework_theme.typography.styles.avenir_light,
      color: color.primary_palette.black,
    },
  },
  companyVibe: {
    width: "446px !important",
  },
  dropdownInput: {
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    marginTop: pxToRem(5),
    height: pxToRem(23),
  },
  plus: {
    fontSize: pxToRem(20),
    color: color.secondary_palette.grays.shadow_gray,
    paddingLeft: pxToRem(5),
    borderLeft: `solid ${pxToRem(1)}`,
    cursor: "pointer",
  },
  accolades_plus: {
    fontSize: "30px",
    alignSelf: "center",
    paddingLeft: pxToRem(3),
    borderLeft: "0 !important",
  },
  drop_input: {
    border: 0,
    width: "84%",
    paddingLeft: pxToRem(7),
    ...tradework_theme.typography.styles.gillsans_r,
    fontSize: pxToRem(16),
    "&:focus": {
      outline: "none",
    },
    "&::placeholder": {
      color: color.primary_palette.black,
    },
  },
  drop_input2: {
    border: 0,
    width: "95%",
    paddingLeft: pxToRem(7),
    ...tradework_theme.typography.styles.gillsans_r,
    fontSize: pxToRem(16),
    "&:focus": {
      outline: "none",
    },
    "&::placeholder": {
      color: color.primary_palette.black,
    },
  },
  drop_input_accolades: {
    cursor: "pointer",
    border: 0,
    width: "93%",
    borderRight: `solid ${pxToRem(1)} ${
      color.secondary_palette.grays.shadow_gray
    }`,
    "&:focus": {
      outline: "none",
    },
    "&::placeholder": {
      color: color.primary_palette.black,
      paddingLeft: pxToRem(14),
      ...tradework_theme.typography.styles.gillsans_r,
      fontSize: pxToRem(16),
    },
  },
  College_Allegiances_dropdown: {
    "& .MuiSvgIcon-root": {
      fontSize: `${pxToRem(11)} !important`,
      top: pxToRem(10),
      right: pxToRem(10),
      "& path": {
        stroke: color.primary_palette.black,
      },
    },
    "& .MuiSelect-select": {
      backgroundColor: `${color.primary_palette.white} !important`,
    },
  },
  spacing_field: {
    paddingTop: pxToRem(20),
    paddingBottom: pxToRem(15),
  },
  trade_field: {
    display: "block",
    position: "relative",
    // flexWrap: "wrap",
  },
  addtrade_btn_blk: {
    justifyContent: "flex-end",
    clear: "both",
    width: "100%",
    paddingLeft: pxToRem(12),
  },

  Accolades_blk: {
    paddingTop: pxToRem(40),
    display: "block",
  },
  addtrade_btn: {
    textTransform: "capitalize",
    paddingTop: pxToRem(30),
    "& .MuiButton-label": {
      fontSize: pxToRem(16),
      color: color.secondary_palette.grays.dim_grey,
      ...tradework_theme.typography.styles.avenir_black_r,
      textTransform: "capitalize",
    },
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
      color: color.secondary_palette.grays.background_gray,
      paddingRight: pxToRem(5),
      marginTop: pxToRem(5),
      "& path": {
        "&:nth-child(1)": {
          fill: `${color.secondary_palette.grays.background_gray} ! important`,
        },
      },
    },
  },
  addtrade_btn_2: {
    textTransform: "capitalize",
    display: "block",
    marginLeft: "75%",
    "& .MuiButton-label": {
      fontSize: pxToRem(16),
      color: color.secondary_palette.grays.dim_grey,
      ...tradework_theme.typography.styles.avenir_black_r,
      textTransform: "capitalize",
    },
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(15),
      color: color.secondary_palette.grays.background_gray,
      paddingRight: pxToRem(5),
      marginTop: pxToRem(5),
      "& path": {
        "&:nth-child(1)": {
          fill: `${color.secondary_palette.grays.background_gray} ! important`,
        },
      },
    },
  },
  primary_field: {
    // width: "50%",
    marginRight: pxToRem(30),
    float: "left",
    paddingTop: pxToRem(33),
    "& .MuiFormControl-root": {
      width: "58%",
    },
    "& .MuiSvgIcon-root": {
      verticalAlign: "middle",
      fontSize: pxToRem(19),
      color: color.form_colors.blueberry_purple,
      marginLeft: pxToRem(-22),
      paddingRight: pxToRem(5),
    },
  },
  primary_field_2: {
    width: "38%",
    float: "left",
    paddingTop: pxToRem(33),
    "& .MuiFormControl-root": {
      width: "59%",
    },
    "& .MuiSvgIcon-root": {
      verticalAlign: "middle",
      fontSize: pxToRem(19),
      color: color.form_colors.blueberry_purple,
      marginLeft: pxToRem(-22),
      paddingRight: pxToRem(5),
    },
  },
  comp_aco: {
    width: "29%",
  },
  blck: {
    display: "block",
  },
  company_pitch_bg: {
    marginLeft: pxToRem(-10),
    paddingLeft: pxToRem(10),
    marginTop: pxToRem(20),
  },
  vibe: {
    width: "97%",
  },
  sub_title: {
    fontSize: pxToRem(12),
    ...tradework_theme.typography.styles.avenir_sb,
    color: color.secondary_palette.grays.dim_grey,
  },
  checkbox: {
    margin: 0,
    "& .MuiSvgIcon-root": {
      fontSize: `${pxToRem(22)} !important`,
    },
  },
  textfield: {
    flexGrow: 1,
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(35),
    },
    "& .MuiInputBase-input": {
      width: "125px !important",
      textOverflow: "ellipsis",
      paddingLeft: pxToRem(5),
      paddingBottom: pxToRem(2),
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.avenir_sb,
    },
    "& .MuiGrid-spacing-xs-1 > .MuiGrid-item": {
      padding: `${pxToRem(1)} !important`,
    },
    "& .MuiInput-root": {
      width: "90%",
    },
    "& .MuiInput-underline": {
      "&:before": {
        left: `${pxToRem(4)} !important`,
      },
    },
    "& .MuiFormControl-root": {
      marginTop: pxToRem(-6),
    },
  },
  textfield1: {
    visibility: "hidden",
    flexGrow: 1,
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(35),
    },
    "& .MuiInputBase-input": {
      width: "125px !important",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
      marginLeft: pxToRem(4),
      paddingBottom: pxToRem(2),
    },
    "& .MuiInput-root": {
      width: "90%",
    },
    "& .MuiInput-underline": {
      "&:before": {
        left: `${pxToRem(4)} !important`,
      },
    },
  },
  link: {
    fontSize: pxToRem(16),
    color: color.primary_palette.black,
    ...tradework_theme.typography.styles.avenir_roman,
    textAlign: "left",
  },
  linkCopy: {
    fontSize: pxToRem(16),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.avenir_roman,
    marginLeft: pxToRem(20),
    padding: `0px ${pxToRem(10)}`,
    borderRadius: pxToRem(2),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    cursor: "pointer",
  },
  setting: {
    color: color.primary_palette.franklin_purple,
    borderBottom: `solid ${pxToRem(1)} ${
      color.primary_palette.franklin_purple
    }`,
    marginLeft: pxToRem(5),
  },
  spacing: {
    paddingTop: pxToRem(20),
    position: "relative",
  },
  later_btn: {
    justifyContent: "flex-start",
    color: color.finish_color,
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_roman,
    cursor: "pointer",
    textDecoration: "underline",
    display: "flex",
    flexGrow: 1,
    "&:hover": {
      color: color.primary_palette.franklin_purple,
    },
  },
  actionButtons: {
    padding: 0,
    color: color.primary_palette.franklin_purple,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.franklin_purple,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.franklin_purple} !important`,
      border: `1px solid ${color.primary_palette.franklin_purple}`,
    },
  },
  greenactionButtons: {
    padding: 0,
    color: color.primary_palette.pine_green,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.pine_green,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.pine_green} !important`,
      border: `1px solid ${color.primary_palette.pine_green}`,
    },
  },
  cw_error: {
    fontSize: pxToRem(12),
    ...tradework_theme.typography.styles.avenir_black_r,
    color: color.primary_palette.christmas_red,
    paddingTop: pxToRem(5),
    paddingBottom: pxToRem(5),
    clear: "both",
  },
  fill_all_error: {
    position: "absolute",
    top: "-24px",
    right: "33px",
  },
  trade_error: {
    position: "absolute",
    top: pxToRem(60),
    padding: 0,
  },
  cw_error_60: {
    fontSize: pxToRem(12),
    ...tradework_theme.typography.styles.avenir_black_r,
    color: color.primary_palette.christmas_red,
    paddingTop: pxToRem(5),
    paddingBottom: pxToRem(5),
    clear: "both",
  },
  number_text: {
    "& div": {
      marginTop: pxToRem(-12),
    },
    "& span": {
      ...tradework_theme.typography.styles.avenir_sb,
      fontSize: pxToRem(15),
      color: color.secondary_palette.grays.dim_grey,
    },
  },
  info: {
    paddingRight: pxToRem(3),
    marginTop: pxToRem(-3),
  },
  years_scroll: {
    minHeight: `${pxToRem(360)} !important`,
  },
  acco_scroll: {
    minHeight: `${pxToRem(363)} !important`,
    "& .MuiMenuItem-root": {
      padding: 0,
    },
    "& .MuiButtonBase-root ": {
      padding: 0,
      paddingRight: pxToRem(8),
    },
  },
  employee_scroll: {
    minHeight: `${pxToRem(50)} !important`,
    "& li": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.gillsans_r,
      height: pxToRem(15),
    },
  },
  vibes_scroll: {
    minHeight: `${pxToRem(300)} !important`,
    "& li": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.gillsans_r,
      height: pxToRem(15),
    },
    "& .MuiSvgIcon-root": {
      width: pxToRem(15),
    },
    "& .MuiButtonBase-root ": {
      padding: 0,
      paddingLeft: pxToRem(5),
      paddingRight: pxToRem(5),
    },
  },
  btn_width: {
    display: "block",
    width: "90%",
  },
  txt_align_left: {
    textAlign: "left",
  },
  pos_r: {
    position: "relative",
  },
  requried_icon: {
    paddingTop: pxToRem(40),
    paddingBottom: pxToRem(24),
  },
  vibe_space: {
    paddingTop: pxToRem(27),
  },
  sub_space: {
    marginTop: pxToRem(17),
  },
  pad_t_25: {
    paddingTop: pxToRem(25),
  },
  padding_top_20: {
    paddingTop: pxToRem(20),
  },
  accolade_container1: {
    border: "solid 0.0625rem #535353",
    padding: "5px 20px",
    width: pxToRem(570),
  },
  padding_top_15: {
    paddingTop: pxToRem(15),
  },
  step_spacing: {
    textAlign: "left",
    paddingTop: pxToRem(8),
  },
  btn_spacing: {
    paddingTop: pxToRem(34),
    paddingBottom: pxToRem(117),
  },
  first_btn: {
    justifyContent: "flex-end",
    flexGrow: 1,
    position: "relative",
  },
  first_btn_width: {
    width: pxToRem(75),
  },
  second_btn_width: {
    width: pxToRem(187),
  },
  second_btn_clr: {
    color: "#c01f2a",
    border: "solid 0.0625rem #c01f2a",
    "& .MuiButton-label": {
      color: "#c01f2a",
    },
  },
  venues: {
    width: `${pxToRem(368)} !important`,
  },
  insta_underline: {
    ...tradework_theme.typography.styles.avenir_sb,
    textAlign: "center",
    fontSize: pxToRem(15),
    color: color.primary_palette.franklin_purple,
    cursor: "pointer",
    position: "relative",
    bottom: pxToRem(10),
  },
  insta_link: {
    textAlign: "center",
    padding: `${pxToRem(25)}${pxToRem(0)}`,
    width: pxToRem(573),
    margin: "0 auto",
  },
  position_relative: {
    position: "relative",
  },
  recommended_350: {
    position: "absolute",
    left: pxToRem(0),
  },
  custom_accolade_fields: {
    marginLeft: pxToRem(27),
    "& .MuiInput-root": {
      marginBottom: pxToRem(5),
      width: pxToRem(260),
      ...tradework_theme.typography.styles.gillsans_r,
      fontSize: pxToRem(16),
      "& ::placeholder": {
        ...tradework_theme.typography.styles.gillsans_light,
      },
    },
    "& .MuiInputBase-input": {
      paddingBottom: pxToRem(0),
    },
  },
  custom_accolade_fields_large: {
    marginLeft: pxToRem(10),
    "& .MuiInput-root": {
      marginBottom: pxToRem(5),
      width: pxToRem(350),
      ...tradework_theme.typography.styles.gillsans_r,
      fontSize: pxToRem(16),
      "& ::placeholder": {
        ...tradework_theme.typography.styles.gillsans_light,
      },
    },
    "& .MuiInputBase-input": {
      paddingBottom: pxToRem(0),
    },
  },
  accolade_container_dropdown_new: {
    width: `${pxToRem(76)} !important`,
    borderTop: "0 !important",
    borderRight: "0 !important",
    borderLeft: "0 !important",
    borderBottom: `1px solid ${color.secondary_palette.grays.shadow_gray}`,
    position: "relative",
    height: pxToRem(16),
    "&::placeholder": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.gillsans_light,
    },
    "& p": {
      fontSize: pxToRem(16),
      whiteSpace: "nowrap",
      ...tradework_theme.typography.styles.gillsans_r,
    },
    "& .MuiSvgIcon-root": {
      zIndex: 10,
      fontSize: `${pxToRem(10)} !important`,
      paddingBottom: pxToRem(4),
      top: pxToRem(6),
      position: "absolute",
      right: 0,
    },
    "& .MuiSelect-select": {
      paddingBottom: pxToRem(0),
      width: "100%",
      paddingRight: 0,
      fontSize: pxToRem(12),
      textOverflow: "ellipsis",
      ...tradework_theme.typography.styles.avenir_bold,
      "&:focus": {
        backgroundColor: "transparent !important",
      },
      "& .MuiSelect-root ": {
        backgroundColor: "transparent",
      },
    },
    "&.year3": {
      width: `${pxToRem(120)} !important`,
    },
    "&.year4": {
      width: `${pxToRem(150)} !important`,
    },
    "&.year5": {
      width: `${pxToRem(180)} !important`,
    },
    "&.year6": {
      width: `${pxToRem(210)} !important`,
    },
    "&.year7": {
      width: `${pxToRem(240)} !important`,
    },
    "&.year8": {
      width: `${pxToRem(280)} !important`,
    },
    "&.year9, &.year10, &.year11, &.year12, &.year13, &.year14, &.year15": {
      width: `${pxToRem(400)} !important`,
    },
  },
  accolade_container_dropdown_year: {
    width: `${pxToRem(76)} !important`,
    borderTop: "0 !important",
    borderRight: "0 !important",
    borderLeft: "0 !important",
    borderBottom: `1px solid ${color.secondary_palette.grays.shadow_gray}`,
    position: "relative",
    height: pxToRem(16),
    "&::placeholder": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.gillsans_light,
    },
    "& p": {
      fontSize: pxToRem(16),
      whiteSpace: "nowrap",
      ...tradework_theme.typography.styles.gillsans_r,
      overflow: "hidden",
      textOverflow: "ellipsis",
    },
    "& .MuiSvgIcon-root": {
      zIndex: 10,
      fontSize: `${pxToRem(10)} !important`,
      paddingBottom: pxToRem(4),
      top: pxToRem(6),
      position: "absolute",
      right: 0,
    },
    "& .MuiSelect-select": {
      paddingBottom: pxToRem(0),
      width: "100%",
      paddingRight: 0,
      fontSize: pxToRem(12),
      textOverflow: "ellipsis",
      ...tradework_theme.typography.styles.avenir_bold,
      "&:focus": {
        backgroundColor: "transparent !important",
      },
      "& .MuiSelect-root ": {
        backgroundColor: "transparent",
      },
    },
    "&.year3": {
      width: `${pxToRem(120)} !important`,
    },
    "&.year4": {
      width: `${pxToRem(150)} !important`,
    },
    "&.year5": {
      width: `${pxToRem(180)} !important`,
    },
    "&.year6": {
      width: `${pxToRem(210)} !important`,
    },
    "&.year7": {
      width: `${pxToRem(240)} !important`,
    },
    "&.year8": {
      width: `${pxToRem(280)} !important`,
    },
    "&.year9, &.year10, &.year11, &.year12, &.year13, &.year14, &.year15": {
      width: `${pxToRem(400)} !important`,
    },
  },

  accolade_container_years: {
    width: `${pxToRem(76)} !important`,
    borderTop: "0 !important",
    marginLeft: pxToRem(20),
    marginTop: pxToRem(6),
    borderRight: "0 !important",
    borderLeft: "0 !important",
    borderBottom: `1px solid ${color.secondary_palette.grays.shadow_gray}`,
    position: "relative",
    height: pxToRem(16),
    "&::placeholder": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.gillsans_light,
    },
    "& p": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.gillsans_r,
    },
    "& .MuiSvgIcon-root": {
      zIndex: 10,
      fontSize: `${pxToRem(10)} !important`,
      paddingBottom: pxToRem(4),
      top: pxToRem(6),
      position: "absolute",
      right: 0,
    },
    "& .MuiSelect-select": {
      paddingBottom: pxToRem(0),
      width: "100%",
      paddingRight: 0,
      fontSize: pxToRem(12),
      textOverflow: "ellipsis",
      ...tradework_theme.typography.styles.avenir_bold,
      "&:focus": {
        backgroundColor: "transparent !important",
      },
      "& .MuiSelect-root ": {
        backgroundColor: "transparent",
      },
    },
  },
  accoladeHeadingTxt: {
    marginTop: pxToRem(6),
    marginRight: pxToRem(8),
    width: pxToRem(70),
    textAlign: "right",
  },
  accolade_container_new: {
    width: `${pxToRem(76)} !important`,
    borderTop: "0 !important",
    // marginLeft: pxToRem(20),
    borderRight: "0 !important",
    borderLeft: "0 !important",
    borderBottom: `1px solid ${color.secondary_palette.grays.shadow_gray}`,
    position: "relative",
    height: pxToRem(16),
    "&::placeholder": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.gillsans_light,
    },
    "& p": {
      fontSize: pxToRem(16),
      ...tradework_theme.typography.styles.gillsans_r,
    },
    "& .MuiSvgIcon-root": {
      zIndex: 10,
      fontSize: `${pxToRem(10)} !important`,
      paddingBottom: pxToRem(4),
      top: pxToRem(6),
      position: "absolute",
      right: 0,
    },
    "& .MuiSelect-select": {
      paddingBottom: pxToRem(0),
      width: "100%",
      paddingRight: 0,
      fontSize: pxToRem(12),
      textOverflow: "ellipsis",
      ...tradework_theme.typography.styles.avenir_bold,
      "&:focus": {
        backgroundColor: "transparent !important",
      },
      "& .MuiSelect-root ": {
        backgroundColor: "transparent",
      },
    },
  },
  flex_wrap: {
    flexWrap: "wrap",
  },
  placeholder: {
    "& p": {
      color: `${color.placeholder_color} !important`,
      ...tradework_theme.typography.styles.gillsans_light,
      // opacity: 0.65,
    },
  },
  clear_accolade_inp: {
    height: pxToRem(9),
    width: pxToRem(9),
    position: "absolute",
    bottom: pxToRem(8),
    right: pxToRem(2),
    cursor: "pointer",
  },
  redIcon: {
    color: color.primary_palette.christmas_red,
  },
  purpleIcon: {
    color: color.form_colors.blueberry_purple,
  },
  smallPurpleIcon: {
    color: color.form_colors.blueberry_purple,
    fontSize: pxToRem(15),
    position: "absolute",
    left: pxToRem(-14),
  },
  smallPurpleIconTitle: {
    color: color.form_colors.blueberry_purple,
    fontSize: pxToRem(15),
    position: "absolute",
    left: pxToRem(24),
  },
  helper_name: {
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_sb,
    color: color.primary_palette.black,
    width: pxToRem(186),
    margin: "0 auto",
    marginTop: pxToRem(-12),
  },
});

export default styles;
