import { withStyles } from "@material-ui/core/styles";

import tradework_theme, { color, pxToRem } from "../../../utilities/themes";

const styles = withStyles({
  company_highlight: {
    display: "block",
  },
  justify: {
    justifyContent: "center",
  },
  center_text: {
    display: "block",
    width: "100%",
    textAlign: "center",
  },
  main_des: {
    ...tradework_theme.typography.styles.gillsans_light,
    fontSize: pxToRem(35),
    textTransform: "uppercase",
    height: pxToRem(40),
  },
  main_brand: {
    ...tradework_theme.typography.styles.avenir_roman,
    fontSize: pxToRem(20),
  },
  main_head: {
    display: "block",
    paddingLeft: pxToRem(90),
    paddingRight: pxToRem(90),
  },
  step_2a: {
    fontSize: pxToRem(25),
    color: color.primary_palette.franklin_purple,
    ...tradework_theme.typography.styles.gillsans_sb,
    textTransform: "uppercase",
  },
  step_2a_des: {
    fontSize: pxToRem(16),
    color: color.secondary_palette.grays.up_in_smoke,
    ...tradework_theme.typography.styles.avenir_roman,
    paddingBottom: pxToRem(5),
  },
  step_a_b: {
    fontSize: pxToRem(15),
    ...tradework_theme.typography.styles.avenir_black_r,
    color: color.greyish_brown,
    paddingTop: pxToRem(15),
    paddingBottom: pxToRem(13),
  },

  // layout styles
  layout1_blk: {
    paddinTop: pxToRem(25),
    paddingBottom: pxToRem(40),
  },
  layout_block: {
    display: "block",
    paddinTop: pxToRem(25),
    paddingBottom: pxToRem(40),
  },
  layout1: {
    display: "block",
    marginRight: pxToRem(10),
  },
  layout1_in4: {
    display: "block",
    marginLeft: pxToRem(14),
    marginTop: pxToRem(7),
  },
  layout1_img: {
    display: "block",
    width: pxToRem(350),
    margin: "0 auto",
    marginTop: pxToRem(24),
  },
  layout2_img: {
    display: "block",
    width: pxToRem(350),
    margin: "0 auto",
  },
  layout2_img_layout4: {
    display: "flex",
  },
  highlight: {
    "& svg": {
      marginLeft: pxToRem(2),
      width: pxToRem(14),
      height: pxToRem(14),
      marginBottom: pxToRem(2),
    },
  },
  highlight_body_content: {
    paddingBottom: pxToRem(4),
    "& svg": {
      marginLeft: pxToRem(2),
      width: pxToRem(14),
      height: pxToRem(14),
      marginBottom: pxToRem(2),
    },
  },
  high_blk_1: {
    display: "block",
    paddingTop: pxToRem(20),
    width: pxToRem(500),
    position: "relative",
    "& span": {
      ...tradework_theme.typography.styles.avenir_sb,
      fontSize: pxToRem(15),
    },
  },
  high_blk: {
    display: "block",
    width: pxToRem(500),
    position: "relative",
    "& span": {
      ...tradework_theme.typography.styles.avenir_sb,
      fontSize: pxToRem(15),
    },
  },
  high_blk_3: {
    display: "block",
    paddingTop: pxToRem(5),
    width: pxToRem(500),
    position: "relative",
    "& span": {
      ...tradework_theme.typography.styles.avenir_sb,
      fontSize: pxToRem(15),
    },
  },
  high_blk_4: {
    display: "block",
    margin: "0 auto",
    paddingTop: pxToRem(40),
    width: pxToRem(500),
    position: "relative",
    "& span": {
      ...tradework_theme.typography.styles.avenir_sb,
      fontSize: pxToRem(15),
    },
  },
  paddingTop_0: {
    padding: 0,
  },
  high_blk_l2: {
    display: "block",
    paddingTop: pxToRem(20),
    width: pxToRem(500),
    margin: "0 auto",
    position: "relative",
    "& span": {
      ...tradework_theme.typography.styles.avenir_sb,
      fontSize: pxToRem(15),
    },
  },
  highlight_input: {
    margin: 0,
    width: "100%",
    fontSize: pxToRem(18),
    ...tradework_theme.typography.styles.NeutraText,
    // fontStyle: "oblique",
    "& .MuiInputBase-root": {
      padding: 0,
      width: "100%",
    },
    "& .MuiInputBase-input": {
      padding: `${pxToRem(0)} !important`,
    },
  },
  l1_description: {
    height: `${pxToRem(243)} !important`,
    width: "100%",
    fontSize: `${pxToRem(16)} !important`,
    ...tradework_theme.typography.styles.NeutraText,
    color: color.primary_palette.black,
    resize: "none",
  },
  bodycontent_textarea: {
    height: `${pxToRem(137)} !important`,
    width: "100%",
    padding: 0,
    resize: "none",
    outline: "none",
    ...tradework_theme.typography.styles.NeutraText,
    color: color.primary_palette.black,
  },
  Paragraph1_textarea: {
    width: pxToRem(500),
    padding: 0,
    resize: "none",
    outline: "none",
    fontSize: `${pxToRem(16)} !important`,
    ...tradework_theme.typography.styles.NeutraText,
    color: color.primary_palette.black,
  },
  l2_content_wrap: {
    marginTop: pxToRem(32),
  },
  l2_content1: {
    marginTop: pxToRem(15),
  },
  l2_content2: {
    marginTop: pxToRem(15),
    marginLeft: pxToRem(20),
  },
  l2_para1: {
    height: `${pxToRem(140)} !important`,
  },
  l2_para2: {
    height: `${pxToRem(149)} !important`,
  },
  l3_content1: {
    marginTop: pxToRem(10),
    marginRight: pxToRem(18),
  },
  l4_l5_para1: {
    height: `${pxToRem(146)} !important`,
  },
  upload_box: {
    width: pxToRem(225),
    height: pxToRem(315),
    margin: "0 auto",
  },
  upload_box_L2: {
    width: pxToRem(342),
    height: pxToRem(228),
    margin: "0 auto",
    marginTop: pxToRem(30),
  },
  upload_box_L2_1: {
    width: pxToRem(342),
    height: pxToRem(228),
    margin: "0 auto",
    // marginTop: pxToRem(50),
  },
  upload_box_L2_2: {
    width: pxToRem(342),
    height: pxToRem(228),
    margin: "0 auto",
  },
  bodycontent_textarea_l3: {
    height: `${pxToRem(402)} !important`,
    width: "100%",
    padding: 0,
    resize: "none",
    outline: "none",
    fontSize: `${pxToRem(16)} !important`,
    ...tradework_theme.typography.styles.NeutraText,
    color: color.primary_palette.black,
  },
  layouts: {
    paddingLeft: pxToRem(100),
    paddingRight: pxToRem(100),
  },
  layout_spacing: {
    flexGrow: 1,
    display: "block",
    "& .MuiTypography-root": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.avenir_sb,
      color: color.primary_palette.black,
      textTransform: "capitalize",
      paddingLeft: pxToRem(5),
    },
    "& .MuiButtonBase-root": {
      padding: 0,
      marginTop: pxToRem(-3),
    },
  },
  later_btn: {
    justifyContent: "flex-start",
    color: color.finish_color,
    fontSize: pxToRem(16),
    ...tradework_theme.typography.styles.avenir_roman,
    cursor: "pointer",
    textDecoration: "underline",
    display: "flex",
    flexGrow: 1,
    "&:hover": {
      color: color.primary_palette.franklin_purple,
    },
  },
  actionButtons: {
    width: pxToRem(170),
    padding: 0,
    color: color.primary_palette.franklin_purple,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.franklin_purple,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.franklin_purple} !important`,
      border: `1px solid ${color.primary_palette.franklin_purple}`,
    },
  },
  greenactionButtons: {
    width: pxToRem(170),
    padding: 0,
    color: color.primary_palette.pine_green,
    marginRight: pxToRem(25),
    borderRadius: pxToRem(27),
    border: `solid ${pxToRem(1)} ${color.primary_palette.pine_green}`,
    "& .MuiButton-label": {
      fontSize: pxToRem(15),
      ...tradework_theme.typography.styles.gillsans_sb,
      color: color.primary_palette.franklin_purple,
    },
    "&:hover": {
      backgroundColor: `${color.shaded_gray_wc} !important`,
      color: `${color.primary_palette.pine_green} !important`,
      border: `1px solid ${color.primary_palette.pine_green}`,
    },
  },
  skip_btn: {
    width: pxToRem(90),
  },
  cw_error: {
    fontSize: pxToRem(12),
    ...tradework_theme.typography.styles.avenir_black_r,
    color: color.primary_palette.christmas_red,
    paddingTop: pxToRem(5),
    paddingBottom: pxToRem(5),
    clear: "both",
  },
  fill_all_error: {
    position: "absolute",
    top: "-24px",
    right: "33px",
  },
  done_btn_mask: {
    "& .MuiButton-label": {
      opacity: "0.2",
    },
  },
  layout_img: {
    width: "auto",
    height: pxToRem(78),
    marginLeft: pxToRem(-8),
  },
  layout_img_pattern: {
    width: "auto",
    height: pxToRem(35),
    margin: `${pxToRem(20)} ${pxToRem(0)}`,
    marginLeft: pxToRem(-8),
  },
  padding_top_10: {
    paddingTop: pxToRem(10),
  },
  pad_b_20: {
    paddingBottom: `${pxToRem(20)} !important`,
  },
  addbtn_padding: { padding: `${pxToRem(0)} ${pxToRem(12)} !important` },
  btn_blk: {
    width: "60%",
    margin: "0 auto",
    // paddingTop: pxToRem(20),
    paddingBottom: pxToRem(20),
  },
  btn_alignment: {
    flexGrow: 1,
    justifyContent: "flex-end",
    position: "relative",
  },
  margin_left_20: {
    marginLeft: pxToRem(20),
  },
  margin_right_20: {
    marginRight: pxToRem(20),
  },
  mr_top_l3: {
    marginTop: pxToRem(15),
  },
  margin_Top: {
    marginTop: pxToRem(35),
  },
  margin_Top_9: {
    marginTop: pxToRem(28),
  },
  margin_Top_10: {
    marginTop: pxToRem(10),
  },
  errormsg: {
    fontSize: pxToRem(12),
    ...tradework_theme.typography.styles.avenir_black_r,
    color: color.primary_palette.christmas_red,
    "& img": {
      marginRight: pxToRem(5),
    },
    marginTop: pxToRem(20),
  },
  tmcWarning: {
    position: "absolute",
    height: pxToRem(50),
    width: pxToRem(60),
    bottom: pxToRem(10),
    right: pxToRem(-15),
  },
  layout5_content_blk: {
    paddingTop: pxToRem(10),
    "& .Paragraph1_textarea": {
      height: "146px !important",
    },
  },
  logo_btn_red: {
    border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    borderRadius: pxToRem(27),
    fontSize: pxToRem(15),
    marginRight: pxToRem(210),
    ...tradework_theme.typography.styles.avenir_sb,
    color: `${color.primary_palette.christmas_red} !important`,
    textTransform: "uppercase",
    padding: `${pxToRem(1)} ${pxToRem(9)}`,
    height: pxToRem(25),
    "& .MuiSvgIcon-root": {
      fontSize: pxToRem(14),
      verticalAlign: "middle",
      marginRight: pxToRem(10),
    },
    "&:hover": {
      color: `${color.primary_palette.christmas_red} !important`,
      backgroundColor: "#FFFFFF !important",
      border: `solid ${pxToRem(1)} ${color.primary_palette.christmas_red}`,
    },
  },
  helper_name: {
    fontSize: pxToRem(14),
    ...tradework_theme.typography.styles.avenir_sb,
    color: color.primary_palette.black,
    width: pxToRem(186),
    margin: "0 auto",
    marginTop: pxToRem(-12),
  },
});

export default styles;
