import React from "react";
import PropTypes from "prop-types";
import { withStyles } from "@material-ui/core/styles";
import MuiMenu from "@material-ui/core/Menu";
import MenuItem from "@material-ui/core/MenuItem";
import SubMenuItem from "./SubMenuItem";
import { color } from "../../utilities/themes";

const useStyles = {
  menuItem: {
    borderRadius: 0,
    border: `1px solid ${color.form_colors.blueberry_purple}`,
    justifyContent: "space-between",
  },
};

class Menu extends React.Component {
  renderMenuItems = () => {
    const { menuItems, onMenuSelected, PaperProps } = this.props;
    if (menuItems && menuItems.length) {
      return menuItems.map((menuItem) => {
        if (menuItem && (menuItem.subMenuItems || menuItem.renderItem)) {
          return (
            <SubMenuItem
              key={menuItem.key}
              menuItemKey={menuItem.key}
              caption={menuItem.caption}
              icon={menuItem.icon}
              PaperProps={PaperProps}
              renderItem={menuItem.renderItem}
              menuItems={menuItem.subMenuItems}
              onMenuSelected={onMenuSelected}
            />
          );
        }

        return (
          <MenuItem
            key={menuItem.key}
            onClick={() => onMenuSelected(menuItem)}
            style={{
              borderTop: menuItem.borderTop ? "1px solid" : "",
              borderBottom: menuItem.borderBottom ? "1px solid" : "",
              display: "block",
            }}
          >
            <span
              style={{
                paddingLeft: menuItem.isOption ? "10px" : "0px",
              }}
            >
              {menuItem.caption}{" "}
              {menuItem.caption === "Good Citizen Status" && (
                <span style={{ color: "#5E94E0", fontSize: "12px" }}>
                  TO COME
                </span>
              )}
            </span>
            {menuItem?.subMenuOptions?.map((subItems) => {
              return (
                <div
                  style={{
                    paddingLeft: subItems?.noPadding ? "0px" : "10px",
                    color:
                      subItems.name === "Dashboard - Your To Do List!" &&
                      color.primary_palette.christmas_red,
                  }}
                >
                  {subItems.name}
                </div>
              );
            })}

            {menuItem && menuItem.icon && (
              <img
                src={menuItem.icon}
                style={{
                  verticalAlign: "middle",
                  height: "16px",
                  width: "48px",
                }}
              />
            )}
          </MenuItem>
        );
      });
    }
    return null;
  };

  render() {
    const {
      anchorElement,
      open,
      onClose,
      PaperProps,
      renderItem,
      anchorOrigin,
    } = this.props;
    return (
      <MuiMenu
        anchorEl={anchorElement}
        anchorOrigin={anchorOrigin}
        PaperProps={PaperProps}
        open={open}
        onClose={onClose}
      >
        {renderItem ? renderItem : this.renderMenuItems()}
      </MuiMenu>
    );
  }
}

Menu.propTypes = {
  open: PropTypes.bool.isRequired,
  menuItems: PropTypes.array.isRequired,
  onMenuSelected: PropTypes.func,
  PaperProps: PropTypes.object,
  anchorElement: PropTypes.any,
  onClose: PropTypes.func.isRequired,
};

export default withStyles(useStyles, { withTheme: true })(Menu);
