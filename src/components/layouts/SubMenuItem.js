import React from "react";
import PropTypes from "prop-types";
import MenuItem from "@material-ui/core/MenuItem";
import Menu from "./Menu";
import { color } from "../../utilities/themes";
import withStyles from "@material-ui/core/styles/withStyles";

const useStyles = {
  subMenuItem: {
    display: "flex",
    justifyContent: "space-between",
  },
  activeSubMenu: {
    backgroundColor: color.primary_palette.franklin_purple,
    color: color.primary_palette.white,
  },
};

class SubMenuItem extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      menuOpen: false,
      selectedKey: null,
      anchorElement: null,
    };
  }

  setAnchorElement = (node) => {
    this.setState({
      anchorElement: node,
    });
  };

  handleItemClick = (event, key) => {
    if (!this.state.anchorElement) {
      this.setAnchorElement(event.currentTarget);
    }
    this.setState({
      selectedKey: !this.state.menuOpen ? key : null,
      menuOpen: !this.state.menuOpen,
    });
    // this.menuOpen = !this.menuOpen;
  };

  handleSubMenuClose = () => {
    this.setState({
      selectedKey: null,
      menuOpen: false,
    });
  };

  render() {
    const {
      menuItemKey,
      caption,
      menuItems,
      onMenuSelected,
      PaperProps,
      renderItem,
      classes,
    } = this.props;
    return (
      <React.Fragment>
        <MenuItem
          onClick={(e) => this.handleItemClick(e, menuItemKey)}
          className={`${classes.subMenuItem} ${
            this.state.selectedKey === menuItemKey ? classes.activeSubMenu : ""
          }`}
        >
          {caption}&nbsp;&nbsp;
          <img
            src={
              this.state.selectedKey === menuItemKey
                ? "assets/icons/arrow_list_icon_white.svg"
                : "assets/icons/arrow_list_icon.svg"
            }
            alt="coin_icon"
          />
        </MenuItem>
        <Menu
          PaperProps={{
            style: {
              marginLeft: "7px",
              paddingLeft: "10px",
              paddingRight: "10px",
              maxHeight: "100%",
              width: "210px",
              ...PaperProps.style,
            },
            className: PaperProps.className,
          }}
          anchorOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
          open={this.state.menuOpen}
          menuItems={menuItems}
          renderItem={renderItem}
          onMenuSelected={onMenuSelected}
          anchorElement={this.state.anchorElement}
          onClose={this.handleSubMenuClose}
        />
      </React.Fragment>
    );
  }
}

SubMenuItem.propTypes = {
  caption: PropTypes.string.isRequired,
  menuItems: PropTypes.array.isRequired,
};

export default withStyles(useStyles, { withTheme: true })(SubMenuItem);
