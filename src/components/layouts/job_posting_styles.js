import { withStyles } from "@material-ui/core/styles";
import tradework_theme, { color, pxToRem } from "../../utilities/themes";
// import tradework_theme, { pxToRem, color } from "../../utilities/themes";

const styles = withStyles({
  landing_wrapper: {
    padding: `${pxToRem(45)} ${pxToRem(0)} ${pxToRem(20)} ${pxToRem(30)}`,
    margin: `${pxToRem(0)} ${pxToRem(10)}`,
  },
  position_title: {
    marginLeft: pxToRem(31),
    height: pxToRem(30),
    marginRight: pxToRem(10),
  },
  body_wrapper: {
    paddingTop: pxToRem(40),
  },
  search_inp_row: {
    justifyContent: "flex-end",
    position: "relative",
    marginBottom: pxToRem(-17),
  },
  search_icon: {
    width: pxToRem(19),
    height: pxToRem(20),
    position: "absolute",
    right: pxToRem(15),
    top: pxToRem(5),
  },
  search_inp: {
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    width: pxToRem(306),
    height: pxToRem(30),
    backgroundColor: color.primary_palette.white,
    borderRadius: pxToRem(5),
    ...tradework_theme.typography.styles.gillsans_light,
    padding: `${pxToRem(0)} ${pxToRem(30)} ${pxToRem(0)} ${pxToRem(5)}`,
    marginRight: pxToRem(10),
  },
  popup_icon_eye: {
    width: pxToRem(25),
    height: pxToRem(20),
    marginTop: pxToRem(10),
  },
  popup_icon_save: {
    width: pxToRem(22),
    // height: pxToRem(20),
    marginTop: pxToRem(10),
  },
  redBell_icon: {
    width: pxToRem(22),
    height: pxToRem(24),
    marginTop: pxToRem(7),
  },
  popup_icon_sort: {
    width: pxToRem(24),
    height: pxToRem(15),
    marginTop: pxToRem(10),
  },
  popup_icon_border_disabled: {
    border: `solid ${pxToRem(2.4)} ${color.first_quarter_silver}`,
    borderRadius: pxToRem(8.4),
    marginRight: pxToRem(17),
    marginLeft: pxToRem(2),
    height: pxToRem(35),
    width: pxToRem(36),
    textAlign: "center",
  },
  popup_icon_border: {
    border: `solid ${pxToRem(2.4)} ${color.first_quarter_silver}`,
    borderRadius: pxToRem(8.4),
    marginRight: pxToRem(17),
    marginLeft: pxToRem(2),
    height: pxToRem(35),
    width: pxToRem(36),
    textAlign: "center",
    cursor: "pointer",
    "& .hover_display": {
      display: "none",
    },
    "&:hover": {
      border: `solid ${pxToRem(2.4)} ${color.speculative_ashen}`,
      "& .hover_display": {
        display: "block",
      },
    },
  },
  oblique: {
    fontStyle: "oblique",
  },
  iconText: {
    width: pxToRem(45),
    textAlign: "center",
  },
  sortByText: {
    width: pxToRem(34),
    textAlign: "center",
    marginLeft: pxToRem(4),
  },
  margin_top_20: {
    marginTop: pxToRem(-20),
  },
  hover_content: {
    width: pxToRem(200),
    border: `solid ${pxToRem(1)} ${color.Dormitory}`,
    padding: pxToRem(4),
    position: "absolute",
    right: pxToRem(69),
    backgroundColor: color.pale_grey,
    borderRadius: pxToRem(4),
    boxShadow: "0 1px 4px 0 rgba(0, 0, 0, 0.5)",
    "& .triangle": {
      borderBottomColor: color.Dormitory,
      position: "absolute",
      display: "block",
      width: 0,
      height: 0,
      borderStyle: "solid",
      borderWidth: pxToRem(8),
      borderTopWidth: pxToRem(0),
      borderColor: "transparent",
      right: pxToRem(-12),
      top: "41%",
      transform: "rotate(90deg)",
      "&:after": {
        top: pxToRem(11.5),
        margin: pxToRem(-10),
        content: "''",
        borderBottomColor: color.pale_grey,
        position: "absolute",
        display: "block",
        width: 0,
        height: 0,
        borderStyle: "solid",
        borderWidth: pxToRem(10),
        borderTopWidth: pxToRem(0),
        borderColor: "transparent",
      },
    },
  },
  position_rel: {
    position: "relative",
  },
  txtLeft: {
    textAlign: "left",
  },
  disableIcon: {
    pointerEvents: "none",
    opacity: 0.8,
  },
  no_padding: {
    paddingLeft: 0,
  },
  savedsearch_inp: {
    width: pxToRem(166),
    height: pxToRem(21),
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    ...tradework_theme.typography.styles.gillsans_light,
    fontSize: pxToRem(14),
  },
  popup_icon_border_saved_serach: {
    border: `solid ${pxToRem(1.7)} ${color.first_quarter_silver}`,
    borderRadius: pxToRem(6),
    height: pxToRem(25),
    width: pxToRem(26),
    textAlign: "center",
  },
  popup_icon_sort_saved_serach: {
    width: pxToRem(17),
    height: pxToRem(11),
    marginTop: pxToRem(7),
  },
  width_50: {
    width: "50%",
  },
  width_100: {
    width: "100%",
  },
  spacing_search_icon: {
    justifyContent: "flex-end",
    paddingRight: pxToRem(40),
  },
  width_289: {
    width: pxToRem(289),
  },
  posi_relative: {
    position: "relative",
  },
  search_icon_saved_job: {
    width: pxToRem(19),
    height: pxToRem(20),
    position: "absolute",
    right: pxToRem(15),
    top: pxToRem(5),
  },
  savedjob_inp: {
    width: pxToRem(306),
    height: pxToRem(30),
    border: `solid ${pxToRem(1)} ${color.secondary_palette.grays.shadow_gray}`,
    ...tradework_theme.typography.styles.gillsans_light,
    fontSize: pxToRem(14),
    borderRadius: pxToRem(5),
  },
  saved_job_spacing: {
    marginTop: pxToRem(-28),
    paddingLeft: pxToRem(6),
  },
  margin_auto: {
    margin: "0 auto",
  },
  margin_right_7: {
    marginRight: `${pxToRem(7)} !important`,
  },
  padding_top_10: {
    paddingTop: pxToRem(10),
  },
  width_dream_job: {
    width: pxToRem(1012),
    margin: "0 auto",
  },
  padding_bottom_30: {
    paddingBottom: pxToRem(30),
  },
  cloud_icon: {
    width: pxToRem(59),
    height: pxToRem(40),
    paddingRight: pxToRem(7),
  },
  settings_text: {
    ...tradework_theme.typography.styles.gillsans_r,
    color: color.forthcoming_almond,
    textDecoration: "underline",
  },
  dream_text: {
    width: pxToRem(794),
  },
  padding_left_78: {
    paddingLeft: pxToRem(78),
  },
  margin_top_7: {
    marginTop: pxToRem(7),
  },
});

export default styles;
