/*
 * @file responsible for main landing page jpack
 * @Author: Sow<PERSON><PERSON>; <EMAIL>
 * @Date: 2020-11-23 10:39:34
 * @Last Modified by: <PERSON><PERSON> <<EMAIL>>
 * @Last Modified time: 2021-03-12 19:13:29
 */
/*eslint-disable*/
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { concat, filter, debounce, get } from "lodash";

import JobPostingStyles from "./job_posting_styles";
// import FilterPanel from "../filter_pannel/filter_pannel";
import Tabs from "../tabs/tabs";
// import SearchIcon from "../../assets/images/search.png";
import SaveSearchIcon from "../../assets/images/Save.png";
import EyeIcon from "../../assets/images/Eye.png";
import SortByIcon from "../../assets/images/Sort By.png";
import BellOne from "../../assets/images/BellOne.png";
import JobListingImg from "../../assets/images/JOB LISTINGS.svg";
import AppliedJobImg from "../../assets/images/APPLIED JOBS.svg";
import savedJobListingImg from "../../assets/images/Saved job listing.svg";
import savedSearchesJobImg from "../../assets/images/Saved searches & Job.svg";
import recentlyVisitedImg from "../../assets/images/recently visited.svg";
import dreamJobSettingImg from "../../assets/images/DREAM JOB SETTINGS E.svg";
import MagicEye from "../popups/magic_eye";
import SaveMySearch from "../popups/saveMySearch";
import CreateAlert from "../popups/createAlert";
import SortBy from "../popups/sortBy";
import strings from "../../utilities/strings";
import AdvancedSearch from "../popups/advancedSearch";
import CloudEmpty from "../../assets/images/Cloud.svg";

import { JobSearchActions, LookupActions } from "../../redux/actions";
import { Row, Text } from "../common/ui_kit";
import { color } from "../../utilities/themes";
// import FilterPannelNew from "../filter_pannel/filter_pannel_new";
import Gamification from "../gamification/gamification";
import FilterPannelWrapper from "../filter_pannel/FilterPannelWrapper";
/**
 *  Default component for landing page for jpack
 */
function JobPostLanding(props) {
  let searchInput = "";
  const { classes } = props;
  const dispatch = useDispatch();
  const jobPosts = useSelector((state) => state.JobSearchReducer.jobPosts);
  const pageLimit = useSelector((state) => state.JobSearchReducer.limit);
  const allJobsLength = useSelector(
    (state) => state.JobSearchReducer.tabOnePageLength
  );

  const advSearch = useSelector(
    (state) => state.JobSearchReducer.advanceSearch
  );
  const refineSearch = useSelector(
    (state) => state.JobSearchReducer.refineSearch
  );
  const searchName = useSelector((state) => state.JobSearchReducer.searchName);
  const currentPage = useSelector(
    (state) => state.JobSearchReducer.currentPage
  );
  const showAdvSearch = useSelector(
    (state) => state.JobSearchReducer.showAdvSearch
  );

  const filterSearchResults = useSelector(
    (state) => state.JobSearchReducer.refineSearch
  );

  const titleOrCompanyName = useSelector(
    (state) => state.JobSearchReducer.titlCompanyName
  );

  const [values, setValues] = useState({
    showMagicEye: false,
    showSaveSearch: false,
    showAlertModal: false,
    showSortBy: false,
    showAdvSearch: false,
    showGamification: false,
    currentTab: 0,
    search: "",
  });

  const [dreamJobEdit, setDreamJobEdit] = useState(true);
  const [errors, setErrors] = useState({});
  const [filters, setFilters] = useState({
    trade: [],
    location: "",
    JobDuration: [],
    company: [],
    postingDate: 0,
    salary: {
      duration: "yearly",
      minSalary: "0",
      maxSalary: "100",
      plusCommision: false,
      includeNonPaid: false,
      dental: false,
      healthCare: false,
      dentalPaidVacations: false,
      visionCare: false,
      petsAllowed: false,
      childcare: false,
    },
    venue: [],
    companySize: [],
    level: [],
    clientel: [],
    companyVibe: [],
    jobPerks: [],
    seekingCandidate: [],
    applicationProcess: [],
    commute: [],
    limit: 10,
    pageNo: 1,
  });

  const {
    showMagicEye,
    showSaveSearch,
    showAlertModal,
    showSortBy,
    // showAdvSearch,
    currentTab,
    showGamification,
  } = values;
  const { salary } = filters;

  useEffect(() => {
    dispatch(LookupActions.getAllTrades());
    dispatch(LookupActions.getUserInfo());
    dispatch(JobSearchActions.getGamificationData());
    // setTimeout(() => {
    //   if (jobPosts.length === 0) {
    //     dispatch(
    //       JobSearchActions.getJobPosts({
    //         titleOrCompanyName: "",
    //         cityStateOrZip: "",
    //         limit: 5,
    //         pageNo: 1,
    //       })
    //     );
    //   }
    // }, 3000);
    // eslint-disable-next-line
  }, []);

  // handles triggering modal
  const triggerModal = (type) => () => {
    setValues({ ...values, [type]: !values[type] });
  };

  const toggleClose = (type, type2) => () => {
    if (type2) {
      setValues({
        ...values,
        [type]: !values[type],
        [type2]: true,
        newJobAlert: true,
      });
      return;
    }
    setValues({
      ...values,
      [type]: !values[type],
      newJobAlert: false,
    });
    dispatch(
      JobSearchActions.getJobPosts({
        titleOrCompanyName: titleOrCompanyName || "",
        cityStateOrZip: "",
        limit: 10,
        pageNo: 1,
      })
    );
  };

  // sets filter pannel changes
  const setFilterData = (keyName, val) => (e) => {
    if (typeof filters[keyName] === "object") {
      if (Array.isArray(filters[keyName])) {
        if (filters[keyName].includes(val)) {
          filters[keyName] = filter(filters[keyName], (each) => each !== val);
        } else {
          filters[keyName] = concat(filters[keyName], val);
        }
      }
    }
    setFilters({ ...filters });
  };

  // triggers on deselecting filter
  const deselectFilter = (data) => () => {
    filters[data.type] = filter(
      filters[data.type],
      (each) => each !== data.name
    );
    setValues({ ...filters });
  };

  const handleFilterInputChange = (e) => {
    const { name, value, checked } = e.target;
    const salaryKeys = [
      "maxSalary",
      "minSalary",
      "plusCommision",
      "includeNonPaid",
      "dental",
      "healthCare",
      "dentalPaidVacations",
      "visionCare",
      "petsAllowed",
      "childcare",
    ];
    if (name === "minSalary" || name === "maxSalary") {
      if (value > 100) {
        setErrors({ ...errors, [name]: true });
        return;
      }
      if (
        name === "minSalary" &&
        Number(value) > Number(filters.salary.maxSalary)
      ) {
        setErrors({ ...errors, [name]: true });
        return;
      }
      if (
        name === "maxSalary" &&
        Number(value) < Number(filters.salary.minSalary)
      ) {
        setErrors({ ...errors, [name]: true });
        return;
      }
      salary[name] = checked || value;
      setErrors({ ...errors, [name]: false });
      setFilters({ ...filters });
      return;
    }
    if (salaryKeys.includes(name)) {
      salary[name] = checked || value;
      setFilters({ ...filters });
    }
    setFilters({ ...filters, [name]: value });
  };

  const handleDropdownChange = (name, value) => () => {
    if (name === "duration") {
      salary[name] = value;
    }
    setValues({ ...values });
  };

  const onSalarySliderChange = (e, value) => {
    salary.minSalary = value[0];
    salary.maxSalary = value[1];
    setFilters({ ...filters });
  };

  const onPostingSliderChange = (e, value) => {
    setFilters({ ...filters, postingDate: value });
  };

  const handleDisableSaveSearch = () => {
    if (advSearch || refineSearch) {
      return false;
    }
    return true;
  };

  const getSearchName = () => {
    // if (!jobPosts) {
    //   return null;
    // }
    // return `${get(jobPosts[0], "basicDetails.tradeName", "")} `;
    // - ${get(
    //   jobPosts[0],
    //   "basicDetails.location.name",
    //   ""
    // )
    // }`;
  };

  const refreshPage = () => () => {
    const dataToSubmit = {
      limit: 10,
      pageNo: 1,
      titleOrCompanyName: "",
    };
    dispatch(JobSearchActions.refineSearch(dataToSubmit));
    window.location.reload();
  };

  // returns header jsx
  const returnHeaderJSX = () => {
    const headerTitles = {
      0: JobListingImg,
      1: dreamJobSettingImg,
      2: AppliedJobImg,
      3: savedJobListingImg,
      4: savedSearchesJobImg,
      5: recentlyVisitedImg,
    };
    const disableSaveSearch = handleDisableSaveSearch();
    return (
      <>
        <Row style={{ justifyContent: "space-between" }}>
          <Row className={classes.width_100}>
            <div
              className={`${
                (currentTab === 4 || currentTab === 3) && classes.width_50
              } ${currentTab === 1 && classes.width_dream_job}`}
            >
              <Text
                family="TrajanPro3"
                size={40}
                color={color.form_colors.blueberry_purple}
                // onClick={() => {
                //   setValues({ ...values, showGamification: true });
                // }}
              >
                {currentTab === 1 && (
                  <img
                    src={CloudEmpty}
                    alt="cloud"
                    className={classes.cloud_icon}
                  />
                )}
                {/* {headerTitles[currentTab]}{" "} */}
                <img
                  src={headerTitles[currentTab]}
                  alt="cloud"
                  // className={classes.cloud_icon}
                />
                {/* {currentTab === 1 && dreamJobEdit && "EDIT"} */}
              </Text>
              {currentTab === 0 && (
                <Text family="gillsans_sb" size={16} color="#828282">
                  Ranked by your Dream Job settings
                </Text>
              )}
              {currentTab === 1 && (
                <Text
                  family="gillsans_r"
                  size={20}
                  color={color.greyish_brown}
                  className={`${classes.padding_top_10} ${classes.padding_bottom_30} ${classes.dream_text}`}
                >
                  These settings will determine which jobs you’ll see listed
                  first. We’ll also be sure to send you an email alert whenever
                  a position opens up that closely matches your dream job.
                  {!dreamJobEdit && (
                    <span
                      className={`${classes.settings_text} ${classes.padding_left_78}`}
                    >
                      settings
                    </span>
                  )}
                </Text>
              )}
            </div>
            {currentTab === 0 && (
              <div>
                <Text
                  family="gillsans_sb"
                  size={25}
                  color={color.form_colors.blueberry_purple}
                  className={`${classes.position_title} ${classes.margin_top_7}`}
                >
                  <span
                    onClick={refreshPage()}
                    style={{ cursor: "pointer" }}
                  >{`${"ALL JOBS"}`}</span>{" "}
                  {(titleOrCompanyName || searchName) && "> "}
                  {titleOrCompanyName || searchName}
                </Text>
                <Text
                  family="avenir_black_r"
                  size={18}
                  color={color.unrelated_mousy}
                  className={classes.position_title}
                >
                  {allJobsLength || 0} Jobs Found
                </Text>
                {allJobsLength === 0 && (
                  <Text
                    family="avenir_sb"
                    size={18}
                    color="#5e94e0"
                    className={classes.position_title}
                  >
                    Please Widen your Search for Better Results
                  </Text>
                )}
              </div>
            )}
            {/* {currentTab === 4 && (
              <>
                <Row
                  className={`${classes.width_50} ${classes.spacing_search_icon}`}
                >
                  <input
                    className={classes.savedsearch_inp}
                    placeholder="Search List"
                  />
                  <Row>
                    <Text
                      size={10}
                      color={color.unrelated_mousy}
                      family="avenir_black_r"
                      className={`${classes.oblique} ${classes.sortByText}`}
                    >
                      {strings.jobLanding.title.sortby}
                    </Text>
                    <div
                      className={`${classes.popup_icon_border_saved_serach}`}
                    >
                      <img
                        src={SortByIcon}
                        alt="magic eye"
                        className={`${classes.popup_icon_sort_saved_serach}`}
                        onClick={triggerModal("showSortBy")}
                      />
                    </div>
                  </Row>
                </Row>
              </>
            )} */}
          </Row>
          {currentTab === 0 &&
            localStorage.getItem("tradeworks_user_token") !== "null" && (
              <Row className={classes.margin_top_20}>
                <div className={classes.position_rel}>
                  <Text
                    size={10}
                    color={color.unrelated_mousy}
                    family="avenir_black_r"
                    className={`${classes.oblique} ${classes.iconText}`}
                  >
                    {strings.jobLanding.title.magic_eye}
                  </Text>
                  <div className={classes.popup_icon_border}>
                    <div className={`${classes.hover_content} hover_display`}>
                      <div className="triangle"></div>
                      <Text
                        size={12}
                        color={color.primary_palette.black}
                        family="gillsans_r"
                        className={classes.txtLeft}
                      >
                        {strings.jobLanding.title.tooltip}
                      </Text>
                    </div>
                    <img
                      src={EyeIcon}
                      alt="magic eye"
                      className={`${classes.popup_icon_eye}`}
                      onClick={triggerModal("showMagicEye")}
                    />
                  </div>
                </div>
                <div>
                  <Text
                    size={10}
                    color={color.unrelated_mousy}
                    family="avenir_black_r"
                    className={`${classes.oblique} ${classes.iconText}`}
                  >
                    {strings.jobLanding.title.save_search}
                  </Text>
                  <div
                    className={` ${
                      disableSaveSearch
                        ? classes.popup_icon_border_disabled
                        : classes.popup_icon_border
                    }`}
                  >
                    <img
                      src={SaveSearchIcon}
                      alt="magic eye"
                      className={`${disableSaveSearch && classes.disableIcon} ${
                        classes.popup_icon_save
                      }`}
                      onClick={triggerModal("showSaveSearch")}
                    />
                  </div>
                </div>
                <div>
                  <Text
                    size={10}
                    color={color.unrelated_mousy}
                    family="avenir_black_r"
                    className={`${classes.oblique} ${classes.iconText}`}
                  >
                    {strings.jobLanding.title.job_alert}
                  </Text>
                  <div
                    className={` ${
                      disableSaveSearch
                        ? classes.popup_icon_border_disabled
                        : classes.popup_icon_border
                    }`}
                  >
                    <img
                      src={BellOne}
                      alt="magic eye"
                      className={`${disableSaveSearch && classes.disableIcon} ${
                        classes.redBell_icon
                      }`}
                      onClick={triggerModal("showAlertModal")}
                    />
                  </div>
                </div>
                <div>
                  <Text
                    size={10}
                    color={color.unrelated_mousy}
                    family="avenir_black_r"
                    className={`${classes.oblique} ${classes.sortByText}`}
                  >
                    {strings.jobLanding.title.sortby}
                  </Text>
                  <div className={`${classes.popup_icon_border}`}>
                    <img
                      src={SortByIcon}
                      alt="magic eye"
                      className={`${classes.popup_icon_sort}`}
                      onClick={triggerModal("showSortBy")}
                    />
                  </div>
                </div>
              </Row>
            )}
        </Row>
        {/* {currentTab === 3 && (
          <>
            <Row className={`${classes.spacing_search_icon}`}>
              <div className={classes.posi_relative}>
                <img
                  src={SearchIcon}
                  className={classes.search_icon_saved_job}
                  alt=""
                  onClick={toggleClose("showAdvSearch")}
                />{" "}
                <input
                  className={classes.savedjob_inp}
                  placeholder="Search List"
                />
              </div>
              <Row className={classes.saved_job_spacing}>
                <div>
                  <Text
                    size={10}
                    color={color.unrelated_mousy}
                    family="avenir_black_r"
                    className={`${classes.oblique} ${classes.sortByText}`}
                  >
                    {strings.jobLanding.title.magic_eye}
                  </Text>
                  <div
                    className={` ${
                      disableSaveSearch
                        ? `${classes.popup_icon_border_disabled} ${classes.margin_right_7}`
                        : classes.popup_icon_border
                    }`}
                  >
                    <img
                      src={EyeIcon}
                      alt="magic eye"
                      className={`${classes.popup_icon_eye}`}
                      onClick={triggerModal("showMagicEye")}
                    />
                  </div>
                </div>
                <div>
                  <Text
                    size={10}
                    color={color.unrelated_mousy}
                    family="avenir_black_r"
                    className={`${classes.oblique} ${classes.sortByText}`}
                  >
                    {strings.jobLanding.title.sortby}
                  </Text>
                  <div
                    className={` ${
                      disableSaveSearch
                        ? classes.popup_icon_border_disabled
                        : classes.popup_icon_border
                    }`}
                  >
                    <img
                      src={SortByIcon}
                      alt="magic eye"
                      className={`${disableSaveSearch && classes.disableIcon} ${
                        classes.popup_icon_save
                      }`}
                      onClick={triggerModal("showSortBy")}
                    />
                  </div>
                </div>
              </Row>
            </Row>
          </>
        )} */}
      </>
    );
  };

  // handles pagination change
  const handlePaginationChange = (e, pageCount, limit) => {
    dispatch(
      JobSearchActions.refineSearch({
        ...filterSearchResults,
        pageNo: pageCount || currentPage,
        limit: limit || pageLimit,
      })
    );
  };

  const handleSearch = (e) => {
    const { value } = e.target;
    searchInput = value;
    makeSearch();
  };

  // fetch data on search
  const getSearchResults = () => {
    if (searchInput.length >= 2 || searchInput.length === 0) {
      const dataToSubmit = { searchValue: searchInput, limit: 10, pageNo: 1 };
      dispatch(JobSearchActions.getJobPostsOnSearch(dataToSubmit));
    }
  };

  const setCurrentTab = (tab) => {
    setValues({ ...values, currentTab: tab });
  };

  // triggers methods on 800ms after user finishing typing in search input
  const makeSearch = debounce(getSearchResults, 800);

  return (
    <div className={`${classes.landing_wrapper}`}>
      {/* HEADING SECTION */}
      {returnHeaderJSX()}

      {/* {currentTab === 0 && (
        <Row className={classes.search_inp_row}>
          <img
            src={SearchIcon}
            className={classes.search_icon}
            alt=""
            onClick={toggleClose("showAdvSearch")}
          />{" "}
          <input
            className={classes.search_inp}
            placeholder={`Search Within ${jobPosts?.length} Job Results`}
            onChange={handleSearch}
          />
        </Row>
      )} */}
      {/* BODY SECTION (FILTER PANNEL, TABS)  */}
      <div>
        <Row className={classes.body_wrapper}>
          {values.currentTab === 0 && (
            // <FilterPanel
            //   setFilterData={setFilterData}
            //   filters={filters}
            //   onSalarySliderChange={onSalarySliderChange}
            //   onPostingSliderChange={onPostingSliderChange}
            //   deselectFilter={deselectFilter}
            //   handleInputChange={handleFilterInputChange}
            //   handleDropdownChange={handleDropdownChange}
            //   errors={errors}
            // />
            <FilterPannelWrapper />
          )}
          <Tabs
            handlePaginationChange={handlePaginationChange}
            setCurrentTab={setCurrentTab}
            triggerModal={triggerModal}
            setDreamJobEdit={setDreamJobEdit}
          />
        </Row>
      </div>
      {showAdvSearch && (
        <AdvancedSearch
          filters={filters}
          onClose={toggleClose}
          deselectFilter={deselectFilter}
          handleFilterInputChange={handleFilterInputChange}
          open={showAdvSearch}
        />
      )}
      {showMagicEye && (
        <MagicEye open={showMagicEye} onClose={toggleClose("showMagicEye")} />
      )}
      {showSaveSearch && (
        <SaveMySearch
          open={showSaveSearch}
          onClose={toggleClose("showSaveSearch")}
          filters={filters}
          deselectFilter={deselectFilter}
          type={values.newJobAlert ? "jobAlert" : "saveSearch"}
        />
      )}
      {showAlertModal && (
        <CreateAlert
          filters={filters}
          deselectFilter={deselectFilter}
          open={showAlertModal}
          onClose={toggleClose("showAlertModal")}
          toggleClose={toggleClose}
        />
      )}
      {showSortBy && (
        <SortBy open={showSortBy} onClose={toggleClose("showSortBy")} />
      )}
      {showGamification && (
        <Gamification onClose={toggleClose("showGamification")} />
      )}
    </div>
  );
}

export default JobPostingStyles(JobPostLanding);
