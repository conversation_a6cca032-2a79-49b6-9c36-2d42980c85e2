import React from "react";
import { withStyles, Switch } from "@material-ui/core";
import { color, pxToRem } from "../../../utilities/themes";

export default withStyles(theme => ({
  root: {
    width: 55,
    height: 20,
    padding: 0,
    display: "flex",
    overflow: 'initial'
  },
  switchBase: {
    padding: 2,
    color: theme.palette.grey[500],
    "&$checked": {
      transform: "translateX(35px)",
      color: color.secondary_palette.grays.background_gray,
      // "& + $track": {
      //   opacity: 1,
      //   backgroundColor: theme.palette.primary.main,
      //   borderColor: theme.palette.primary.main
      // }
    }
  },
  thumb: {
    width: 16,
    height: 16,
    color: color.form_colors.blueberry_purple,
    boxShadow: "none"
  },
  track: {
    border: `1px solid ${color.wizard_box_colors.shadow_gray}`,
    borderRadius: pxToRem(10),
    opacity: 1,
    backgroundColor: color.secondary_palette.grays.background_gray
  },
  checked: {}
}))(Switch);
