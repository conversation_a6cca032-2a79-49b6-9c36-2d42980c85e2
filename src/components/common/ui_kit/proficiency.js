import React from "react";
import PropTypes from "prop-types";
import _ from "lodash";
import { makeStyles } from "@material-ui/core";
import { pxToRem, color } from "../../../utilities/themes";
import { PROFICIENCY } from "../../../constants";

const useStyles = makeStyles({
  root: props => ({
    display: "inline-block",
    width: pxToRem(props.width),
    height: pxToRem(props.height),
    border:
      props.type == "bullet"
        ? "none"
        : `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    borderRadius: props.type == "circle" && "100%",
    alignSelf: props.type == "bullet" && "center",
    backgroundColor:
      props.type == "bullet"
        ? color.primary_palette.black
        : _.get(PROFICIENCY, `[${props.level}].color`, "")
  })
});

function Proficiency({ style, className, ...props }) {
  const classes = useStyles(props);
  return <span className={`${classes.root} ${className}`} style={style} />;
}

Proficiency.propTypes = {
  type: PropTypes.oneOf(["circle", "square", "bullet"]),
  level: PropTypes.number.isRequired,
  width: PropTypes.number,
  height: PropTypes.number,
  style: PropTypes.object,
  className: PropTypes.string
};

Proficiency.defaultProps = {
  type: "circle",
  width: 10,
  height: 10
};

export default Proficiency;
