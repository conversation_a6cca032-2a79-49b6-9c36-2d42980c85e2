import React from "react";
import _ from "lodash";
import { makeStyles } from "@material-ui/core";
import { pxToRem, color } from "../../../utilities/themes";
import Row from "./row";
import Proficiency from "./proficiency";
import Text from "./text";
import Single_key from "../../data_display/icons/single_key";
import CustomButton from "../../../components/navigations/custom_buttons";
import Close_Icon from "../../data_display/icons/Close";

const useStyles = makeStyles({
  proficieny_width: {
    width: pxToRem(100),
    height: pxToRem(150),
    background: color.alpine_goat,
    display: "block",
    border: `solid ${pxToRem(1)} ${color.primary_palette.franklin_purple}`,
    paddingLeft: pxToRem(4),
    paddingRight: pxToRem(4)
  },
  text_right: {
    textAlign: "right",
    paddingTop: pxToRem(2),
    paddingBottom: pxToRem(2)
  },
  single_key: {
    "& path": {
      fill: color.primary_palette.franklin_purple
    }
  },
  joinUs_cross: {
    fontSize: pxToRem(10)
  },
  close_btn: {
    padding: 0,
    minWidth: pxToRem(30),
    float: "right"
  }
});

function Proficiency_wizard({ style, className, ...props }) {
  const classes = useStyles(props);
  return (
    <>
      <div className={`${classes.proficieny_width} ${className}`} style={style}>
        <div>
          <Single_key className={classes.single_key} />
          <CustomButton className={classes.close_btn}>
            <Close_Icon className={classes.joinUs_cross} onClick={props.onClose}/>
          </CustomButton>
        </div>
        <Text className={classes.text_right}>
          Basic <Proficiency level={1} style={{ display: "inline-block" }} />
        </Text>
        <Text className={classes.text_right}>
          Familiar <Proficiency level={2} style={{ display: "inline-block" }} />
        </Text>
        <Text className={classes.text_right}>
          Proficient{" "}
          <Proficiency level={3} style={{ display: "inline-block" }} />
        </Text>
        <Text className={classes.text_right}>
          Advanced <Proficiency level={4} style={{ display: "inline-block" }} />
        </Text>
        <Text className={classes.text_right}>
          Expert <Proficiency level={5} style={{ display: "inline-block" }} />
        </Text>
      </div>
    </>
  );
}

export default Proficiency_wizard;
