import React from "react";
import { styled } from "@material-ui/core/styles";

import { color } from "../../utilities/themes";
/**this is a loader component of whole application */
const Loading = ({ loading }) => {
  return !loading ? null : (
    <LoadingWheel>
      <img src="assets/images/loading.gif" style={{width: "150px", height: "150px"}} />
    </LoadingWheel>
  );
};

const LoadingWheel = styled("div")({
  backgroundColor: color.primary_palette.white,
  position: "fixed",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  width: "100%",
  height: "100%",
  opacity: 0.8,
  zIndex: 2000,
});

export default Loading;
