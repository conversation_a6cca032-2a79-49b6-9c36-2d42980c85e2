import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles(theme => ({
  root: {
    "& :hover": {}
  }
}));
const onHandleOnclick = props => {
  if (props.redirect && props.url) window.open(props.url);
};
function Single_key(props) {
  function styledIcon(props) {
    const classes = makeStyles(props);
    return (
      <span className={classes.root}>
        <SvgIcon
          {...props}
          viewBox="0 0 35 12"
          onClick={() => onHandleOnclick(props)}
        >
          <path
            d="M3.192 8.367c-.948.035-1.853-.078-2.542-.886C.214 6.97-.004 6.38 0 5.756c.006-.814.353-1.521 1.028-2.035.638-.486 1.37-.597 2.146-.509.184-.468.346-.904.526-1.332C4.293.47 6.28-.411 7.85.193c1.613.622 2.556 2.241 2.207 3.904-.032.15-.056.302-.09.493.676.213 1.327.085 1.971.098l.72-.876.843.26c.149-.06.377-.148.56-.22.552.027.673.473.957.868h19.644c.084.824.04 1.565.038 2.389l-1.374.11c-.219.71-.081 1.433-.106 2.143-.024.685-.005 1.371-.005 2.126-1.018.078-1.946.032-2.942.03V9.756l1.15-.155.078-.807-1.262-.135-.1-1.429-1.022-.058-.104 1.475-1.03.135-.046.814 1.05.117v1.673c-.653.19-1.962.207-2.931.013V7.334c-.829-.2-8.497-.267-11.128-.12l-.66.59-.792-.276c-.236.06-.536.226-.8.181-.24-.04-.443-.309-.664-.478h-2.01c.297 1.14.15 2.15-.54 3.062-.601.795-1.39 1.251-2.365 1.412-1.74.287-3.296-1.026-3.905-3.338m3.505-6.294c-.811.003-1.28.468-1.286 1.275-.006.853.482 1.366 1.307 1.374.82.007 1.351-.52 1.347-1.337-.005-.824-.516-1.314-1.368-1.312m1.355 6.211c-.002-.819-.525-1.313-1.378-1.3-.794.01-1.269.504-1.257 1.309.011.84.532 1.348 1.356 1.327a1.324 1.324 0 001.28-1.336M2.7 4.94c-.882.37-.882.37-.737 1.318l.639.357c.996-.537 1.021-1.036.098-1.675"
            fill={props.color || "#000"}
            fillRule="evenodd"
          />
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_single_key = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {}
    }
  })(styledIcon);
  return (
    <>
      <Custom_single_key {...props} />
    </>
  );
}

export default Single_key;
