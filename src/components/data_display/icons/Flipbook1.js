import React from 'react';

const SvgFlipbook1 = props => (
  <svg width={170} height={49} {...props}>
    <g fill="none" fillRule="evenodd">
      <path
        d="M98.892 28.739c-.924-.115-1.47 1.057-1.748 1.739-.278.682-.736 2.546-.014 3.604.722 1.057 1.86.459 2.359-.877.455-1.215.86-4.286-.597-4.466zm-8.506 0c-.924-.115-1.471 1.057-1.748 1.739-.278.682-.736 2.546-.014 3.604.721 1.057 1.859.459 2.359-.877.454-1.215.86-4.286-.597-4.466zm-7.98 1.155c-.346-1.058-1.845-.585-2.247-.265-.403.32-1.027 1.183-1.124 1.934-.097.752-.32 2.936-.32 2.936 1.277.404 2.401.265 2.942-.612.542-.877 1.097-2.936.75-3.993zm-3.357-2.227c1.693-2.7 2.4-4.16 2.844-5.76.432-1.555.458-2.394-.25-2.435-.707-.042-1.18 2.1-1.304 2.602-.125.5-.888 3.311-1.29 5.593zm-4.732 2.547c.314-1.277-.43-1.879-1.277-1.448-.847.432-1.374 2.324-1.513 4.87.472-.25 2.248-1.224 2.79-3.422zm-4.894 6.04c-.752 1.383-1.336 3.441-1.442 4.63-.083.944.982 1.468 1.129.525.146-.943.188-4.421.313-5.155zm-6.416-13.777c.194-.791.4-1.72.336-2.202a.17.17 0 00-.229-.134c-.508.208-1 1.107-1.162 1.53-.18.472-1.498 4.299-1.679 7.04 1.513-2.463 2.512-5.33 2.734-6.234zm42.756 7.281c1.633-3.751 2.341-6.684 2.717-7.585.376-.9.836-2.368.209-2.284-.627.084-1.463 1.467-1.839 2.913-.376 1.445-.898 4.84-1.087 6.956zm15.159.93l.005.022-1.377-1.413c-1.651 3.312-3.585 6.525-7.125 7.507a5.265 5.265 0 01-1.336.157c-1.011 0-2.246-.241-3.054-1.059-.48-.486-.96-1.324-.709-2.36.252-1.036 1.229-1.492 2.767-2.48 1.02-.656 1.334-3.292-.727-2.647-1.083.339-1.789 3.537-2.516 5.618-.937 2.681-1.65 2.62-2.34 2.472-.69-.146-.815-.838-.92-3.1-.039-.854.012-1.734.072-2.615l-.038.022c-.513-.946-1.818-1.684-2.484-2.129 0 0 .666 1.1.666 2.922 0 1.823-.999 3.743-2.428 4.522-1.43.78-3.83.362-4.399-1.67-.507-1.81-.552-2.568-.18-4.095-.615-.73-1.61-1.307-2.165-1.679 0 0 .666 1.1.666 2.922 0 1.823-1 3.743-2.428 4.522-1.43.78-3.83.362-4.4-1.67l-.096-.353c-.976.657-2.488 1.314-3.851 1.554-.142.15-.286.284-.43.39-.995.723-3.465.546-4.99-.483-2.277 1.448-3.833.788-5.309.499 0 0-.032 2.38-.481 4.133-.45 1.753-1.652 4.005-3.176 3.989-1.524-.016-2.021-1.673-2.005-3.297.016-1.625.946-3.378 2.181-5.678-.48.402-1.844.981-2.71 1.238-.867.258-2.054-.096-2.407-.884-.4.305-1.764.74-2.15.836-.384.097-1.17.37-1.828-.402-.657-.772-1.716-2.09-1.25-7.19.464-5.098 2.886-8.669 3.352-9.312.465-.643 2.759-2.718 3.833-1.207 1.075 1.513-.128 4.73-1.058 6.868-.93 2.14-2.952 5.47-3.32 6.225-.37.756-.386 1.801-.257 2.268.128.466.272.563.561.563.289 0 2.053-.66 2.182-1.383.186-1.054 1.01-4.182 1.139-4.633.128-.45.16-.514.449-.917.288-.402 1.411-.498 1.86-.434.45.065.29.708.16 1.27-.128.564-1.78 4.874-1.283 6.048.498 1.175 1.925-.048 3.786-1.72 1.075-4.874 1.925-5.485 4.01-5.678 2.086-.193 3.209 1.463 2.92 3.57-.29 2.108-1.685 3.137-3.593 4.086 0 0-.049.193.032.209.673.048 2.727-.274 3.946-1.062.096-1.431 1.122-10.326 2.422-13.301 1.3-2.976 2.486-2.815 3.56-2.557 1.076.257 1.54 1.753 1.397 3.65-.145 1.898-2.252 5.084-4.353 7.641 0 0 2.234-2.769 3.759-1.593 1.647 1.27 1.25 3.973.93 4.954a9.698 9.698 0 01-.518 1.226c.773-.406 1.553-.931 2.164-1.38-.02-.719.114-1.404.395-2.442.264-.974 1.318-2.212 3.122-2.796 1.804-.585 3.913.473 4.621 1.03.444.348.795.681 1.105 1.008.491-.79 1.423-1.599 2.78-2.038 1.805-.585 3.914.473 4.622 1.03.456.358.813.7 1.13 1.035.353-3.078 1.036-5.96 1.604-7.555.836-2.347 2.174-3.982 4.242-3.71 2.173.273 1.003 4.464.272 6.643-.732 2.18-2.547 4.868-3.434 6.319 0 0 1.748-2.387 3.248-3.38.827-.548 2.205-1.27 2.965.225.474.933.27 1.836-.291 2.51-.776.933-2.107 1.003-2.937 2.5-.587 1.057.287 2.292.565 2.474 1 .652 2.37-.042 3.75-1.198 1.57-1.315 3.253-3.882 4.151-5.358l-2.016-.208 4.716-3.274.27 5.674-.008-.011zm-55.958-6.722c-.14.78.346 1.67 1.554 1.614 1.207-.055 1.956-.487 2.04-1.683.083-1.197-.888-1.462-1.471-1.49-1.541-.073-1.985.78-2.123 1.56zm-8.009 4.085h-3.734l-1.263 5.487c-.654 2.84-1.654 3.97-2.717 3.97-.804 0-1.802-1.26-1.66-1.872.096-.42.262-.162.583-.162.58 0 1-.71 1.38-2.356l1.167-5.067h-2.638c-.676 0-.944-.936-.788-1.614.082-.355.31-.646.632-.646h3.314l2.415-10.49c-4.573.291-8.067 1.905-9.234 6.972-.513 2.227.18 3.55.824 4.39.377-.097.453.129.38.452-.194.839-1.244 1.904-1.985 1.904-.16 0-.314-.032-.445-.161-.778-.678-2.104-3.164-1.317-6.585 1.427-6.197 6.184-9.521 13.91-9.521 1.77 0 10.473.064 11.148.064.644 0 .865-.258.961-.258.225 0 .317.58.206 1.065-.179.775-.877 1.711-1.875 1.711-.58 0-8.059-.065-10.047-.097l-2.43 10.554h4.023c1.169-.742-.07 2.26-.81 2.26z"
        fill="#010202"
      />
      <path
        d="M170 37.049s-1.022.915-1.207 1.074c-6.567 5.134-14.179 8.239-22.473 10.017l-15.157-25.532.108-1.09s14.9-.52 19.312-12.913l18.363 21.844s-1.055 1.196-1.96 2.25l1.782 1.912-.595.602L170 37.05zm-.99-.127l-1.144-1.197c-9.716 9.058-21.46 11.721-21.46 11.721v.192c6.243-1.41 11.501-3.605 15.368-5.75 4.6-2.552 7.235-4.966 7.235-4.966zm-2.403-3.707c-9.858 10.562-20.201 13.816-20.201 13.816v.203c5.694-1.612 13.996-6.106 18.313-9.667 2.144-1.77 3.228-2.949 3.228-2.949l-1.34-1.403z"
        fill="#000"
      />
      <path
        d="M146.2 46.512s5.404-1.464 11.694-6.363c3.12-2.43 6.65-5.47 9.78-9.616l-16.831-20.3s-3.472 10.371-18.75 12.163l14.108 24.116z"
        fill="#FFF"
      />
      <path
        d="M157.907 35.48c-1.318 1.098-1.743 1.426-2.856 2.194-2.871-4.106-5.64-8.262-8.593-12.316-1.432.803-2.92 1.443-5.044 2.42-.132.062-.274.112-.454.184-.063-.127-.131-.229-.165-.341-.348-1.133.27-2.167 1.363-2.826.816-.491 1.19-1.107 1.403-1.87.105-.373.254-.719.546-.98.024-.02.033-.03.057-.052.084-.067.37-.217.37-.217s2.402-1.663 2.402-1.668c.529-.482 1.082-.557 1.643-.496.862.095 1.636-.002 2.339-.495.941-.658 1.884-.514 2.707.329.082.085.147.188.236.297-.093.104-.162.19-.238.263-1.259 1.206-2.52 2.33-4.092 3.467 2.739 4.07 5.537 8.103 8.376 12.106z"
        fill="#410166"
      />
      <path fill="#FFF" d="M155.116 36.977l-10.232-14.653 2.325-1.626 10 14.65z" />
      <text fontFamily="Pacifico" fontSize={25.581} letterSpacing={0.14} fill="#000">
        <tspan x={0} y={36}>
          {'M'}
        </tspan>
        <tspan x={27.057} y={36} fontSize={20.93}>
          {'y'}
        </tspan>
      </text>
    </g>
  </svg>
);

export default SvgFlipbook1;
