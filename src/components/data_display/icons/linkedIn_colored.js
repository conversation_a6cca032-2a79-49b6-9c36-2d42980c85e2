import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles((theme) => ({
  root: {
    "& :hover": {},
  },
}));
const onHandleOnclick = (props) => {
  if (props.redirect && props.url) {
    // if (props.url.includes("http")) {
    //   window.open(props.url);
    // } else {
    //   window.open(`//${props.url}`);
    // }
    window.open(`https://linkedin.com/in${props.url}`);
  }
};
function LinkedIn_Color_Icon(props) {
  function styledIcon(props) {
    // const classes = useStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={""}>
        <SvgIcon
          {...props}
          viewBox="0 0 30 30"
          onClick={() => onHandleOnclick(props)}
        >
          <g fill="none" fillRule="evenodd">
            <path
              d="M30 22.5c0 4.143-3.357 7.5-7.5 7.5h-15A7.499 7.499 0 010 22.5v-15C0 3.357 3.357 0 7.5 0h15C26.643 0 30 3.357 30 7.5v15z"
              fill="#0173B2"
            />
            <path
              fill="#FFF"
              mask="url(#prefix__b)"
              d="M5.625 24.375h4.376v-12.5H5.625zM7.812 10a2.189 2.189 0 100-4.374 2.189 2.189 0 000 4.374M24.375 17.505c0-3.372-.727-5.63-4.659-5.63-1.89 0-3.159.702-3.678 1.686h-.053v-1.686H12.5v12.5h3.644v-6.197c0-1.633.309-3.214 2.33-3.214 1.992 0 2.152 1.867 2.152 3.32v6.09h3.75v-6.87z"
            />
          </g>
          {/* <svg width={30} height={30} viewBox="0 0 30 30" {...props}>
      <defs>
        <path id="prefix__a" d="M0 30h30V0H0z" />
      </defs>
      <g fill="none" fillRule="evenodd">
        <path
          d="M30 22.5c0 4.143-3.357 7.5-7.5 7.5h-15A7.499 7.499 0 010 22.5v-15C0 3.357 3.357 0 7.5 0h15C26.643 0 30 3.357 30 7.5v15z"
          fill="#0173B2"
        />
        <path
          fill="#FFF"
          mask="url(#prefix__b)"
          d="M5.625 24.375h4.376v-12.5H5.625zM7.812 10a2.189 2.189 0 100-4.374 2.189 2.189 0 000 4.374M24.375 17.505c0-3.372-.727-5.63-4.659-5.63-1.89 0-3.159.702-3.678 1.686h-.053v-1.686H12.5v12.5h3.644v-6.197c0-1.633.309-3.214 2.33-3.214 1.992 0 2.152 1.867 2.152 3.32v6.09h3.75v-6.87z"
        />
      </g>
    </svg> */}
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_LinkedIn_Outline_Icon = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {},
    },
  })(styledIcon);
  return (
    <>
      <Custom_LinkedIn_Outline_Icon {...props} />
    </>
  );
}

export default LinkedIn_Color_Icon;
