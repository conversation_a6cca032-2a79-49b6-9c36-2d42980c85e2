import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles(theme => ({
  root: {
    "& :hover": {}
  }
}));
const onHandleOnclick = props => {
  if (props.redirect && props.url) window.open(props.url);
};
function Tag_Purple(props) {
  function styledIcon(props) {
    const classes = makeStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={classes.root}>
        <SvgIcon
          {...props}
          viewBox="0 0 11 11"
          onClick={() => onHandleOnclick(props)}
        >
          <g fill="none" fillRule="evenodd">
        <path
          d="M8.625 1.75c.348 0 .625.277.625.625A.621.621 0 018.625 3a.624.624 0 110-1.25zM6.102 0a.25.25 0 00-.153.07L.199 5.695a.695.695 0 00-.2.496c0 .195.08.365.2.485l4.125 4.125a.61.61 0 00.426.2c.195 0 .329-.107.422-.196l5.75-5.5a.255.255 0 00.078-.18V.25a.25.25 0 00-.25-.25H6.102z"
          fill="#000"
        />
        <path
          d="M8.625 1.026a1.348 1.348 0 000 2.694 1.347 1.347 0 000-2.694zM.728 6.187L6.316.72h3.964v4.202L4.75 10.21.729 6.185z"
          fill="#801FB8"
        />
      </g>
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_Tag_Purple = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {}
    }
  })(styledIcon);
  return (
    <>
      <Custom_Tag_Purple {...props} />
    </>
  );
}

export default Tag_Purple;
