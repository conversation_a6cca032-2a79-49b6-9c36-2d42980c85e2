import React from 'react';

const SvgBenjaminMaster = props => (
  <svg id="benjamin_master_svg__Layer_1" x={0} y={0} viewBox="0 0 314.2 216.5" xmlSpace="preserve" {...props}>
    <style>{'.benjamin_master_svg__st8{fill:#231f20}'}</style>
    <radialGradient
      id="benjamin_master_svg__SVGID_1_"
      cx={7463.718}
      cy={48.384}
      r={30.158}
      gradientTransform="matrix(-1 0 0 1 7699.112 0)"
      gradientUnits="userSpaceOnUse"
    >
      <stop offset={0} stopColor="#fdf9ce" />
      <stop offset={0.099} stopColor="#f9efbd" />
      <stop offset={0.295} stopColor="#f1dd97" />
      <stop offset={0.371} stopColor="#eed688" />
      <stop offset={0.685} stopColor="#eabb1f" />
      <stop offset={1} stopColor="#c2922e" />
    </radialGradient>
    <path
      d="M197.3 67.6c7.9-12.5 13.5-15.3 16.6-17.3 3-2.1 6.7-6.6 3.5-10.5 0 0 3.8-.1 5.8 1.4 2.1 1.4 5.5 2.7 7.8 1.2 2.3-1.5 4.4-3.9 1.7-11.7 3.6 1.3 4.7 5.2 6.2 7.9s4.1 6 7.5 4.5c3.4-1.5 4.9-6.5 5.4-13.8 2.2 2.1 2.2 6.1.7 10.6s-.8 6.9.9 8.6c1.7 1.7 5.2 2.4 10.2.3 4.9-2.1 8.3-7.4 7.7-15.6 3.3 2.1 3 13.8-2.7 20.4-5.7 6.6-9.6 7.9-13.1 9.3l-11.1-4.2-47.1 8.9"
      fill="url(#benjamin_master_svg__SVGID_1_)"
      stroke="#cea449"
      strokeWidth={0.425}
      strokeMiterlimit={10}
    />
    <path
      d="M252 68.3c.5.1.8-.4.6-.9-.9-2.2-2.6-4.3-4.9-5.9-1-.7-2.4-1.1-3.2-1.9.3 1.9.5 2.9.9 3.8.6 1.4 1.7 2.7 3.3 3.6 1.1.6 2.1 1.1 3.3 1.3zm17.3 144.4c1.8-2.8-.3-7.9-2.8-11.2-2.5-3.3-14.9-13.1-26-19.2 2.4-1.7 4.3-3.2 6.3-5.3 3.6-1.6 7.1-4 9.4-7.2.6 1.7 1.1 2.7 1.8 4.3 4.3 8.8 10.8 15.8 18.7 23.6 9.3 9.2 21.4 12 23.7 12.4 1.1.2 3 .2 4.7.2 2.6 0 7.5 3.3 9 6.3h-49.3c.1-1 1.9-2.8 4.5-3.9zM252 96.7c-.2 3.1.3 5.5 3.2 7.2 2.9 1.7 6.1-.2 8-1.7 1.8-1.5 3 .9.7 2.8s-6.9 3.3-11.6 1.2c-2.8-1.3-4.2-6.9-4.3-9.3-.1-2.2-1.8-4.8-3.4-6.4l-3.3-3.3c-.6-.6-.7-1.3-.6-1.7 0-.4.1-1.1.8-1.6.5-.4 1.1-.5 1.5-.5.6 0 1.2.2 1.6.7l5 5.1s1.3 1.2 1.7 2.4c.4 1 1 2 .7 5.1zM147.2 167.2c1 .3 2.1.7 3.2 1.1 5.3 2 12.4 6.5 19.3 10.8.8.5 1.7 1 2.5 1.5-10 8.1-15.8 5.7-19.8 8.7-4.1 3-4.2 7.8-.4 12 3.9 4.2 8.7 6.7 14.6 8.1 3.5.8 7.5.9 10.2.9 1 0 5.7 1.1 8.7 6.2H136s2.2-2.8 4.8-4.2c-1-11.1-4.3-17.9-8-23.2 7-5.5 12.2-13.1 14.4-21.9zM8 155.9c5.1-6 26.8-15.5 31.2-21s1.5-15.7-5.8-22.3l-4 10.4-13-33.7 32.1 16.4s-8.8 3.8-10.8 4.4c4.9 3.5 9.7 10.4 9.7 19.2s-8.2 16.4-20.1 22.6c-11.9 6.2-18.1 13.3-17.3 23.9.9 10.6 7.3 18.8 19 19.9s19.3-7.3 19.3-7.3c5.9-6.3 15.3-22.8 27.7-34.9 12.4-12 26.6-18.1 37.9-21.9s21.2-5.4 27.5-4.2c6.3 1.1 10.8 5.3 17.6 10.4-6.3-11.3-13.1-14.8-19-16.6-5.9-1.8-14.9-.6-33.3-1.4-11.8-.5-20.5-5.1-26.7-9.9-2.1-1.7-.6-5 2.1-4.4 9.4 2.1 22 3.3 32 3.1 0 0-17.4-5.1-28.5-9.3-7.5-2.9-12.2-8-15.5-13.7-1.3-2.2 1.2-4.7 3.5-3.4 11.6 6.7 28.8 12.2 36 12.2-19.3-7.7-34.5-22.9-37-26.4-1.2-1.6-2-3.9-2.5-6-.4-2.3 2.3-3.9 4.1-2.4 8.4 7.2 22.4 15.7 37.4 21-14.4-7-29.1-25.2-32.9-30.8-1.7-2.5-2.8-6.4-3.5-10-.5-2.5 2.6-4.1 4.3-2.2 9.7 11.1 24.8 24.2 36.6 30.3C93.7 48 89.2 35.6 86.8 28.2c-.7-2.3-1.1-5.1-1.3-7.9-.2-2.7 3.4-3.7 4.7-1.4 1 1.8 2.2 3.9 3.6 6.3 7.9 13 17 20.9 30.9 32.3.7.5-16.7-23-22.2-34.6-3.3-6.8-3.6-14.1-3.2-20.6.2-2.7 3.8-3.3 4.8-.8 4.8 11.7 14.3 29.2 27.1 43 18.4 20 36.8 33.4 43.1 38.5 6.3 5.1 8.4 10.7 7.5 16.6-.9 5.9-9 12.5-5.7 23.8 3.3 11.2 10 15.8 19.7 18.1 9.7 2.3 16.5-.9 18.8-6.3 2.3-5.4-4.9-10.9-14.4-19.3s-10.3-17.8-10-28.6c.3-10.8 5.2-20.9 19.4-29.8.3-.1-3.1-3.5-5.7-6.4 2.1 0 10.4.5 20.5 3l-3.4-6.5c2.5.3 10.4 2.3 16.8 5.9l.7-5s13.1 9.9 18.9 16.8c3.5 4.2 7 10.4 9.1 14.6 1.3 2.5.6 5.6-1.7 7.3l-3.3 2.5c-2 1.5-4.8 1.3-6.6-.5l-7.7-7.9c-2.2-2.3-5.9-2.5-8.3-.4-2.7 2.3-2.9 6.4-.4 8.9 1.1 1.1 2.3 2.3 3.3 3.4 1.7 1.7 1.7 4.4 0 6.1-1.7 1.7-4.5 1.6-6.2-.1-2.6-2.7-6-6.4-6.9-7.3-1.5-1.5-5.7-6.3-3.1-14.3-11.6 16 6 24.3 19.7 36.2 13.7 11.9 15.9 25 13.7 37.4-2.2 12.4-12.8 27-27.2 33.4-5.9 2.6-11.9 3.9-17.6 4.4-1.3 7.2 1.5 12.2 6.2 16.4 0 0 7.3 4.9 15.3 5.1 3.8.1 10.2.8 13.1 6.1h-48.4c.5-1.9 2.3-3.7 4.4-4.1.5-7.1 2-11.7-11.3-36.3 6.5.9 15-3.4 19-11.8-10.1 6.7-14.5 9.1-24.3 7 1.6 4.6 3.2 8.9 4.4 15.5-10.2-2.7-29-17.8-41-22.3-2.7-1-5.1-1.8-7.5-2.4-2.5 18.3-18.8 32.4-38.5 32.4-2.4.2-4.8-.1-6 0-4 1-5 2.9-3.5 8.9s21.6 6.9 24.8 7.2 6.7 3 7.5 6H78.9s3.1-1.8 4.3-4.6c1.2-2.8.6-5.7-.9-8.5-3-6.5-3.7-13.8-3.7-13.8 0-4.2.9-8 2.4-11.3 2.8 2.4 6.9 2.7 11.3 2.9 12.3 0 22.5-9.4 23.8-21.3h-2.8c-20.4.2-34.1 11.1-39.4 18.1-5.3 7.1-16.1 24.6-33.8 29-17.7 4.4-31-3.5-37.4-16.8-6.5-13.5.2-28.6 5.3-34.5z"
      fill="#432063"
    />
    <linearGradient
      id="benjamin_master_svg__SVGID_2_"
      gradientUnits="userSpaceOnUse"
      x1={7444.101}
      y1={54.27}
      x2={7448.049}
      y2={54.27}
      gradientTransform="matrix(-1 0 0 1 7699.112 0)"
    >
      <stop offset={0} stopColor="#2f2f7f" />
      <stop offset={0.075} stopColor="#303181" />
      <stop offset={0.153} stopColor="#313985" />
      <stop offset={0.233} stopColor="#35458c" />
      <stop offset={0.314} stopColor="#3b5597" />
      <stop offset={0.396} stopColor="#446aa6" />
      <stop offset={0.478} stopColor="#5085bb" />
      <stop offset={0.489} stopColor="#528abf" />
      <stop offset={0.52} stopColor="#4b7baf" />
      <stop offset={0.637} stopColor="#385485" />
      <stop offset={0.748} stopColor="#2a3869" />
      <stop offset={0.849} stopColor="#202457" />
      <stop offset={0.936} stopColor="#1a164d" />
      <stop offset={1} stopColor="#18114a" />
    </linearGradient>
    <circle cx={253} cy={54.3} r={2} fill="url(#benjamin_master_svg__SVGID_2_)" />
    <linearGradient
      id="benjamin_master_svg__SVGID_3_"
      gradientUnits="userSpaceOnUse"
      x1={7452.758}
      y1={48.918}
      x2={7456.171}
      y2={48.918}
      gradientTransform="matrix(-1 0 0 1 7699.112 0)"
    >
      <stop offset={0} stopColor="#2f2f7f" />
      <stop offset={0.075} stopColor="#303181" />
      <stop offset={0.153} stopColor="#313985" />
      <stop offset={0.233} stopColor="#35458c" />
      <stop offset={0.314} stopColor="#3b5597" />
      <stop offset={0.396} stopColor="#446aa6" />
      <stop offset={0.478} stopColor="#5085bb" />
      <stop offset={0.489} stopColor="#528abf" />
      <stop offset={0.52} stopColor="#4b7baf" />
      <stop offset={0.637} stopColor="#385485" />
      <stop offset={0.748} stopColor="#2a3869" />
      <stop offset={0.849} stopColor="#202457" />
      <stop offset={0.936} stopColor="#1a164d" />
      <stop offset={1} stopColor="#18114a" />
    </linearGradient>
    <circle cx={244.6} cy={48.9} r={1.7} fill="url(#benjamin_master_svg__SVGID_3_)" />
    <linearGradient
      id="benjamin_master_svg__SVGID_4_"
      gradientUnits="userSpaceOnUse"
      x1={7462.326}
      y1={47.42}
      x2={7465.091}
      y2={47.42}
      gradientTransform="matrix(-1 0 0 1 7699.112 0)"
    >
      <stop offset={0} stopColor="#2f2f7f" />
      <stop offset={0.075} stopColor="#303181" />
      <stop offset={0.153} stopColor="#313985" />
      <stop offset={0.233} stopColor="#35458c" />
      <stop offset={0.314} stopColor="#3b5597" />
      <stop offset={0.396} stopColor="#446aa6" />
      <stop offset={0.478} stopColor="#5085bb" />
      <stop offset={0.489} stopColor="#528abf" />
      <stop offset={0.52} stopColor="#4b7baf" />
      <stop offset={0.637} stopColor="#385485" />
      <stop offset={0.748} stopColor="#2a3869" />
      <stop offset={0.849} stopColor="#202457" />
      <stop offset={0.936} stopColor="#1a164d" />
      <stop offset={1} stopColor="#18114a" />
    </linearGradient>
    <circle cx={235.4} cy={47.4} r={1.4} fill="url(#benjamin_master_svg__SVGID_4_)" />
    <linearGradient
      id="benjamin_master_svg__SVGID_5_"
      gradientUnits="userSpaceOnUse"
      x1={7437.736}
      y1={150.11}
      x2={7505.874}
      y2={150.11}
      gradientTransform="matrix(-1 0 0 1 7699.112 0)"
    >
      <stop offset={0} stopColor="#fdecf3" />
      <stop offset={0.112} stopColor="#f5e9f3" />
      <stop offset={0.223} stopColor="#f8eef1" />
      <stop offset={0.354} stopColor="#fffef1" />
      <stop offset={0.488} stopColor="#faf9f1" />
      <stop offset={0.614} stopColor="#eceff1" />
      <stop offset={0.714} stopColor="#dce4f4" />
      <stop offset={1} stopColor="#fee9df" />
    </linearGradient>
    <path
      d="M210.7 125.5c-5.5-1.9-12.4-2-16.4 5.6-4.1 7.7 5 15.5 10.6 16.5s26 4.7 33.1 9.2c7.1 4.5 10.9 13.4 6.9 18.8 10.4-5.1 20.4-15.9 15-31.7-.1 3.8-2.6 13.2-5.2 15.5-1.1-7.7-10.5-18.8-19.5-22.3-9-3.2-19.4-6.5-24.5-11.6z"
      fill="url(#benjamin_master_svg__SVGID_5_)"
      stroke="#939598"
      strokeWidth={0.213}
      strokeMiterlimit={10}
    />
    <linearGradient
      id="benjamin_master_svg__SVGID_6_"
      gradientUnits="userSpaceOnUse"
      x1={7431.191}
      y1={57.204}
      x2={7441.169}
      y2={57.204}
      gradientTransform="matrix(-1 0 0 1 7699.112 0)"
    >
      <stop offset={0} stopColor="#2f2f7f" />
      <stop offset={0.075} stopColor="#303181" />
      <stop offset={0.153} stopColor="#313985" />
      <stop offset={0.233} stopColor="#35458c" />
      <stop offset={0.314} stopColor="#3b5597" />
      <stop offset={0.396} stopColor="#446aa6" />
      <stop offset={0.478} stopColor="#5085bb" />
      <stop offset={0.489} stopColor="#528abf" />
      <stop offset={0.52} stopColor="#4b7baf" />
      <stop offset={0.637} stopColor="#385485" />
      <stop offset={0.748} stopColor="#2a3869" />
      <stop offset={0.849} stopColor="#202457" />
      <stop offset={0.936} stopColor="#1a164d" />
      <stop offset={1} stopColor="#18114a" />
    </linearGradient>
    <path
      d="M267.9 54.6c-.4 3-2.4 7.6-9.2 7.1-1.3-.8-1.1-5.2 1.5-7.1 2.7-1.9 7.2-3 7.7 0z"
      fill="url(#benjamin_master_svg__SVGID_6_)"
    />
    <path
      className="benjamin_master_svg__st8"
      d="M208.7 129.7c1 .9 2.9 3 4 3.5-2.7-.3-7.4-2-7.9-2.9 1 .1 3.9-.6 3.9-.6zM224.1 136.6c1 .7 2.9 2.2 3.9 2.6-2.7-.2-7.3-1.5-7.8-2.1 1 0 3.9-.5 3.9-.5zM243.9 147.3c.7 1 1.7 3.2 2.4 4-2.3-1.3-6-4.4-6.2-5.1 1 .3 3.8 1.1 3.8 1.1zM210.6 140.7c1 .7 2.9 2.2 3.9 2.6-2.7-.2-7.3-1.5-7.8-2.1 1.1 0 3.9-.5 3.9-.5zM229.4 147.2c.9.8 2.5 2.6 3.4 3.2-2.6-.6-6.9-2.6-7.4-3.3 1.1.1 4 .1 4 .1zM247.6 163.5c.7.9 2.2 2.6 2.6 3.5-.2-2.4-1.6-6.6-2.2-7 0 .9-.4 3.5-.4 3.5z"
    />
  </svg>
);

export default SvgBenjaminMaster;
