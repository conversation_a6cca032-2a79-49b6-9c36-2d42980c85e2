import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles(theme => ({
  root: {
    "& :hover": {}
  }
}));
const onHandleOnclick = props => {
  if (props.redirect && props.url) window.open(props.url);
};
function Phone_Outline_Icon(props) {
  function styledIcon(props) {
    const classes = makeStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={classes.root}>
        <SvgIcon
          {...props}
          viewBox="0 0 20 21"
          onClick={() => onHandleOnclick(props)}
        >
          <path
            d="M2.2.348s.75-.6 1.726-.225c1.05.3 1.652 1.352 2.403 3.228.825 1.952.825 1.877.45 2.552-.3.676-1.426 1.877-1.426 1.877.45 1.277 5.63 6.381 6.906 6.832 0 0 1.2-1.05 1.802-1.427.675-.375.675-.3 2.552.451 1.952.75 2.928 1.351 3.303 2.402.3 1.051-.3 1.801-.3 1.801s-1.426 1.728-2.552 2.028C11.133 21.593-1.63 8.907.173 2.901.473 1.851 2.2.348 2.2.348z"
            fill="#7A7A7A"
            fillRule="evenodd"
        />
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_Phone_Icon = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {}
    }
  })(styledIcon);
  return (
    <>
      <Custom_Phone_Icon {...props} />
    </>
  );
}

export default Phone_Outline_Icon;
