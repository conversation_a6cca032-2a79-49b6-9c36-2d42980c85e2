import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { makeStyles, withStyles } from '@material-ui/core/styles';

import { color } from '../../../utilities/themes';

const useStyles = makeStyles(theme => ({
  root: {
    '& :hover': {},
  },
}));

function Lil_Plus_filled(props) {
  const getColor = (props, line_type) => {
    let colorValue = props.color ? props.color : color.secondary_palette.grays.light_gray;

    if (props && props.color === 'primary') {
      if (line_type === 'stroke') {
        colorValue = color.primary_palette.franklin_purple;
      }
      if (line_type === 'fill') {
        colorValue = color.primary_palette.white;
      }
    } else {
    }

    return colorValue;
  };

  const classes = useStyles(props);
  function styledIcon(props) {

    return (
      <span className={classes.root}>
        <SvgIcon {...props} viewBox="0 0 12 12">
        <g fill="none" fillRule="evenodd">
            <path d="M6 12A6 6 0 116 0a6 6 0 010 12z" fill={getColor(props, 'fill')} />
            <path
            fill="#FEFEFE"
            d="M10.5 4.998H7.003V1.5H4.998v3.498H1.5v2.004h3.498V10.5h2.005V7.002H10.5z"
            />
        </g>
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_Lil_Plus_filled = withStyles({
    root: {
      '& > svg': {
        fontSize: '1.65rem',
      },
      '& :hover': {},
    },
  })(styledIcon);
  return (
    <>
      <Custom_Lil_Plus_filled {...props} />
    </>
  );
}

export default Lil_Plus_filled;
