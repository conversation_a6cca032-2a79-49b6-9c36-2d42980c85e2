import React from 'react';

const SvgSmallFlipbook = props => (
  <svg width={23} height={23} {...props}>
    <defs>
      <path id="small_flipbook_svg__a" d="M0 .07h22.447V23H0z" />
    </defs>
    <g fill="none" fillRule="evenodd">
      <g transform="translate(0 -.07)">
        <mask id="small_flipbook_svg__b" fill="#fff">
          <use xlinkHref="#small_flipbook_svg__a" />
        </mask>
        <path
          d="M0 8.523l.096-.967.262-.01c.081-.002 8.153-.365 10.519-6.985l.176-.491 10.765 12.752-.172.194s-.46.52-.916 1.045l1.008 1.078-.325.328 1.034 1.035s-.812.725-.916.813c-3.493 2.72-7.64 4.562-12.691 5.64L8.63 23 0 8.523z"
          fill="#7A7A7A"
          mask="url(#small_flipbook_svg__b)"
        />
      </g>
      <path
        d="M20.114 14.287c-5.508 5.877-11.289 7.688-11.289 7.688v.113c3.183-.897 7.822-3.398 10.234-5.38 1.198-.984 1.804-1.64 1.804-1.64l-.749-.781z"
        fill="#FEFEFE"
      />
      <path
        d="M8.714 21.622s3.02-.81 6.534-3.52c1.743-1.344 3.716-3.026 5.465-5.319l-9.405-11.23S9.368 7.29.832 8.283l7.882 13.34zM21.457 16.35l-.639-.666c-5.429 5.04-11.992 6.522-11.992 6.522v.107c3.488-.785 6.427-2.006 8.588-3.2 2.57-1.42 4.043-2.763 4.043-2.763"
        fill="#FEFEFE"
      />
      <path
        d="M10.494 8.757a385.997 385.997 0 005.213 7.548c-.82.685-1.085.89-1.777 1.37-1.787-2.562-3.51-5.153-5.348-7.68-.89.5-1.817.898-3.138 1.508-.082.038-.17.07-.283.114-.039-.079-.082-.142-.103-.213-.216-.706.168-1.35.85-1.76.507-.307.74-.692.872-1.167.065-.233.158-.449.34-.612l.035-.032c.052-.042.231-.136.231-.136S8.88 6.66 8.88 6.657c.33-.3.674-.347 1.023-.309.536.06 1.018 0 1.455-.308.586-.41 1.173-.32 1.685.***************.117.146.186-.057.064-.1.118-.147.164-.685.672-1.832 1.612-2.548 2.163"
        fill="#7A7A7A"
      />
      <path fill="#FEFEFE" d="M8.876 7.246l-1.096.731 6.2 8.976 1.041-.787z" />
    </g>
  </svg>
);

export default SvgSmallFlipbook;
