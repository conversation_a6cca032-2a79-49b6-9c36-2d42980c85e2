import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles(theme => ({
  root: {
    "& :hover": {}
  }
}));
const onHandleOnclick = props => {
  if (props.redirect && props.url) window.open(props.url);
};
function Website_Outline_Icon(props) {
  function styledIcon(props) {
    const classes = makeStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={classes.root}>
        <SvgIcon
          {...props}
          viewBox="0 0 30 23"
          onClick={() => onHandleOnclick(props)}
        >
          <g fill={getColor(props, "stroke")} fillRule="evenodd">
            <path d="M14.069 15.464l.035.014c.726.343 1.235.772 1.507 1.084a8.667 8.667 0 01-3.896 1.884 13.654 13.654 0 002.354-2.982zm-8.09-.02c.278.527.582 1.007.889 1.436.426.594.868 1.107 1.284 1.54a8.663 8.663 0 01-3.764-1.858c.273-.312.781-.74 1.508-1.084.026-.013.056-.022.083-.035zm4.687-.78c.787.045 1.507.163 2.139.338-.67 1.11-1.47 1.975-2.139 2.602v-2.94zm-1.333 0v2.81a13.21 13.21 0 01-1.924-2.127c-.078-.11-.153-.224-.229-.339a10.373 10.373 0 012.153-.345zm6.194-4.005h3.111a8.545 8.545 0 01-2.062 4.95c-.44-.5-1.088-.94-1.903-1.325h-.006a10.72 10.72 0 00.86-3.625zm-4.861 0h3.528a9.421 9.421 0 01-.777 3.148 11.451 11.451 0 00-2.751-.476V10.66zm-5.055 0h3.722v2.672a11.393 11.393 0 00-2.833.504 8.788 8.788 0 01-.889-3.176zm-4.25 0h3.298c.073 1.354.366 2.544.771 3.583-.033.015-.071.026-.104.042-.815.385-1.463.824-1.903 1.325a8.552 8.552 0 01-2.062-4.95zm12.056-4.473c.404.923.696 1.972.777 3.148h-3.528V6.67a11.392 11.392 0 002.751-.483zM6.5 6.158c.83.264 1.781.455 2.833.511v2.665H5.611A8.793 8.793 0 016.5 6.158zm10.076-1.774a8.545 8.545 0 012.062 4.95h-3.111a10.663 10.663 0 00-.867-3.624l.013-.007c.815-.385 1.463-.817 1.903-1.319zm-13.153 0c.44.502 1.088.934 1.903 1.32.034.014.07.031.104.047a11.502 11.502 0 00-.771 3.583H1.361a8.552 8.552 0 012.062-4.95zm5.91-1.864v2.817a10.225 10.225 0 01-2.153-.352c.076-.114.151-.229.229-.338.644-.9 1.349-1.61 1.924-2.127zm1.333-.13c.669.627 1.469 1.492 2.139 2.602a10.3 10.3 0 01-2.139.345V2.39zm-2.514-.816c-.416.432-.858.945-1.284 1.54-.307.43-.611.908-.889 1.436-.028-.013-.056-.029-.083-.042-.726-.342-1.235-.765-1.508-1.077a8.663 8.663 0 013.764-1.857zm3.556-.027a8.651 8.651 0 013.903 1.884c-.273.312-.781.735-1.507 1.077l-.042.021a13.66 13.66 0 00-2.354-2.982zM9.993.001a.276.276 0 00-.153.062C4.399.148 0 4.568 0 9.997c0 5.43 4.399 9.848 9.84 9.934a.264.264 0 00.354 0C15.619 19.827 20 15.414 20 9.997c0-5.42-4.384-9.834-9.813-9.934a.267.267 0 00-.194-.062z" />
          </g>
          {/* <svg width={20} height={20} viewBox="0 0 20 20" {...props}>
      <title>{"Icon / Website Gray"}</title>
      <path
        d="M14.069 15.464l.035.014c.726.343 1.235.772 1.507 1.084a8.667 8.667 0 01-3.896 1.884 13.654 13.654 0 002.354-2.982zm-8.09-.02c.278.527.582 1.007.889 1.436.426.594.868 1.107 1.284 1.54a8.663 8.663 0 01-3.764-1.858c.273-.312.781-.74 1.508-1.084.026-.013.056-.022.083-.035zm4.687-.78c.787.045 1.507.163 2.139.338-.67 1.11-1.47 1.975-2.139 2.602v-2.94zm-1.333 0v2.81a13.21 13.21 0 01-1.924-2.127c-.078-.11-.153-.224-.229-.339a10.373 10.373 0 012.153-.345zm6.194-4.005h3.111a8.545 8.545 0 01-2.062 4.95c-.44-.5-1.088-.94-1.903-1.325h-.006a10.72 10.72 0 00.86-3.625zm-4.861 0h3.528a9.421 9.421 0 01-.777 3.148 11.451 11.451 0 00-2.751-.476V10.66zm-5.055 0h3.722v2.672a11.393 11.393 0 00-2.833.504 8.788 8.788 0 01-.889-3.176zm-4.25 0h3.298c.073 1.354.366 2.544.771 3.583-.033.015-.071.026-.104.042-.815.385-1.463.824-1.903 1.325a8.552 8.552 0 01-2.062-4.95zm12.056-4.473c.404.923.696 1.972.777 3.148h-3.528V6.67a11.392 11.392 0 002.751-.483zM6.5 6.158c.83.264 1.781.455 2.833.511v2.665H5.611A8.793 8.793 0 016.5 6.158zm10.076-1.774a8.545 8.545 0 012.062 4.95h-3.111a10.663 10.663 0 00-.867-3.624l.013-.007c.815-.385 1.463-.817 1.903-1.319zm-13.153 0c.44.502 1.088.934 1.903 1.32.034.014.07.031.104.047a11.502 11.502 0 00-.771 3.583H1.361a8.552 8.552 0 012.062-4.95zm5.91-1.864v2.817a10.225 10.225 0 01-2.153-.352c.076-.114.151-.229.229-.338.644-.9 1.349-1.61 1.924-2.127zm1.333-.13c.669.627 1.469 1.492 2.139 2.602a10.3 10.3 0 01-2.139.345V2.39zm-2.514-.816c-.416.432-.858.945-1.284 1.54-.307.43-.611.908-.889 1.436-.028-.013-.056-.029-.083-.042-.726-.342-1.235-.765-1.508-1.077a8.663 8.663 0 013.764-1.857zm3.556-.027a8.651 8.651 0 013.903 1.884c-.273.312-.781.735-1.507 1.077l-.042.021a13.66 13.66 0 00-2.354-2.982zM9.993.001a.276.276 0 00-.153.062C4.399.148 0 4.568 0 9.997c0 5.43 4.399 9.848 9.84 9.934a.264.264 0 00.354 0C15.619 19.827 20 15.414 20 9.997c0-5.42-4.384-9.834-9.813-9.934a.267.267 0 00-.194-.062z"
        fill="#7A7A7A"
        fillRule="evenodd"
      />
    </svg> */}
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_website_Icon = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {}
    }
  })(styledIcon);
  return (
    <>
      <Custom_website_Icon {...props} />
    </>
  );
}

export default Website_Outline_Icon;
