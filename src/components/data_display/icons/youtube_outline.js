import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles(theme => ({
  root: {
    "& :hover": {}
  }
}));
const onHandleOnclick = props => {
  if (props.redirect && props.url) window.open(props.url);
};
function Youtube_Outline_Icon(props) {
  function styledIcon(props) {
    const classes = makeStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={classes.root}>
        <SvgIcon
          {...props}
          viewBox="0 0 36 36"
          onClick={() => onHandleOnclick(props)}
        >
          <g fill="none" fillRule="evenodd">
            <path
            d="M35 26.499A8.5 8.5 0 0126.499 35h-17A8.499 8.499 0 011 26.499V9.5C1 4.805 4.803 1 9.499 1h17A8.5 8.5 0 0135 9.5v16.999z"
            stroke="#7F7F7F"
            strokeWidth={1.5}
            />
            <path
            d="M17.506 10h-.01s-4.409 0-7.347.23c-.41.052-1.304.057-2.104.962-.63.69-.834 2.26-.834 2.26S7 15.291 7 17.132v1.726c0 1.842.21 3.683.21 3.683s.206 1.567.835 2.257c.8.907 1.85.876 2.315.972 1.681.174 7.14.229 7.14.229s4.413-.01 7.353-.237c.409-.053 1.305-.057 2.103-.964.628-.69.833-2.257.833-2.257S28 20.7 28 18.859v-1.726c0-1.84-.21-3.682-.21-3.682s-.206-1.568-.834-2.26c-.798-.904-1.694-.91-2.103-.962C21.913 10 17.506 10 17.506 10"
            fill="#7F7F7F"
            />
            <path fill="#FFF" d="M15.002 21L21 18.011 15 15z" />
        </g>
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_Youtube_Icon = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {}
    }
  })(styledIcon);
  return (
    <>
      <Custom_Youtube_Icon {...props} />
    </>
  );
}

export default Youtube_Outline_Icon;
