import React from 'react';

const SvgBenjaminWarrior = props => (
  <svg id="benjamin_warrior_svg__Layer_1" x={0} y={0} viewBox="0 0 298.4 192.5" xmlSpace="preserve" {...props}>
    <style>{'.benjamin_warrior_svg__st14{fill:#30459c}'}</style>
    <path
      className="benjamin_warrior_svg__st14"
      d="M253.1 44.1c-2-2.8-6-6.7-8.6-8.4.6 1.1.5 2.7.8 3.7s1.3 2.6 2.9 3.3 3.2.4 4.9 1.4zm-105.9 99c1 .3 2.1.7 3.2 1.1 5.3 2 12.4 6.5 19.3 10.8.8.5 1.7 1 2.5 1.5-10 8.1-15.8 5.7-19.8 8.7-4.1 3-4.2 7.8-.4 12 3.9 4.2 8.7 6.7 14.6 8.1 3.5.8 7.5.9 10.2.9 1 0 5.7 1.1 8.7 6.2H136s2.2-2.8 4.8-4.2c-1-11.1-4.3-17.9-8-23.2 7-5.5 12.2-13.1 14.4-21.9zM128 91.7c-4.2 0-15.6-5.1-18.1-10.7 0 0-12.7-2.3-25.2-3.1-12.4-.8-28.3-9.6-30-14.7-20.9-2.6-35-20.5-36.7-23-1.7-2.5 15 5.1 29.4 8.5 14.4 3.4 37.3 4.2 74.1 1.7s44.4 2.5 52.8 8.5c6.5 4.7 8.4 10.7 7.5 16.6-.9 5.9-9 12.5-5.7 23.8 3.3 11.2 10 15.8 19.7 18.1 9.7 2.3 16.5-.9 18.8-6.3 2.3-5.4-4.9-10.9-14.4-19.3s-10.3-17.8-10-28.6c.3-10.8 5.2-20.9 19.4-29.8.3-.1-3.1-3.5-5.7-6.4 2.1 0 10.4.5 20.5 3l-3.4-6.5c2.5.3 10.4 2.3 16.8 5.9l.7-5s13.1 9.9 18.9 16.8c3.5 4.2 7 10.4 9.1 14.6 1.3 2.5.6 5.6-1.7 7.3l-3.3 2.5c-2 1.5-4.8 1.3-6.6-.5l-7.7-7.9c-2.2-2.3-5.9-2.5-8.3-.4-2.7 2.3-2.9 6.4-.4 8.9 1.1 1.1 2.3 2.3 3.3 3.4 1.7 1.7 1.7 4.4 0 6.1-1.7 1.7-4.5 1.6-6.2-.1-2.6-2.7-6-6.4-6.9-7.3-1.5-1.5-5.7-6.3-3.1-14.3-11.6 16 6 24.3 19.7 36.2 13.7 11.9 15.9 25 13.7 37.4-2.2 12.4-12.8 27-27.2 33.4-5.9 2.6-11.9 3.9-17.6 4.4-1.3 7.2 1.5 12.2 6.2 16.4 0 0 7.3 4.9 15.3 5.1 3.8.1 10.2.8 13.1 6.1h-48.4c.5-1.9 2.3-3.7 4.4-4.1.5-7.1 2-11.7-11.3-36.3 6.5.9 15-3.4 19-11.8-10.1 6.7-14.5 9.1-24.3 7 1.6 4.6 3.2 8.9 4.4 15.5-10.2-2.7-29-17.8-41-22.3-2.7-1-5.1-1.8-7.5-2.4-2.5 18.3-18.8 32.4-38.5 32.4-2.4.2-4.8-.1-6 0-4 1-5 2.9-3.5 8.9s21.6 6.9 24.8 7.2 6.7 3 7.5 6H78.9s3.1-1.8 4.3-4.6.6-5.7-.9-8.5c-3-6.5-3.7-13.8-3.7-13.8 0-4.2.9-8 2.4-11.3 2.8 2.4 6.9 2.7 11.3 2.9 12.3 0 22.5-9.4 23.8-21.3h-2.8c-20.4.2-34.1 11.1-39.4 18.1-5.3 7.1-16.1 24.6-33.8 29-17.7 4.4-31-3.5-37.4-16.8C-3.7 153 2.9 138 8 132c5.1-6 26.8-15.5 31.2-21 4.4-5.5 1.5-15.7-5.8-22.3l-4 10.4-13-33.9 32.1 16.4s-8.8 3.8-10.8 4.4c4.9 3.5 9.7 10.4 9.7 19.2s-8.2 16.4-20.1 22.6c-12 6.2-18.2 13.3-17.4 23.9.9 10.6 7.3 18.8 19 19.9 11.7 1.1 19.3-7.3 19.3-7.3 5.9-6.3 15.3-22.8 27.7-34.9s26.6-18.1 37.9-21.9 21.2-5.4 27.5-4.2 10.8 5.3 17.6 10.4c-6.3-11.3-8.3-12.5-14.2-14.4-5.9-.5-15.3-3.1-16.7-7.6z"
    />
    <linearGradient
      id="benjamin_warrior_svg__SVGID_1_"
      gradientUnits="userSpaceOnUse"
      x1={-1935.937}
      y1={98.57}
      x2={-1954.085}
      y2={76.327}
      gradientTransform="translate(2206.863)"
    >
      <stop offset={0} stopColor="#fcef42" />
      <stop offset={0.059} stopColor="#fcde31" />
      <stop offset={0.13} stopColor="#fcd121" />
      <stop offset={0.203} stopColor="#fdc917" />
      <stop offset={0.281} stopColor="#fdc714" />
      <stop offset={0.668} stopColor="#f29040" />
      <stop offset={0.888} stopColor="#ed693c" />
      <stop offset={1} stopColor="#e74039" />
    </linearGradient>
    <path
      d="M243.1 60.8c-1.6.9-1.3 2.2-.5 3.3.8 1.2 4.3 4.1 5.5 6.3 1.2 2.2.8 8.6 3.3 10.9 2.5 2.3 7.4 6.2 8.8 9.6 1.2 2.8 1 5.6-.8 8.9 3.1-2.2 4.5-7.1 2.8-10.4 4.8 5.6 7.9 10.5 8.3 13.8.5 5.2-2.7 7.6-2.7 7.6 6.2-1.9 5.6-7.9 4.4-10.7-1.3-3.2-5.7-9.1-5.7-9.1 1.6 1 4.3 1.1 5.4.7 1.5-.6 2.7-2.1 2.8-3.2.1-1.2-.5-5.1-5.3-2.8 1.8-.7 3.3.9 3.3 1.8-.1 1.4-1.2 2.6-4.5 2.1-2.4-.3-6-2.2-8.8-5.4 1.7 0 6.3-.7 5.8-5.1-1.2 2.8-7.5 4.6-10.3 1.3-2.8-3.3-2.2-7.6-2.4-10.3-.2-3.1-5-7.1-5.7-7.9-.7-.7-2.3-2.2-3.7-1.4z"
      fill="url(#benjamin_warrior_svg__SVGID_1_)"
    />
    <linearGradient
      id="benjamin_warrior_svg__SVGID_2_"
      gradientUnits="userSpaceOnUse"
      x1={-2015.627}
      y1={27.596}
      x2={-1940.231}
      y2={27.596}
      gradientTransform="translate(2206.863)"
    >
      <stop offset={0} stopColor="#e5e2df" />
      <stop offset={0} stopColor="#ddd9d6" />
      <stop offset={0.058} stopColor="#cec9c8" />
      <stop offset={0.169} stopColor="#aaa5a7" />
      <stop offset={0.264} stopColor="#8b878c" />
      <stop offset={0.522} stopColor="#f2f3f3" />
      <stop offset={0.584} stopColor="#e0e0e0" />
      <stop offset={0.702} stopColor="#b8b6b8" />
      <stop offset={0.863} stopColor="#848184" />
      <stop offset={0.876} stopColor="#807d81" />
      <stop offset={1} stopColor="#d0d2d3" />
      <stop offset={1} stopColor="#a7a9ac" />
    </linearGradient>
    <path
      d="M253.7 38.1l2.3-3.3c-2.5-4-10.3-10.3-16.2-12.4 10.6-.7 16.9 1.8 17.7 3.1 0 1.7-.2 5.5-.2 5.5s9.9-8.8 9.3-9.3C257.8 10.1 246.9 3.6 228.3.8S196.7 4.1 191.2 7c3.8 2.7 9.4 6.8 10.9 10.3 4-3.6 14.2-2.5 18.7 2.5-6.8.2-15.7 3-20.2 8.4.2 5.1 1.3 10.4 1.3 10.4s11.4-7.3 13.4-8.1c.2 4 0 10.8 3.8 16.6 3.8 5.8 6.6 8.6 9.4 8 2.8-.7 2-3.1 0-6.6-2-3.5 1-16.2 8-17.7 4.9.5 12.9 3.3 17.2 7.3z"
      fill="url(#benjamin_warrior_svg__SVGID_2_)"
      stroke="#969696"
      strokeWidth={0.213}
      strokeMiterlimit={10}
    />
    <path d="M279.5 158.6s.6 1.7 5 1.7 5.3-1.7 5.3-1.7v-24.5h-10.3v24.5z" />
    <path
      className="benjamin_warrior_svg__st14"
      d="M255.9 140.4c3.6 1.8 7.4 3.1 9.3 3.5 2 .4 6.7.1 7.6-1 0-1.2.1-2.5.6-4 .4-1.4 1.6-3.2 2.8-3.6 1.2-.4 3.2.2 4.5.2s4.8-.6 5.5-1c-1.9 2-2 2.4-2.5 3.5-.4 1-.7 3.6-4.1 4-.2.2-.2 1.2-.2 1.2v15.4c-1.7-.6-7.5-2.3-12.3-2.8-4.8-.5-25.5.8-25.5.8s5.6-4 9.1-8.4c2.5-3 5.2-7.8 5.2-7.8z"
    />
    <path
      className="benjamin_warrior_svg__st14"
      d="M291.7 136c.6-.3 3.4-.1 4.1 1.2.7 1.2.8 2.3-.2 3.2-1 .9-2.3 1.5-2.3 1.5 1.2 0 2.9.7 2.8 2.3-.1 1.5-.3 2.1-1.8 2.9 1.4.6 2 1.8 1.6 3-.4 1.2-.9 2-2 2.3.6.5 1.4 1.5.5 2.6s-3 2.3-4.6 2.1c-1.7-.1-3.3-1.2-4.4-2 .4-.8 1.6-1.5 1.2-1.5-1 .2-4.2.2-5.9-1.5.7 0 2-1.2 2.6-1.8.6-.6 1.7-1.1 1.7-1.1-2.3-.6-4.4-1.5-5.2-2.9 1.5.3 3.7-1.5 4.4-1.9s3-1.2 4-1.2c-.4-.2-3.4-.6-4.6-.7 1.7-1.5 2.8-3 4.2-3.8l3.9-2.7z"
    />
    <linearGradient
      id="benjamin_warrior_svg__SVGID_3_"
      gradientUnits="userSpaceOnUse"
      x1={285.59}
      y1={85.439}
      x2={278.07}
      y2={85.439}
    >
      <stop offset={0} stopColor="#fff" />
      <stop offset={0.18} stopColor="#edeae8" />
      <stop offset={0.333} stopColor="#ddd9d6" />
      <stop offset={1} stopColor="#d0d2d3" />
      <stop offset={1} stopColor="#a7a9ac" />
    </linearGradient>
    <path fill="url(#benjamin_warrior_svg__SVGID_3_)" d="M278.1 51.2l1.4 77.4h5.4V42.3z" />
    <linearGradient
      id="benjamin_warrior_svg__SVGID_4_"
      gradientUnits="userSpaceOnUse"
      x1={284.935}
      y1={85.438}
      x2={307.848}
      y2={85.438}
    >
      <stop offset={0} stopColor="#e5e2df" />
      <stop offset={0} stopColor="#ddd9d6" />
      <stop offset={0.058} stopColor="#cec9c8" />
      <stop offset={0.169} stopColor="#aaa5a7" />
      <stop offset={0.264} stopColor="#8b878c" />
      <stop offset={0.522} stopColor="#f2f3f3" />
      <stop offset={0.584} stopColor="#e0e0e0" />
      <stop offset={0.702} stopColor="#b8b6b8" />
      <stop offset={0.863} stopColor="#848184" />
      <stop offset={0.876} stopColor="#807d81" />
      <stop offset={1} stopColor="#d0d2d3" />
      <stop offset={1} stopColor="#a7a9ac" />
    </linearGradient>
    <path fill="url(#benjamin_warrior_svg__SVGID_4_)" d="M289.8 128.6l2-77.4-6.9-8.9v86.3z" />
    <path
      d="M272.5 134.3h24.8c.6 0 1.2-.5 1.2-1.2v-3.6c0-.6-.5-1.2-1.2-1.2h-24.8c-.6 0-1.2.5-1.2 1.2v3.6c0 .7.6 1.2 1.2 1.2z"
      fill="#231f20"
    />
  </svg>
);

export default SvgBenjaminWarrior;
