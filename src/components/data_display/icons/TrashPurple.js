import React from 'react';

const SvgTrashPurple = props => (
  <svg
    id="trash_purple_svg__Layer_1"
    x={0}
    y={0}
    viewBox="0 0 12 14.8"
    xmlSpace="preserve"
    enableBackground="new 0 0 12 14.8"
    {...props}
  >
    <style>{'.trash_purple_svg__st1{fill-rule:evenodd;clip-rule:evenodd;fill:#fff}'}</style>
    <g id="trash_purple_svg__icon-_x2F_-Trash-Can" transform="translate(31 1)">
      <g id="trash_purple_svg__Group-7">
        <g id="trash_purple_svg__Group-3" transform="translate(.358 2.56)">
          <defs>
            <filter
              id="trash_purple_svg__Adobe_OpacityMaskFilter"
              filterUnits="userSpaceOnUse"
              x={-31}
              y={-1.1}
              width={11.4}
              height={12.3}
            >
              <feColorMatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0" />
            </filter>
          </defs>
          <mask maskUnits="userSpaceOnUse" x={-31} y={-1.1} width={11.4} height={12.3} id="trash_purple_svg__mask-2_1_">
            <g filter="url(#trash_purple_svg__Adobe_OpacityMaskFilter)">
              <path id="trash_purple_svg__path-1_1_" className="trash_purple_svg__st1" d="M-31-1.1h11.4v12.4H-31z" />
            </g>
          </mask>
          <path
            id="trash_purple_svg__Fill-1"
            d="M-22.3 9.6c0 .3-.3.6-.6.5-.3 0-.5-.3-.5-.6l.6-8.6c0-.3.3-.6.6-.5.3 0 .5.3.5.6l-.6 8.6zm-2.3 0c0 .3-.3.6-.6.6s-.6-.3-.6-.6V1c0-.3.3-.6.6-.6s.6.3.6.6v8.6zm-3.1.5c-.3 0-.6-.2-.6-.5l-.6-8.6c0-.3.2-.6.5-.6s.6.2.6.5l.6 8.6c0 .3-.2.6-.5.6zM-31-1.1l1.3 11.6c0 .*******.8h7.2c.4 0 .8-.3.8-.8l1.2-11.6H-31z"
            mask="url(#trash_purple_svg__mask-2_1_)"
            fillRule="evenodd"
            clipRule="evenodd"
            fill="#2c1851"
          />
        </g>
        <g id="trash_purple_svg__Group-6" transform="translate(0 .181)">
          <defs>
            <filter
              id="trash_purple_svg__Adobe_OpacityMaskFilter_1_"
              filterUnits="userSpaceOnUse"
              x={-31}
              y={-1.2}
              width={12}
              height={2.1}
            >
              <feColorMatrix values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0" />
            </filter>
          </defs>
          <mask maskUnits="userSpaceOnUse" x={-31} y={-1.2} width={12} height={2.1} id="trash_purple_svg__mask-4_1_">
            <g filter="url(#trash_purple_svg__Adobe_OpacityMaskFilter_1_)">
              <path id="trash_purple_svg__path-3_1_" className="trash_purple_svg__st1" d="M-31-1.2h12V.9h-12z" />
            </g>
          </mask>
          <path
            id="trash_purple_svg__Fill-4"
            d="M-19.5-.2H-22v-.1c0-.4-.3-.8-.8-.8h-4.6c-.4 0-.8.4-.8.8v.1h-2.5c-.1 0-.3.2-.3.4v.3c0 .*******.5h11.1c.2 0 .4-.2.4-.5V.2c.1-.2-.1-.4-.4-.4"
            mask="url(#trash_purple_svg__mask-4_1_)"
            fillRule="evenodd"
            clipRule="evenodd"
            fill="#2c1851"
          />
        </g>
      </g>
    </g>
  </svg>
);

export default SvgTrashPurple;
