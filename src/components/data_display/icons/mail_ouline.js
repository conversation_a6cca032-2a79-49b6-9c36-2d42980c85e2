import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles(theme => ({
  root: {
    "& :hover": {}
  }
}));

function Mail_Outline_Icon(props) {
  function styledIcon(props) {
    const classes = makeStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={classes.root}>
        <SvgIcon
          {...props}
          viewBox="0 0 30 23"
          
        >
          <g fill={getColor(props, "stroke")} fillRule="evenodd">
            <path
                d="M1.52 22.08C.68 22.08 0 21.4 0 20.56V1.52C0 .68.68 0 1.52 0h26.96C29.32 0 30 .68 30 1.52v19.04a1.52 1.52 0 01-1.52 1.52H1.52zm26.694-3.13V3.44l-7.73 7.596 7.73 7.913zM1.64 3.31v15.646l7.88-8.044L1.64 3.31zm25.076 17.163c-2.628-2.687-5.185-5.305-7.739-7.917-.49.483-.939.922-1.386 1.364-1.5 1.491-3.72 1.49-5.215-.01-.462-.465-.914-.94-1.396-1.438l-7.829 8.001h23.565zM3.173 1.518c.173.19.276.31.385.42 3.403 3.45 6.807 6.898 10.212 10.346.807.817 1.62.821 2.424.012 3.437-3.46 6.874-6.923 10.31-10.386.106-.107.2-.226.346-.392H3.173z"
            />
          </g>
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_Mail_Icon = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {}
    }
  })(styledIcon);
  return (
    <>
      <Custom_Mail_Icon {...props} />
    </>
  );
}

export default Mail_Outline_Icon;
