import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles(theme => ({
  root: {
    "& :hover": {}
  }
}));
const onHandleOnclick = props => {
  if (props.redirect && props.url) {
    // if (props.url.includes("http")) {
    //   window.open(props.url);
    // } else {
    //   window.open(`${props.url}`);
    // }
    window.open(`https://youtube.com/c${props.url}`);
  }
};
function Youtube_Color_Icon(props) {
  function styledIcon(props) {
    // const classes = useStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={""}>
        <SvgIcon
          {...props}
          viewBox="0 0 34 34"
          onClick={() => onHandleOnclick(props)}
        >
          <g fill="none" fillRule="evenodd">
            <path
            d="M34 25.499A8.5 8.5 0 0125.499 34h-17A8.499 8.499 0 010 25.499V8.5C0 3.805 3.803 0 8.499 0h17A8.5 8.5 0 0134 8.5v16.999z"
            fill="#DF2C26"
            />
            <path
            d="M17.506 9h-.01s-4.409 0-7.347.23c-.41.052-1.304.057-2.104.962-.63.69-.834 2.26-.834 2.26S7 14.291 7 16.132v1.726c0 1.842.21 3.683.21 3.683s.206 1.567.835 2.257c.8.907 1.85.876 2.315.972 1.681.174 7.14.229 7.14.229s4.413-.01 7.353-.237c.409-.053 1.305-.057 2.103-.964.628-.69.833-2.257.833-2.257S28 19.7 28 17.859v-1.726c0-1.84-.21-3.682-.21-3.682s-.206-1.568-.834-2.26c-.798-.904-1.694-.91-2.103-.962C21.913 9 17.506 9 17.506 9"
            fill="#FFF"
            />
            <path fill="#DF2C26" d="M16.002 20L22 17.011 16 14z" />
        </g>
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_Youtube_colored_Icon = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {}
    }
  })(styledIcon);
  return (
    <>
      <Custom_Youtube_colored_Icon {...props} />
    </>
  );
}

export default Youtube_Color_Icon;
