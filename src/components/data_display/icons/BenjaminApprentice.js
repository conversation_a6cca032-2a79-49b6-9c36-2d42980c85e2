import React from 'react';

const SvgBenjaminApprentice = props => (
  <svg id="benjamin_apprentice_svg__Layer_1" x={0} y={0} viewBox="0 0 299.9 191.4" xmlSpace="preserve" {...props}>
    <style>
      {
        '.benjamin_apprentice_svg__st9{fill:#99c05c}.benjamin_apprentice_svg__st10{fill-rule:evenodd;clip-rule:evenodd;fill:#231f20}.benjamin_apprentice_svg__st12{fill:#fff}'
      }
    </style>
    <path
      className="benjamin_apprentice_svg__st9"
      d="M252 71.5c-.2 3.1.3 5.5 3.2 7.2 2.9 1.7 6.1-.2 8-1.7 1.8-1.5 3 .9.7 2.8s-6.9 3.3-11.6 1.2c-2.8-1.3-4.2-6.9-4.3-9.3-.1-2.2-1.8-4.8-3.4-6.4l-3.3-3.3c-.6-.6-.7-1.3-.6-1.7 0-.4.1-1.1.8-1.6.5-.4 1.1-.5 1.5-.5.6 0 1.2.2 1.6.7l5 5.1s1.3 1.2 1.7 2.4c.4 1 1 2.1.7 5.1zM147.2 142c1 .3 2.1.7 3.2 1.1 5.3 2 12.4 6.5 19.3 ******** 1.7 1 2.5 1.5-10 8.1-15.8 5.7-19.8 8.7-4.1 3-4.2 7.8-.4 12 3.9 4.2 8.7 6.7 14.6 8.1 3.5.8 7.5.9 10.2.9 1 0 5.7 1.1 8.7 6.2H136s2.2-2.8 4.8-4.2c-1-11.1-4.3-17.9-8-23.2 7-5.5 12.2-13.1 14.4-21.9zm15.9-29.4c-5.1-9.3-10.7-12.1-15.5-13.5-4.8-1.5-12.1-.5-27.2-1.2-9.6-.4-16.8-4.2-21.8-8.1-1.7-1.4-.5-4.1 1.7-3.6 7.7 1.7 18 2.7 26.2 2.6 0 0-14.2-4.1-23.3-7.6-6.1-2.3-10-6.5-12.6-11.2-1-1.8 1-3.8 2.8-2.8 9.5 5.5 23.5 10 29.4 10-15.8-6.3-28.2-18.8-30.2-21.6-1-1.3-1.7-3.2-2-4.9-.4-1.9 1.9-3.2 3.3-1.9 6.9 5.9 18.3 12.9 30.6 17.2-11.7-5.7-23.8-20.6-26.9-25.2-1.4-2-2.3-5.2-2.9-8.2-.4-2.1 2.2-3.3 3.5-1.8 8 9 20.3 19.8 29.9 24.7-18.3-16.3-22-26.4-23.9-32.5-.6-1.9-.9-4.2-1.1-6.5-.2-2.2 2.8-3.1 3.8-1.1.8 1.5 1.8 3.2 3 5.1 6.4 10.7 13.9 17.1 25.3 26.4.6.4-13.6-18.8-18.2-28.3-2.7-5.6-3-11.5-2.6-16.8.1-2.2 3.1-2.7 3.9-.7 4 9.6 11.7 23.9 22.2 35.2 15 16.3 30.1 27.3 35.2 31.4 5.1 4.1 6.9 8.8 6.1 13.5-.5 3.5-2.4 8-5.1 12.7-.6 1-.6 3.8 0 5.8 2.9 10.1 10 14.1 19.1 16.3 9.7 2.3 16.5-.9 18.8-6.3 2.3-5.4-4.9-10.9-14.4-19.3s-10.3-17.8-10-28.6c.3-10.8 5.2-20.9 19.4-29.8.3-.1-3.1-3.5-5.7-6.4 2.1 0 10.4.5 20.5 3l-3.4-6.5c2.5.3 10.4 2.3 16.8 5.9l.7-5s13.1 9.9 18.9 16.8c3.5 4.2 7 10.4 9.1 14.6 1.3 2.5.6 5.6-1.7 7.3l-3.3 2.5c-2 1.5-4.8 1.3-6.6-.5l-7.7-7.9c-2.2-2.3-5.9-2.5-8.3-.4-2.7 2.3-2.9 6.4-.4 8.9 1.1 1.1 2.3 2.3 3.3 3.4 1.7 1.7 1.7 4.4 0 6.1-1.7 1.7-4.5 1.6-6.2-.1-2.6-2.7-6-6.4-6.9-7.3-1.5-1.5-5.7-6.3-3.1-14.3-11.6 16 6 24.3 19.7 36.2 13.7 11.9 15.9 25 13.7 37.4-2.2 12.4-12.8 27-27.2 33.4-5.9 2.6-11.9 3.9-17.6 4.4-1.3 7.2 1.5 12.2 6.2 16.4 0 0 7.3 4.9 15.3 5.1 3.8.1 10.2.8 13.1 6.1h-48.4c.5-1.9 2.3-3.7 4.4-4.1.5-7.1 2-11.7-11.3-36.3 6.5.9 15-3.4 19-11.8-10.1 6.7-14.5 9.1-24.3 7 1.6 4.6 3.2 8.9 4.4 15.5-10.2-2.7-29-17.8-41-22.3-2.7-1-5.1-1.8-7.5-2.4-2.5 18.3-18.8 32.4-38.5 32.4-2.4.2-4.8-.1-6 0-4 1-5 2.9-3.5 8.9s21.6 6.9 24.8 7.2c3.2.2 6.7 3 7.5 6H78.9s3.1-1.8 4.3-4.6c1.2-2.8.6-5.7-.9-8.5-3-6.5-3.7-13.8-3.7-13.8 0-4.2.9-8 2.4-11.3 2.8 2.4 6.9 2.7 11.3 2.9 12.3 0 22.5-9.4 23.8-21.3h-2.8c-20.4.2-34.1 11.1-39.4 18.1-5.3 7.1-16.1 24.6-33.8 29-17.7 4.4-31-3.5-37.4-16.8-6.4-13.3.2-28.3 5.3-34.3 5.1-6 26.8-15.5 31.2-21 4.4-5.5 1.5-15.7-5.8-22.3l-4 10.4-13-33.6 32.1 16.4s-8.8 3.8-10.8 4.4c4.9 3.5 9.7 10.4 9.7 19.2s-8.2 16.4-20.1 22.6c-12 6.2-18.2 13.3-17.4 23.9.9 10.6 7.3 18.8 19 19.9s19.3-7.3 19.3-7.3c5.9-6.3 15.3-22.8 27.7-34.9s26.6-18.1 37.9-21.9c11.3-3.8 21.2-5 27.5-4.2 7.8.9 15 5.3 21.8 10.4z"
    />
    <path
      className="benjamin_apprentice_svg__st9"
      d="M255.5 142c-3.3 7.1-14.4 15.4-14.4 15.4 8.1-1.2 21-6.9 34.2 2.5.4.3 1 .3 1.5.1 4.2-1.4 9.7-2.1 12.5-1.9.5 0 1.1-.2 1.3-.6 2-3.5 5.3-7.5 5.3-7.5l3.9-3.2c.2-4.3-1.2-8.7-4.1-10.3-.4-.7-.7-1.4-1.9-3.2-.3 1.6.1 3.5-.1 4-2.5 2.3-16-.8-16-.8.1-2.3-3.1-5-5.1-6.4.9 3.5-.3 4.4 0 6.3-1.6.8-4.2 2.6-4.3 6.9-5.1 2.1-12.8-1.3-12.8-1.3z"
    />
    <path
      className="benjamin_apprentice_svg__st10"
      d="M285 151c-7.3 0-13.2-5.9-13.2-13.2v-3.3c0-7.3 5.9-13.2 13.2-13.2 7.3 0 13.2 5.9 13.2 13.2v3.3c0 7.3-5.9 13.2-13.2 13.2z"
    />
    <path
      d="M285.5 149.9v-2.7h-1.6v2.8h1.6zm3.7-.6v-2.8c-.5.2-1 .3-1.6.4v2.3h1.6zm-7 0V147c-.5-.1-1.1-.3-1.6-.5v2.8h1.6zm10-1.8v-2.6c-.5.4-1 .7-1.6.9v1.6h1.6zm-13 0v-1.7c-.6-.3-1.1-.6-1.6-1v2.7h1.6zm5.8-2.1c6 0 10.8-4.8 10.8-10.8 0-6-4.8-10.8-10.8-10.8s-10.8 4.8-10.8 10.8c0 6 4.8 10.8 10.8 10.8zm10.2-.5V142c-.4.5-.8 1-1.3 1.5v1.4h1.3zm-19.4 0v-1.6c-.4-.4-.8-.9-1.1-1.4h-.1v3h1.2zm21-3.8v-2c-.2.6-.5 1.2-.8 1.7v.3h.8zm-23 0v-.6c-.3-.6-.6-1.2-.8-1.9v2.5h.8z"
      fillRule="evenodd"
      clipRule="evenodd"
      fill="#fff"
    />
    <path
      className="benjamin_apprentice_svg__st10"
      d="M281.9 140.5l2.7-1.7c.2-.1.6-.1.8 0l2.6 1.7c1.3.8 2 .3 1.7-1.2l-.8-3.1c-.1-.2 0-.6.2-.7l2.4-2c1.2-1 .9-1.8-.6-1.9l-3.2-.2c-.3 0-.5-.2-.6-.5L286 128c-.6-1.4-1.5-1.4-2 0l-1.2 3c-.1.2-.4.4-.6.5l-3.1.2c-1.5.1-1.8 1-.6 1.9l2.5 2.1c.*******.2.7l-.8 3c-.5 1.3.2 1.9 1.5 1.1"
    />
    <path
      className="benjamin_apprentice_svg__st9"
      d="M270.9 149.2c3.3-2.9 7.4-2.1 10-.9 2.6 1.2 5.2 3.8 *******-.4 1-1 1.5-1.9-.6-.7-.4-3 .3-4.1.8-1.3 2.5-1.6 3.2-2 .7-1.3.1-3.3.1-3.3 1.9 2.8 2.8 7.3 2.8 7.3-.6 1.9-2.9 3.2-3.2 3.9 0 0-5.1 5-5.3 5-.2 0-10.7-1-10.7-1l-8.2-3.9z"
    />
    <g>
      <path
        className="benjamin_apprentice_svg__st12"
        d="M270.8 149.3c1.3-1.2 3-2 4.8-2.2 1.8-.2 3.6.2 5.2.9s3.1 1.8 4.7 2.1c1.6.4 3.3-.3 4.7-1.2.6-.5 1-1.1 1.4-1.8v.4c-.5-.8-.5-1.5-.4-2.3.1-.7.2-1.4.6-2.1.4-.7 1-1.2 1.6-1.6.6-.4 1.3-.6 1.8-.9l-.1.1c.4-.9.4-2.1.1-3.1l-.4-1.9 1 1.7c.7 1.2 1.2 2.4 1.7 3.7.4 1.3.8 2.6 1 3.9-.3.9-.7 1.6-1.2 2.4l-.7 1.1c-.2.4-.5.7-.7 1.2.1-.4.4-.8.6-1.2l.6-1.2c.4-.8.8-1.6 1-2.4v.1c-.7-2.6-1.5-5.1-2.9-7.3l.6-.3c.2.6.2 1.2.2 1.8 0 .6-.1 1.2-.4 1.8v.1h-.1c-.7.4-1.3.6-1.9.9-.6.3-1 .7-1.3 1.3-.3.5-.4 1.2-.5 1.8 0 .6 0 1.4.3 1.7l.2.2-.1.2c-.4.7-.9 1.5-1.6 2-.8.5-1.6.9-2.5 1.2-.9.3-1.9.3-2.8.1-1.8-.4-3.3-1.6-4.8-2.3-1.6-.7-3.3-1.2-5-1.1-1.7.4-3.4 1.1-4.7 2.2z"
      />
    </g>
    <path
      className="benjamin_apprentice_svg__st12"
      d="M252.2 43.4c.5.1.8-.4.6-.9-.9-2.2-2.6-4.3-4.9-5.9-1-.7-2.4-1.1-3.2-1.9.3 1.9.5 2.9.9 3.8.6 1.4 1.7 2.7 3.3 3.6 1.1.7 2.1 1.2 3.3 1.3z"
    />
  </svg>
);

export default SvgBenjaminApprentice;
