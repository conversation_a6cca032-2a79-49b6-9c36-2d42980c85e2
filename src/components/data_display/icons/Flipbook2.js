import React from 'react';

const SvgFlipbook2 = props => (
  <svg width={65} height={26} {...props}>
    <defs>
      <path id="flipbook_2_svg__a" d="M0 20.215V.086h21.449v20.13z" />
      <path id="flipbook_2_svg__c" d="M51.164 21.65V.187H.137V21.65h51.027z" />
    </defs>
    <g fill="none" fillRule="evenodd">
      <mask id="flipbook_2_svg__b" fill="#fff">
        <use xlinkHref="#flipbook_2_svg__a" />
      </mask>
      <path
        d="M13.055 10.763H9.8l1.966-8.515c1.608.026 7.66.078 8.128.078.807 0 1.372-.756 1.516-1.38.09-.391.016-.86-.166-.86-.078 0-.256.209-.777.209-.547 0-7.587-.053-9.02-.053C5.197.242 1.35 2.925.195 7.924c-.637 2.76.436 4.766 1.065 5.313.*************.36.13.6 0 1.45-.86 1.606-1.537.06-.26-.003-.442-.307-.364-.52-.677-1.081-1.745-.667-3.542.944-4.088 3.77-5.39 7.47-5.624L7.77 10.763H5.087c-.26 0-.444.234-.51.52-.127.547.09 1.303.637 1.303h2.134l-.944 4.088c-.307 1.328-.647 1.9-1.116 1.9-.26 0-.395-.207-.473.131-.114.495.693 1.51 1.344 1.51.86 0 1.669-.911 2.198-3.203l1.022-4.426h3.02c.6 0 1.601-2.422.656-1.823"
        fill="#231F20"
        mask="url(#flipbook_2_svg__b)"
      />
      <g transform="translate(12.99 3.954)">
        <mask id="flipbook_2_svg__d" fill="#fff">
          <use xlinkHref="#flipbook_2_svg__c" />
        </mask>
        <path
          d="M33.819 12.79c-.404 1.079-1.325 1.561-1.909.708-.583-.853-.213-2.358.012-2.908.224-.55.666-1.495 1.414-1.403 ************ 2.623.483 3.604zm-6.882 0c-.404 1.079-1.325 1.561-1.908.708-.584-.853-.213-2.358.01-2.908.226-.55.668-1.495 1.416-1.403 ************ 2.623.482 3.604zm-7.544.55c-.438.708-1.347.82-2.38.495 0 0 .18-1.763.258-2.369.079-.606.584-1.302.91-1.56.325-.259 1.538-.64 1.818.213.281.853-.168 2.515-.606 3.222zm-1.066-9.53c.1-.404.482-2.133 1.055-2.1.572.034.551.71.202 1.965-.36 1.291-.932 2.47-2.301 4.648.325-1.841.943-4.11 1.044-4.513zm-7.129 9.329c.112-2.055.539-3.581 1.224-3.93.684-.347 1.287.138 1.032 1.168-.437 1.774-1.874 2.56-2.256 2.762zm-1.956 6.27c-.118.761-.98.338-.913-.422.086-.96.558-2.62 1.167-3.736-.101.591-.135 3.398-.254 4.158zM2.094 9.165c.146-2.212 1.212-5.299 1.358-5.68.13-.341.529-1.067.94-1.234a.137.137 0 01.185.108c.051.388-.115 1.138-.272 1.776-.18.73-.988 3.043-2.211 5.03zm37.68-4.768c.304-1.166.98-2.282 1.487-2.35.508-.067.136 1.116-.169 1.843-.304.727-.876 3.093-2.197 6.12.152-1.708.574-4.446.879-5.613zm11.39 6.372l-.218-4.578-3.815 2.641L48.76 9c-.726 1.191-2.087 3.262-3.358 4.323-1.116.933-2.225 1.493-3.033.967-.225-.147-.932-1.144-.458-1.997.672-1.207 1.749-1.264 2.376-2.016.455-.544.62-1.273.236-2.025-.615-1.207-1.73-.624-2.398-.183-1.214.802-2.628 2.728-2.628 2.728.718-1.17 2.186-3.34 2.778-5.098.591-1.758 1.538-5.14-.22-5.36-1.674-.219-2.756 1.1-3.432 2.993-.46 1.287-1.012 3.613-1.298 6.096a8.155 8.155 0 00-.914-.836c-.573-.449-2.279-1.302-3.738-.83-1.098.354-1.853 1.008-2.25 1.644a8.168 8.168 0 00-.893-.814c-.573-.449-2.28-1.302-3.739-.83-1.46.471-2.313 1.47-2.526 2.256-.227.837-.335 1.39-.319 1.97-.494.362-1.126.786-1.751 1.113.186-.374.333-.726.42-.989.259-.791.58-2.972-.753-3.997-1.234-.949-3.042 1.286-3.042 1.286 1.7-2.064 3.405-4.634 3.522-6.165.117-1.531-.26-2.738-1.13-2.946-.869-.207-1.829-.337-2.88 2.064-1.051 2.4-1.882 9.577-1.96 10.731-.986.636-2.647.896-3.192.857-.064-.013-.026-.169-.026-.169 1.545-.766 2.674-1.596 2.907-3.296.234-1.7-.675-3.036-2.362-2.88-1.687.155-2.374.648-3.244 4.58-1.505 1.35-2.66 2.336-3.062 1.389-.403-.948.934-4.425 1.038-4.88.104-.454.233-.973-.13-1.025-.363-.052-1.272.026-1.505.35-.234.325-.26.377-.364.74-.103.364-.77 2.888-.921 3.738-.104.583-1.531 1.115-1.765 1.115-.233 0-.35-.077-.454-.454-.104-.376-.09-1.22.208-1.83.298-.61 1.933-3.295 2.686-5.021.752-1.726 1.726-4.322.856-5.541-.869-1.22-2.725.454-3.101.973C2.566 2.25.606 5.13.23 9.244c-.377 4.114.48 5.178 1.012 5.801.532.623 1.168.402 1.48.324.31-.078 1.414-.428 1.738-.675.286.636 1.246.922 1.947.714.7-.207 1.803-.675 2.193-.999-1 1.856-1.752 3.27-1.765 4.58-.013 1.312.39 2.648 1.622 2.661 1.233.013 2.206-1.804 2.57-3.218.363-1.415.389-3.335.389-3.335 1.194.233 2.452.765 4.295-.403 1.233.831 3.231.974 4.036.39a2.57 2.57 0 00.348-.314c1.103-.194 2.326-.724 3.116-1.254.024.092.05.186.078.285.46 1.64 2.402 1.976 3.558 1.347 1.157-.628 1.965-2.178 1.965-3.648 0-1.47-.539-2.358-.539-2.358.45.3 1.254.766 1.751 1.354-.3 1.233-.264 1.844.146 3.305.46 1.64 2.403 1.976 3.559 1.347 1.156-.628 1.965-2.178 1.965-3.648 0-1.47-.54-2.358-.54-2.358.54.36 1.595.954 2.01 1.718l.03-.017c-.048.71-.09 1.42-.057 2.109.084 1.825.185 2.383.743 2.502.558.118 1.136.168 1.894-1.995.588-1.68 1.159-4.26 2.035-4.533 1.668-.52 1.414 1.606.588 2.135-1.244.798-2.035 1.166-2.238 2.001-.204.836.185 1.512.574 1.905.653.66 1.652.854 2.47.854.44 0 .828-.056 1.081-.126 2.864-.793 4.428-3.385 5.763-6.057l1.115 1.14-.004-.019.006.01z"
          fill="#231F20"
          mask="url(#flipbook_2_svg__d)"
        />
      </g>
      <circle fill="#000" cx={20.639} cy={8.398} r={1} />
    </g>
  </svg>
);

export default SvgFlipbook2;
