import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles((theme) => ({
  root: {
    "& :hover": {},
  },
}));
const onHandleOnclick = (props) => {
  if (props.redirect && props.url) {
    // if (props.url.includes("http")) {
    //   window.open(props.url);
    // } else {
    //   window.open(`${props.url}`);
    // }
    window.open(`https://facebook.com${props.url}`);
  }
};
function Facebook_Color_Icon(props) {
  function styledIcon(props) {
    // const classes = useStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={""}>
        <SvgIcon
          {...props}
          viewBox="0 0 30 30"
          onClick={() => onHandleOnclick(props)}
        >
          <g fill="none" fillRule="evenodd">
            <path
              d="M30,22.5 C30,26.6415 26.643,30 22.4985,30 L7.4985,30 C3.357,30 0,26.6415 0,22.5 L0,7.5 C0,3.357 3.357,0 7.4985,0 L22.4985,0 C26.643,0 30,3.357 30,7.5 L30,22.5 Z"
              id="Fill-1"
              fill="#3C5B9B"
            ></path>
            <path
              d="M12.4995,24.375 L16.2495,24.375 L16.2495,15 L19.032,15 L19.3755,11.25 L16.395,11.25 L16.395,9.75 C16.395,9.021 16.8825,8.8485 17.223,8.8485 L19.3275,8.8485 L19.3275,5.637 L16.431,5.625 C13.2165,5.625 12.4845,8.022 12.4845,9.555 L12.4845,11.25 L10.6245,11.25 L10.6245,15 L12.4995,15 L12.4995,24.375 Z"
              id="Fill-3"
              fill="#FFFFFF"
            ></path>
          </g>
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_Facebook_colored_Icon = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {},
    },
  })(styledIcon);
  return (
    <>
      <Custom_Facebook_colored_Icon {...props} />
    </>
  );
}

export default Facebook_Color_Icon;
