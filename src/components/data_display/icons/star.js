import React from 'react';
import SvgIcon from '@material-ui/core/SvgIcon';
import { makeStyles, withStyles } from '@material-ui/core/styles';

import { color } from '../../../utilities/themes';

const useStyles = makeStyles(theme => ({
  root: {
    '& :hover': {},
  },
}));


function Star(props) {
  const classes = useStyles();
  function styledIcon(props) {
    return (
      <span className={classes.root}>
        <SvgIcon viewBox="0 0 62 93.02" {...props}>
        <path
            d="M28.66 4.59c0 22.2 20.81 38.13 20.81 38.13-17.37 2.9-20.81 30.89-20.81 39.58 0-8.69-3.44-36.68-20.81-39.58 0 0 20.81-15.93 20.81-38.13z"
            fill="#422063"
        />
        </SvgIcon>
      </span>
    );
  }
  /**
   * Custom styling the colors
   */
  const Custom_Star = withStyles({
    root: {
      '& > svg': {
        color: props.color ? props.color : color.secondary_palette.grays.light_gray,
      },
      '& :hover': {},
    },
  })(styledIcon);
  return (
    <>
      <Custom_Star {...props} />
    </>
  );
}

export default Star;
