import React from 'react';

const SvgUserIcon = props => (
  <svg width={59} height={80} {...props}>
    <defs>
      <path id="user_icon_svg__a" d="M0 0h58.51v79.787H0z" />
    </defs>
    <g fill="none" fillRule="evenodd" opacity={0.2}>
      <mask id="user_icon_svg__b" fill="#fff">
        <use xlinkHref="#user_icon_svg__a" />
      </mask>
      <path
        d="M35.904 26.596a2.66 2.66 0 110 5.319 2.66 2.66 0 010-5.32zm-13.298 0a2.66 2.66 0 110 5.319 2.66 2.66 0 010-5.32zM34.078 39.6l-1.667-2.072a5.346 5.346 0 01-6.313 0L24.434 39.6a7.942 7.942 0 009.644 0zm10.585 20.24l-3.05 3.05c-.519.52-.519 1.36 0 1.88l3.05 3.05-4.77 4.768V55.071l4.77 4.77zm-7.43-3.989a11.83 11.83 0 01-7.977 2.66 11.866 11.866 0 01-7.979-2.66v-3.745a6.66 6.66 0 002.659-5.31v-.628a11.474 11.474 0 005.32 1.704 11.672 11.672 0 005.319-1.72v.645a6.67 6.67 0 002.659 5.313v3.741zm-19.946-23.67v-8.245h23.936v8.244c0 7.001-8.028 13.033-11.967 13.033-1.576 0-4.563-1.208-7.4-3.856-1.706-1.606-4.569-4.936-4.569-9.177zm-5.319-10.904a1.33 1.33 0 010-2.66h34.575a1.33 1.33 0 010 2.66H11.968zm11.968-16.27v8.29h2.66V2.66h5.32v10.638h2.659V4.993a14.67 14.67 0 019.054 10.964H14.885a14.659 14.659 0 019.05-10.95zm-5.319 67.581l-4.769-4.768 3.05-3.05c.519-.52.519-1.36 0-1.88l-3.05-3.05 4.77-4.769v17.517zm32.156-17.92l-.242-.091v-.226a2.812 2.812 0 01-1.027-.506 2.892 2.892 0 01-.91-.24 2.98 2.98 0 01-.721-.224v.19l-8.004-3.021a4.003 4.003 0 01-2.635-3.753v-2.433a17.234 17.234 0 006.046-8.597c.006-.025 0-.045.01-.07.096-.276.167-.56.247-.84.024-.094.057-.187.08-.283h.003c.138-.53.251-1.064.314-1.608.04-.347-.029-1.051-.051-1.051v-7.979h2.66a3.989 3.989 0 000-7.979h-.229a17.305 17.305 0 00-11.74-13.764V1.33A1.33 1.33 0 0033.245 0h-7.978a1.33 1.33 0 00-1.33 1.33v.868a17.31 17.31 0 00-11.75 13.76h-.218a3.99 3.99 0 000 7.978h2.66v7.979c0 .959.245 2.66.245 2.66.8 3.363 2.6 6.405 5.164 8.725.4.375.818.729 1.24 1.064v2.43a4.007 4.007 0 01-2.66 3.76L10.64 53.57v-.218c-.64.431-1.136.658-1.913.769-.25.036-.5.039-.748.025v.431l-.24.09A12.03 12.03 0 000 65.863v13.924h2.66V65.863a9.356 9.356 0 016.018-8.707l6.58-2.486-4.23 4.23c-.52.52-.52 1.361 0 1.88l3.049 3.05-3.048 3.05c-.52.518-.52 1.36 0 1.88l7.588 7.588v3.44h2.66v-20.63a15.708 15.708 0 007.979 2.012 15.78 15.78 0 007.978-1.995v20.612h2.66v-3.439l7.588-7.589c.52-.519.52-1.36 0-1.88l-3.05-3.05 3.05-3.049c.52-.519.52-1.36 0-1.88l-4.229-4.23 6.579 2.486a9.353 9.353 0 016.019 8.707v13.924h2.66V65.863a12.028 12.028 0 00-7.738-11.196z"
        fill="#000"
        mask="url(#user_icon_svg__b)"
      />
    </g>
  </svg>
);

export default SvgUserIcon;
