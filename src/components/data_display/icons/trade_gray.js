import React from "react";
import SvgIcon from "@material-ui/core/SvgIcon";
import { makeStyles, withStyles } from "@material-ui/core/styles";

import { color } from "../../../utilities/themes";

const useStyles = makeStyles(theme => ({
  root: {
    "& :hover": {}
  }
}));
const onHandleOnclick = props => {
  if (props.redirect && props.url) window.open(props.url);
};
function Trade_Icon(props) {
  function styledIcon(props) {
    const classes = makeStyles(props);
    const getColor = (props, line_type) => {
      let colorValue = props.color
        ? props.color
        : color.secondary_palette.grays.light_gray;

      if (props && props.color === "primary") {
        if (line_type === "stroke") {
          colorValue = color.primary_palette.franklin_purple;
        }
        if (line_type === "fill") {
          colorValue = color.primary_palette.franklin_purple;
        }
      } else {
      }
      return colorValue;
    };
    return (
      <span className={classes.root}>
        <SvgIcon
          {...props}
          viewBox="0 0 18 20"
          onClick={() => onHandleOnclick(props)}
        >
          <path
            d="M17.051 4.53s0-2.073-2.006-2.278C12.176 1.96 13.663 0 10.522 0H6.53c-3.14 0-1.654 1.96-4.523 2.252C0 2.458 0 4.53 0 4.53h6.643v15.074h3.765V4.53h6.643z"
            fill="#7A7A7A"
            fillRule="evenodd"
        />
        </SvgIcon>
      </span>
    );
  }

  /**
   * Custom styling the colors
   */
  const Custom_Trade_Icon = withStyles({
    root: {
      "& > svg": {},
      "& :hover": {}
    }
  })(styledIcon);
  return (
    <>
      <Custom_Trade_Icon {...props} />
    </>
  );
}

export default Trade_Icon;
