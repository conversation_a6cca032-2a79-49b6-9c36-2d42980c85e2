// import { translate } from './translations/translator';
import moment from "moment";

const path = "/api/v1/";
const TOKEN_KEY = "tradeworks_user_token";
const queryString = window.location.href;
const SERVER_URL = queryString.includes("beta")
  ? "https://tra-cpack-prd.azurewebsites.net"
  : queryString.includes("twwstage") || queryString.includes("localhost")
  ? "https://tra-cpack-stage.azurewebsites.net"
  : "https://tra-cpack-prd.azurewebsites.net";
const SERVER_URL_LOOKUP = queryString.includes("beta")
  ? "https://tra-lookups-prd.azurewebsites.net"
  : queryString.includes("twwstage") || queryString.includes("localhost")
  ? "https://tra-lookups-stage.azurewebsites.net"
  : "https://tra-lookups-prd.azurewebsites.net";
const SPACK_DEV_URL = "tradeworks-spack-dev-fe.azurewebsites.net";
const SPACK_QA_URL = "tradeworks-spack-qa-fe.azurewebsites.net";
const SPACK_STAGE_URL = "tradeworks-spack-stage-fe.azurewebsites.net";
const SERVER_URL_JPACK = queryString.includes("beta")
  ? "https://tra-jpack-prd.azurewebsites.net"
  : queryString.includes("twwstage") || queryString.includes("localhost")
  ? "https://tra-jpack-stage.azurewebsites.net"
  : "https://tra-jpack-prd.azurewebsites.net";
const SERVER_URL_CATS = queryString.includes("beta")
  ? "https://tra-cats-prd.azurewebsites.net"
  : queryString.includes("twwstage") || queryString.includes("localhost")
  ? "https://tra-cats-stage.azurewebsites.net"
  : "https://tra-cats-prd.azurewebsites.net";
const TWITTER_SCRIPT_URL = "https://platform.twitter.com/widgets.js";
const TWITTER_REF_SRC = "twsrc%5Etfw";
const FACEBOOK_PLUGIN = {
  url1: "https://www.facebook.com/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2F",
  url2: "&tabs=timeline&width=270&height=500&small_header=false&adapt_container_width=true&hide_cover=false&show_facepile=true&appId",
};
const FACEBOOK_URL = "https://www.facebook.com/";
const TWITTER_URL = "https://twitter.com/";
const LINKEDIN_URL = "https://www.linkedin.com/company/";
const TWITTER_URL2 = "?ref_src=";
const INSTAGRAM_AUTHENTICATION_URL = {
  P1: "https://api.instagram.com/oauth/authorize?client_id=",
  P2_DEV:
    "&redirect_uri=https://tradeworks-cpack-dev-fe.azurewebsites.net/twc/profile&scope=user_profile,user_media&response_type=code",
  P2_QA:
    "&redirect_uri=https://tradeworks-cpack-qa-fe.azurewebsites.net/twc/profile&scope=user_profile,user_media&response_type=code",
  P2_STAGE:
    "&redirect_uri=https://tradeworks-cpack-stage-fe.azurewebsites.net/twc/profile&scope=user_profile,user_media&response_type=code",
};
const PUBLIC_PROFILE = {
  local: "http://localhost:3002/company/public/",
  dev: "http://tradeworks-cpack-dev-fe.azurewebsites.net/company/public/",
  qa: "http://tradeworks-cpack-qa-fe.azurewebsites.net/company/public/",
  stage: "http://tradeworks-cpack-stage-fe.azurewebsites.net/company/public/",
  twwstage: "http://twwstage.franklinreport.com/cpack/company/public/",
};
const LLAMA_DEV_URL = "tradeworks-llama-dev-fe.azurewebsites.net";
const LLAMA_QA_URL = "tradeworks-llama-qa-fe.azurewebsites.net";
const LLAMA_STAGE_URL = "tradeworks-llama-stage-fe.azurewebsites.net";

const TDW_URL = {
  WPACK: {
    DEV: "https://tradeworks-wpack-dev-fe.azurewebsites.net",
    QA: "https://tradeworks-wpack-qa-fe.azurewebsites.net",
    STAGE: "https://tradeworks-wpack-stage-fe.azurewebsites.net",
  },
};

const CATS_URL = {
  CATS: {
    DEV: "https://tradeworks-cats-dev-fe.azurewebsites.net",
    QA: "https://tradeworks-cats-qa-fe.azurewebsites.net",
    STAGE: "https://tradeworks-cats-stage-fe.azurewebsites.net",
  },
};

const BASEURL = {
  URL: queryString.includes("beta")
    ? "https://beta.thetrade.works/"
    : queryString.includes("twwstage")
    ? "http://twwstage.franklinreport.com/"
    : "https://trade-works.com/",
};

const URI = {
  GET_CONFIG: `${path}get-config-value`,
  UPLOAD: `${path}upload-files`,

  // TRADES API URI
  TRADES: `${path}trade/all`,

  // LINK META DATA
  META_DATA: `${path}get-metaData`,

  // PROFILE
  UPDATE_BUSINESS_CARD: `${path}add-update-business-card`,
  COMPANY_PROFILE_DATA: `${path}company-profile-data`,
  COMPANY_PROFILE: `${path}company-profile`,
  UPDATE_ADD_COMPANY_PITCH: `${path}add-update-company-pitch`,
  UPDATE_WATER_COOLER: `${path}add-update-water-cooler`,
  FETCH_ACCOLADES: `${path}get-accolades`,
  GET_COMPANY_VIBES: `${path}get-company-vibe`,
  GET_COLLEGE_ALLEGIANCES: `${path}get-college-allegiances`,
  GET_CLIENTELE_DATA: `${path}get-clientel`,
  GET_PROCLIVITIES_DATA: `${path}get-design-proclivities`,
  ADD_UPDATE_OFFICE_SHOTS: `${path}add-update-office-shots`,
  ADD_UPDATE_COMPANY_HIGHLIGHTS: `${path}add-update-company-highlight`,
  FETCH_ALL_COMPANIES: `${path}company`,
  FETCH_ALL_COUNTRIES: `${path}get-country-llist`,
  FETCH_FEEDS: `${path}`,
  PING_ADMIN_PUBLIC: `${path}public-ping-administrator`,
  PING_ADMIN_TM: `${path}employee-ping-administrator`,
  GET_GAMIFICATION: `${path}get-gamification-data`,
  SEND_THANKS: `${path}admin-thanks-mail`,
  INSTA_VERIFICATION_CODE: `${path}save-instagram-credentials`,
  GET_INSTA_FEEDS: `${path}get-instagram-feeds`,
  TRIGGER_EMAIL_TM: `${path}email-trigger`,
  EMAIL_FR_ADMIN: `${path}send-email-to-fr-admin`,
  COMPANY_BY_USER: `${path}companies-by-user`,
  COMPANY_NAME_CHANGE: `${path}company-name-change-request`,
  ADD_ME_TO_COMPANY: `${path}add-me-to-company`,
  COMPANY_LIST: `${path}company-list`,
  BASIC_COMPANIES_INFO: `${path}basic-companies-info`,
  OPEN_POSITIONS: `${path}open-poisitions`,
  COMPANY_MEMBERS_BY_ID: `${path}company-members-by-id`,
};

const LOOKUP_URI = {
  JOB_LISTING: `${path}job-listing/`,
  LOCATIONS_DATA: `${path}trade-locations`,
  GET_INTERVIEW_INFO: `${path}get-interview-info`,
  POST_FEEDBACK: `${path}post-feedback`,
  LOGOUT_IMPERSONATE: `${path}logout-inpersonate`,
  COMPANY_STATUS_UPDATE: `${path}company-status-update`,
  CONTACT_US: `${path}contact-us`,
  SEARCH_IN_ALL: `${path}search-in-all`,
};

const JPACK_URI = {
  JOB_DATA: `${path}full-job-details`,
};

const CATS_URI = {
  COMPANY_INTERVIEW_BANNERS: `${path}company-interview-banners`,
};

const ATS_URLS = {
  local: "localhost:3008",
  dev: "tradeworks-ats-dev-fe.azurewebsites.net",
  qa: "tradeworks-ats-qa-fe.azurewebsites.net",
  stage: "tradeworks-ats-stage-fe.azurewebsites.net",
};

const DEBOUNCE_TIME = 700; //ms
const MONTHS = moment.months();
const MAX_INPUT_COUNT = 200;
const MAX_UPLOAD_SIZE = 2 * 1024 * 1024; //   2 MB
const COMPANY_CULTURE_DESC_MINLENGTH = 100;

const PROFILE_TYPES = {
  BUSINESS_CARD: "businessCard",
  COMPANY_PITCH: "companyPitch",
  WATER_COOLER: "waterCooler",
  OFFICE_SHOTS: "officeShots",
  COMPANY_HIGHLIGHTS: "companyHighlights",
  GAMIFICATION: "gamification",
  COMPANY_INFO: "companyInfo",
  INSTAGRAM_FEEDS: "instagramfeeds",
};

const APP_URLS_JPACK = {
  LOCAL: "http://localhost:3006/",
  DEV: "https://tradeworks-jpack-dev-fe.azurewebsites.net/",
  QA: "https://tradeworks-jpack-qa-fe.azurewebsites.net/",
  STAGE: "https://tradeworks-jpack-stage-fe.azurewebsites.net/",
};

const FOOTER_DATA = {
  FOR_JOB_SEEKERS: [
    {
      title: "Search Jobs",
      key: "searchJobs",
    },
    {
      title: "My User Profile",
      key: "userProfile",
    },
    {
      title: "My Job Journey",
      key: "redirectAts",
    },
    {
      title: "My Dream Job",
      key: "userProfile",
    },
  ],
  FOR_EMPLOYERS: [
    {
      title: "Post Jobs",
      key: "postJobs",
    },
    {
      title: "Discover Candidates",
      key: "disCandidates",
    },
    // {
    //   title: "My Company Profile",
    //   key: "",
    // },
    {
      title: "My Company Settings",
      key: "redirectSettings",
    },
    {
      title: "View TW Community",
      key: "viewTWCommunity",
    },
  ],
  MY_ACCOUNT: [
    // {
    //   title: "Sign In/Register",
    //   key: "",
    // },
    {
      title: "Personal Settings",
      key: "redirectSettings",
    },
    {
      title: "Log Out",
      key: "logOut",
    },
  ],
  CONTACT_US: [
    "If you have any questions, please contact Customer Service at 866.960.9100 or",
  ],
};

const FRANKLIN_REPORT_URL = "https://www.franklinreport.com/default.aspx";

export {
  URI,
  SERVER_URL,
  TOKEN_KEY,
  DEBOUNCE_TIME,
  MONTHS,
  MAX_INPUT_COUNT,
  PROFILE_TYPES,
  MAX_UPLOAD_SIZE,
  SPACK_DEV_URL,
  SPACK_QA_URL,
  SPACK_STAGE_URL,
  TWITTER_SCRIPT_URL,
  TWITTER_REF_SRC,
  FACEBOOK_URL,
  TWITTER_URL,
  TWITTER_URL2,
  COMPANY_CULTURE_DESC_MINLENGTH,
  LINKEDIN_URL,
  INSTAGRAM_AUTHENTICATION_URL,
  PUBLIC_PROFILE,
  FACEBOOK_PLUGIN,
  TDW_URL,
  SERVER_URL_JPACK,
  SERVER_URL_CATS,
  SERVER_URL_LOOKUP,
  LLAMA_DEV_URL,
  LLAMA_STAGE_URL,
  LLAMA_QA_URL,
  CATS_URL,
  JPACK_URI,
  CATS_URI,
  APP_URLS_JPACK,
  ATS_URLS,
  LOOKUP_URI,
  FOOTER_DATA,
  FRANKLIN_REPORT_URL,
  BASEURL,
};
