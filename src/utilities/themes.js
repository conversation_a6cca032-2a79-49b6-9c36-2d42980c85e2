//Tradeworks Color Palette
export const color = {
  primary_palette: {
    franklin_purple: "#410166",
    highlight_purple: "#801FB8",
    christmas_red: "#C01F2A",
    pine_green: "#004400",
    fafa_gray: "#FAFAFA",
    black: "#000000",
    white: "#FFFFFF",
    tricks_red: "#c0202a",
    dark_purple: "#415cca",
    border_opacity: "rgba(25,0,40,0.3)",
  },
  secondary_palette: {
    blues: {
      modal_blue: "#FBFDFE",
      tile_blue: "#F7FAFB",
      baby_blue: "#C8DBF1",
      click_blue: "#5E94E0",
      hover_blue: "#C6DDFF",
      business_card_blue: "#e3e9f5",
      action_blue: "#401660",
    },
    grays: {
      fafa_gray: "#FAFAFA",
      fog_gray: "#EFEFEF",
      shadow_gray: "#979797",
      gray: "#808080",
      medium_gray: "#5f5f5f",
      light_gray: "#9B9B9B",
      background_gray: "#d8d8d8",
      drag_text: "#696969",
      bg_gray: "#f7f7f7",
      ping_gray: "#9c9c9c",
      shadowed_steel: "#4B4B4B",
      dim_grey: "#696969",
      dove_grey: "#6d6d6d",
      up_in_smoke: "#6e6e6e",
      dark_souls: "#a3a3a3",
      silver_gray: "#bfbfbf",
      sonic_silver: "#757575",
      border_gray: "#d7d7d7",
      coluded_vision: "#d1d1d1",
      stonewall_grey: "#c1c1c1",
      cape_hope: "#d7d7d7",
      copyright_text: "#9d9ba8",
    },
    purples: {
      basic: "#D6D3E1",
      familiar: "#B9A9C9",
      proficient: "#9B80B0",
      advance: "#7C55697",
      franklin_purple: "#410166",
      ed_purple: "#801fb8",
    },
  },
  accent_colors_and_one_offs: {
    mustard: "#E2C236",
    star_gold: "#FFD000",
    benjamin_green: "#99C05C",
    links_orange: "#F87B1C",
    programmer_notes: "#FF00FF",
  },
  wizard_box_colors: {
    franklin_purple: "#410166",
    pine_green: "#004400",
    benjamin_green: "#99C05C",
    shadow_gray: "#979797",
    wizard_blue: "#4E72B3",
    gray_light: "#313131",
  },
  benjamin_colors: {
    benjamin_green: "#99C05C",
    baby_blue: "#C8DBF1",
    warrior_blue: "#2D459D",
    franklin_purple: "#410166",
  },
  form_colors: {
    textfield_color: "#7e7e7e",
    blueberry_purple: "#57307a",
    royal_purple_1: "#410260",
    gray: "#f9f9f9",
    sucess_color: "#0d802e",
    chip_color: "#ecf3ff",
  },
  greyish_brown: "#4a4a4a",
  finish_color: "#777777",
  placeholder_wc: "#b0b0b0",
  shaded_gray_wc: "#eeeeee",
  light_blue: "#c7dbf4",
  knight_armour: "#5c5c5c",
  shisha_coal: "#3c3c3c",
  kettleman: "#626262",
  red_carpet: "#c01f2a",
  harbor_afternoon: "#e3e9f5",
  liquor_ice: "#0c0101",
  black_oak: "#4e4e4e",
  uniformed_rotten: "#d13b43",
  conversational_lemon: "#fe8f49",
  melodic_chartreuse: "#fdd727",
  menial_cerulean: "#34a853",
  big_tar: "#171717",
  button_hover: "rgba(0, 0, 0, 0.04)",
  placeholder_color: "#808080",
  gamification_green: "#128040",
  warpstone_glow: "#128040",
  super_admin: "#0C61ED",
  profile_parts: {
    logo_cover: "#BF1F2A",
    company_pitch: "#F97B1C",
    water_cooler: "#FFC101",
    our_offices: "#138040",
    company_culture: "#2C67F6",
    team_alumni: "#801FB8",
  },
};

//Sizes for icons
export const size_options = {
  x_small: "0.75rem",
  small: "0.875rem",
  medium: "1.25rem",
  default: "1.5rem",
  large: "1.875em",
  x_large: "3.25rem",
};

// PX to rem conversion on the base of 16px
export const px_to_rem_sizes = {
  size_20: "1.25rem",
  size_1: "0.0625em",
  size_1_3: "0.08125em",
  size_2: "0.125em",
  size_3: "0.1875em",
};

// PX to rem conversion on the base of 16px
export const font_sizes = {
  font_10: "0.625rem",
  font_12: "0.75rem",
  font_12_3: "0.76875em",
  font_13: "0.8125em",
  font_14: "0.875rem",
  font_15: "0.9375rem",
  font_16: "1rem",
  font_17: "1.0625em",
  font_18: "1.125em",
  font_20: "1.25rem",
  font_21: "1.3125em",
  font_22: "1.375rem",
  font_24: "1.5rem",
  font_25: "1.5625em",
  font_30: "1.875em",
  font_35: "2.1875rem",
  font_50: "3.125rem",
};

// Tradeworks material ui theme object
const tradework_theme = {
  palette: {
    primary: {
      light: color.primary_palette.franklin_purple,
      main: color.primary_palette.franklin_purple,
      dark: color.primary_palette.franklin_purple,
      contrastText: color.primary_palette.white,
    },
    secondary: {
      light: color.form_colors.sucess_color,
      main: color.form_colors.sucess_color,
      dark: color.form_colors.sucess_color,
      contrastText: color.primary_palette.white,
    },
    error: {
      light: color.primary_palette.christmas_red,
      main: color.primary_palette.christmas_red,
      dark: color.primary_palette.christmas_red,
      contrastText: color.primary_palette.white,
    },
  },
  // typography: {
  //   fontFamily: [
  //     "gillsans_r",
  //     "gillsans_sb",
  //     "gillsans_light",
  //     "gillsans_bold",
  //     "avenir_black_r",
  //     "avenir_book_r",
  //     "avenir_bold",
  //     "avenir_light",
  //     "avenir_sb",
  //     "avenir_roman",
  //     '"Helvetica Neue"',
  //     "Zapfino_bold",
  //     "Zapfino",
  //     "Pacifico",
  //     "Pacifico_bold",
  //     "ACaslonPro",
  //     "ACaslonPro_bold",
  //     "OptimaLT",
  //     "OptimaLT_bold",
  //     "NeutraText",
  //     "NeutraText_bold"
  //   ].join(","),
  //   h1: {
  //     fontSize: font_sizes.font_50
  //   },
  //   h3: {
  //     fontSize: font_sizes.font_30,
  //     textTransform: "capitalize"
  //   },
  //   h4: {
  //     fontSize: font_sizes.font_24
  //   },
  //   subtitle1: {
  //     fontSize: font_sizes.font_16
  //   }
  // },

  typography: {
    styles: {
      gillsans_sb: {
        fontFamily: "gillsans_sb",
        fontStyle: "normal",
        fontWeight: 600,
      },
      gillsans_bold: {
        fontFamily: "gillsans_bold",
        fontStyle: "normal",
        fontWeight: 900,
      },
      gillsans_r: {
        fontFamily: "gillsans_r",
        fontStyle: "normal",
        fontWeight: 400,
      },
      gillsans_light: {
        fontFamily: "gillsans_light",
        fontStyle: "normal",
        fontWeight: 300,
        // lineHeight: 1.47
      },
      avenir_light: {
        fontFamily: "avenir_light",
        fontStyle: "normal",
        fontWeight: 300,
        lineHeight: 1.47,
      },
      avenir_black_r: {
        fontFamily: "avenir_black_r",
        fontStyle: "normal",
        fontWeight: 900,
        lineHeight: 1.47,
      },
      avenir_book_r: {
        fontFamily: "avenir_book_r",
        fontStyle: "normal",
        fontWeight: 400,
        lineHeight: 1.47,
      },
      avenir_roman: {
        fontFamily: "avenir_roman",
        fontStyle: "normal",
        fontWeight: 300,
        lineHeight: 1.47,
      },
      avenir_sb: {
        fontFamily: "avenir_sb",
        fontStyle: "normal",
        fontWeight: 500,
        lineHeight: 1.47,
      },
      avenir_bold: {
        fontFamily: "avenir_bold",
        fontStyle: "normal",
        fontWeight: "bold",
        lineHeight: 1.47,
      },
      Zapfino: {
        fontFamily: "Zapfino",
        fontStyle: "normal",
        fontWeight: "normal",
      },
      millerdisplay_light: {
        fontFamily: "millerdisplay_light",
        fontStyle: "normal",
        fontWeight: 300,
      },
      Helvetica: {
        fontFamily: "Helvetica",
        fontStyle: "normal",
        fontWeight: "normal",
      },
      StencilStd: {
        fontFamily: "StencilStd",
        fontStyle: "normal",
        fontWeight: "normal",
      },
      OptimaLT: {
        fontFamily: "OptimaLT",
        fontStyle: "normal",
        fontWeight: "normal",
      },
      NeutraText: {
        fontFamily: "NeutraText",
        fontStyle: "normal",
        fontWeight: "normal",
      },
      NeutraText_b: {
        fontFamily: "NeutraText_bold",
        fontStyle: "normal",
        fontWeight: "bold",
      },
    },
    fontFamily: [
      "gillsans_r",
      "gillsans_sb",
      "gillsans_light",
      "gillsans_bold",
      "avenir_black_r",
      "avenir_book_r",
      "avenir_bold",
      "avenir_light",
      "avenir_sb",
      "avenir_roman",
      '"Helvetica Neue"',
      "Zapfino_bold",
      "Zapfino",
      "Pacifico",
      "Pacifico_bold",
      "ACaslonPro",
      "ACaslonPro_bold",
      "OptimaLT",
      "OptimaLT_bold",
      "NeutraText",
      "NeutraText_b",
      "Helvetica",
      "LucidaGrande",
      "American_Typewriter_Medium_BT",
      "millerdisplay_light",
      "Miller_DisplayRoman",
      "StencilStd",
    ].join(","),
    h1: {
      fontSize: font_sizes.font_50,
    },
    h3: {
      fontSize: font_sizes.font_30,
      textTransform: "capitalize",
    },
    h4: {
      fontSize: font_sizes.font_24,
    },
    subtitle1: {
      fontSize: font_sizes.font_16,
    },
  },

  //overrides key of the theme to potentially change every single style injected by Material-UI into the DOM
  overrides: {
    MuiButton: {
      root: {
        borderRadius: 50,
      },
    },
    MuiFormLabel: {
      root: {
        color: color.wizard_box_colors.shadow_gray,
        fontSize: "18px",
        fontWeight: "300",
        lineHeight: "0.5",
        fontFamily: "gillsans_light",
      },
    },
    MuiFormControlLabel: {
      root: {
        textTransform: "capitalize",
      },
    },
    MuiStepLabel: {
      label: {
        color: `${color.primary_palette.franklin_purple} !important`,
        marginTop: "0px !important",
        "&:nth-child(2n)": {
          marginTop: "-30px",
          color: "red !important",
        },
      },
    },
    MuiInputAdornment: {
      root: {
        height: "auto",
        "&:focus": {
          outline: "none",
        },
      },
    },
    MuiInputLabel: {
      formControl: {
        color: color.primary_palette.franklin_purple,
      },
    },
    MuiGrid: {
      container: {
        outline: "inherit",
      },
    },
    MuiPaper: {
      root: {
        padding: "5px 0 !important",
      },
    },
    MuiOutlinedInput: {
      input: {
        textAlign: "center",
      },
    },
    MuiList: {
      padding: {
        padding: 0,
        "&:nth-child(1)": {
          position: "static",
          overflow: "auto",
          width: "100%",
          // height: "107px",
        },
      },
    },
    // MuiList: {
    //   padding: {
    //     "&:nth-child(1)": {
    //       position: "static",
    //       overflow: "auto",
    //       width: "100%",
    //       height: "180px"
    //     }
    //   }
    // },
    MuiListItem: {
      gutters: {
        paddingLeft: "8px !important",
        paddingRight: "8px !important",
      },
      root: {
        padding: `0px !important`,
      },
    },
    MuiPopover: {
      paper: {
        overflowY: "hidden",
      },
    },
    MuiMenu: {
      paper: {
        height: "auto",
        width: "85px",
        borderRadius: 0,
        border: `1px solid ${color.secondary_palette.grays.shadow_gray}`,
      },
    },
    MuiAutocomplete: {
      listbox: {
        height: "64px !important",
        padding: "0 0 !important",
      },
      paper: {
        borderRadius: 0,
        margin: 0,
        border: `1px solid ${color.secondary_palette.grays.shadow_gray}`,
      },
      option: {
        paddingTop: `0px !important`,
        paddingBottom: `0px !important`,
        paddingRight: `8px !important`,
        paddingLeft: `8px !important`,
      },
    },
  },
};

export const pxToRem = (px) => {
  return `${px / 16}rem`; //16px set as fontSize to html in index.css
};

export default tradework_theme;
