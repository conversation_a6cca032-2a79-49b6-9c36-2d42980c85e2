import moment from "moment";
import { groupBy } from "lodash";

const PHONE_NUMBER_FORMAT = /(\d{3})(\d{3})(\d{4})/;

// const getMonthIndex = (month = "") => {
//   return month && moment().month(month).format("M");
// };

// const getMonthNameFromDate = (date = "", format = "MMMM") => {
//   let monthNum = moment(date).month();
//   monthNum = monthNum < 1 ? 1 : monthNum;
//   return date && moment(monthNum, "MM").format(format);
// };

// const getYearFromDate = (date = "") => {
//   return date && moment(date).year();
// };

const getGroupedArray = (data, groupByKey) => {
  return groupBy(
    data.map((each) => {
      if (!each[groupByKey]) {
        return { ...each, [groupByKey]: "noParent" };
      }
      return each;
    }),
    groupByKey
  );
};

const formatPhoneNumber = (value) => {
  return value.replace(PHONE_NUMBER_FORMAT, "$1.$2.$3");
};

export {
  // getMonthIndex,
  // getMonthNameFromDate,
  // getYearFromDate,
  getGroupedArray,
  formatPhoneNumber,
};
