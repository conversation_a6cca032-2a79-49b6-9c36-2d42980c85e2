const strings = {
  general: {
    titles: {
      skip: "Ski<PERSON>",
      finish_later: "I’ll Finish Profile Later",
      save_continue: "Save & Continue",
      done: "Done!",
      join: "Join",
    },
    messages: {},
    errors: {},
  },
  image_text: {
    titles: {},
    alt_text: {
      cart_icon: "cart icon",
      happy_arrow: "Happy Arrow",
    },
    errors: {},
  },
  success_modal: {
    titles: {
      welcome: "welcome",
      pursue_your_dream_job: "Pursue your dream job with TradeWorks",
      begin: "let's begin",
    },
    messages: {},
    errors: {},
  },
  join_us: {
    titles: {
      tradeWorks: "TradeWorks",
      firstName: "First Name",
      lastName: "Last Name",
      email_address: "Email Address",
      password: "Password",
      phone_number: "Phone Number",
      i_am: "I'm a:",
      company: "Company",
      individual: "Individual",
      join: "JOIN",
      account_exists: "Already have a TradeWorks account?",
      sign_in: "Sign In",
      sign_up_fr: "By signing up, you agree to TW's",
      terms_services: "Terms of Service",
      profile_name: "Profile Name",
      popover_description:
        "This doesn’t have to be your legal name but what you choose to be called in the job market.",
    },
    messages: {
      option_login: "Optional for faster Log In",
      password_rule: "8 characters. 1 Upper/1 lower letter. 1 number or symbol",
      eight_chars: "8 characters ",
      one_letter: "1 Uppercase letter ",
      number_symbol: "1 number or symbol",
      success_message: "Welcome!. Pursue your dream job with TradeWorks.",
    },
    errors: {
      firstName_required: "First Name is required",
      firstName_min: "First Name should be at least 3 characters",
      firstName_max: "First Name cannot be more than 20 characters",
      lastName_required: "Last Name is required",
      lastName_min: "Last Name should be at least 3 characters",
      lastName_max: "Last Name cannot be more than 20 characters",
      email_address_required: "Email Address is required",
      invalid_email_address: "Please enter valid email address",
      password_required: "Password is required",
      password_max: "Password cannot be more than 20 characters",
      invalid_phone_number: "Invalid Phone Number",
    },
  },
  login: {
    titles: {
      forgot_your: "Forgot your",
      password: "password",
      or: "or",
      email: "email",
      not_member: "Not a member?",
      join_here: "Join Here",
      email_phone_number: "Email or Phone Number",
    },
    errors: {
      email_phone_required: "Email or Phone Number is required",
      invalid_email_phone: "Please enter valid email or phone number",
    },
  },
  landing_page: {
    titles: {
      log_in: "Sign In",
      join_us: "JOIN US",
      find_best_candidates: "FIND THE VERY BEST CANDIDATES",
      architecture_candidates: "Architecture Candidates",
      interior_design: "Interior Design Prospects",
      plumbing_heros: "Plumbing Heros",
      contractor_counsel: "Contractor Counsel",
      welcome: "WELCOME!",
      connect_with_community: "CONNECT WITH THE TRADE COMMUNITY",
      get_found: "GET FOUND",
      job_title: "JOB TITLE, COMPANY OR KEYWORDS",
      highlight_apprenticeships: "Highlight Apprenticeships",
      show_skills: "Show the world your skills",
      find_talent: "FIND TALENT",
      skills_jobs: "SKILLS, JOB TITLE OR KEYWORDS",
      appropriate_candidates: "Ping the most appropiate candidates",
      post_resume: "Post your resume",
      post_a_job: "Post a Job",
      footer_text: "2020 ® TradeWorks",
      hiring_companies: "HIRING COMPANIES",
      architectual_firms: "Architectual Firms",
      interior_design_firms: "Interior Design Firms",
      plumbing_outfits: "Plumbing Outfits",
      pursue_your_dream_job: "PURSUE YOUR DREAM JOB",
      architecture_positions: "Architecture Positions",
      interior_design_careers: "Interior Design Careers",
      plumbing_gigs: "Plumbing Gigs",
      contractor_openings: "Contractor Openings",
      placeholder_location: "city, state, zip",
      feedback_logo_text: "Beta Testin Feedback Logo",
    },
    messages: {},
    errors: {},
  },
  preview_post: {
    titles: {
      preview_post_title: "JUNIOR ARCHITECT with Classical Training",
      preview_post_title_des:
        "Come for the desing work, stay for the free gin.",
      preview_post_address: "Ledyard Partners LLC",
      preview_post_address1: "New York City, NY, 10021",
      posted_on: "Posted on: ",
      preview_post_position: "Junior Architect",
      easy_apply: "Easy Apply",
      preview_post_des:
        "An opportunity to work alongside and learn from icons within the New York architectual design community. A regularly featured member of Architectial Digest’s AD100 list, Ferguson & Shamamian had achieved iconic stature in architetural and rarified socials circles for its intellectual approach toward classicism as the basics of all great design. As a junio designer you will cultivate and develop the skills necessary to eventually lead and manage your own design team.",
      compensation: "COMPENSATION ",
      salary: "SALARY ",
      salary_amount: " $20,000 - $25,000 / yr, Full-Time, Apprenticeship",
      benefits: "BENEFITS ",
      benefits_detail: " Health Care, Day Care, Transportation",
      apprenticeship: "APPRENTICESHIP",
      internship: "INTERNSHIP ",
      required: "REQUIRED",
      Eeucation_level: "Education Level ",
      Eeucation_level_info: "Bachelor’s Degree ",
      trade_expertise: " Trade/Industry Expertise ",
      trade_expertise_info: "CAD, Drafting, Architectural Drawing ",
      general_skills: "General Skills ",
      general_skills_info:
        "Articulate, General Analysis, Multitasking, Time  Management, Drive a truck ",
      computer_literacy: "Computer Literacy ",
      computer_literacy_info:
        "ketch - Familiar, Cinema4D - Advanced; Zeplin - Familiar ",
      candidate_location: "Candidate location ",
      candidate_location_info: "New York, NY ",
      licenses: "Licenses/Certifications ",
      licenses_info: "Public Notary ",
      additional_skills: "Additional Skills ",
      additional_skills_info: "2 years of Ceramic Tile Fabrication ",
      personality_traits: "Personality Traits ",
      personality_traits_info: "Self-motivated ",
      creative_skills: "Creative Skills ",
      creative_skills_info: "Drawing : Advanced ",
      Preferred: "PREFERRED ",
      years_trade: "Years in Trade ",
      years_trade_info: "2-5 years as Architect ",
      degree: "Degree ",
      degree_info:
        "MArch/NAAB from accredited institution, or MA Urban Design ",
      Trade_industry: "Trade Industry Expertise ",
      Trade_industry_info:
        "Landmark Preservation - Familiar, LEED Certification Levels - Advanced, Sustainability - Expert ",
      computer_literacy_info2:
        "3D Capabilities - Basic; Archicad - Familiar; Revit - Proficient; Adobe CC - Medium",
      languages: "Languages ",
      languages_info: "Spanish - Fluent ",
      design_proclivities: "Design Proclivities ",
      design_proclivities_info:
        "Classical, contemporary, Colonial, Mid-Century Modern ",
      business_skills: "Business Skills ",
      business_skills_info: "Accounting - Basic ",
      clientele_experience: "Clientele Experience ",
      clientele_experience_info: "Goverment ",
      work_authorization: "Work Authorization ",
      work_authorization_info: "Authorized to work in the United States ",
      aplication_process: "APPLICATION PROCESS",
      resume_required: "Resume ",
      elevator_pitch: "Elevator Pitch ",
      robot_interview: "VideoBot Interview ",
      cover_letter: "Cover Letter ",
      contact_info: "CONTACT INFORMATION ",
      key_responsibilities: "KEY RESPONSIBILITIES & DAY-TO-DAY",
      key_responsibilities1:
        "Work with other team members on design and implementation of core platform technologies (integration with financial services, authentication, rules engines, data models, work flows, etc.)",
      key_responsibilities2:
        "Design, implement, and test external facing APIs to facilitate external integration with our platform",
      key_responsibilities3:
        "Integrate with SQL Server backend using a proprietary ORM",
      additional_notes: "Additional Notes",
      additional_notes1:
        "At least 3 years of significant C#, ASP.NET development experience",
      additional_notes2: "Solid understanding of MCV w/Razor",
      additional_notes3: "Solid command of HTML, CSS and JavaScript",
      additional_notes4:
        "In depth knowledge of relational databases, SQL server and ORM",
      additional_notes5:
        "Works well with a team but can also take responsibility for tasks and work independently",
      additional_notes6: "Adept at writing unit tests and testable codes",
      applu_company: "APPLY ON COMPANY SITE",
      venue: "Venue ",
      company_vibe: "Company Vibe ",
      company_size: "Company Size ",
      additionalperks: "Additional Perks ",
      office_snaps: "Office Snaps",
      water_cooler: "OUR PEOPLE",
      view_company: "VIEW COMPANY PROFILE",
      franklin_report: "Franklin report review ",
      company_details: "COMPANY DETAILS",
      done: "done",
      apply_now: "Apply now",
    },
  },
  loggin_header: {
    titles: {
      current_position: "CURRENT POSITION:",
      welcome: "WELCOME",
      get_found: "GET FOUND",
      find_talent: "FIND TALENT",
      search_jobs: "SEARCH JOBS",
      my_profile: "User Profile",
      active_employers: "MY JOB JOURNEY",
      post_jobs: "POST JOBS & COMPANY",
      manage_applicants: "FIND & MANAGE APPLICANTS",
      discover_candidates: "VIEW TW COMMUNITY",
      people: "People",
      job_title_name_company: "Job Title, Name, Company",
      city_state_zip: "City, State or Zip",
      advanced_search: "Advanced Search",
    },
    messages: {},
    errors: {},
  },
  wizard: {
    titles: {
      completed: "All steps completed",
      Reset: "Reset",
    },
    messages: {},
    errors: {},
  },
  add_trade: {
    titles: {
      thank_you: "THANK YOU FOR JOINING",
      develop: "Let’s develop your professional presence",
      main_trade: "MAIN TRADE",
      what_you_do: "What you ",
      do: " do",
      what_you_do_remain:
        ", not where you work.  You may add other trades later",
      location: "YOUR LOCATION",
      currently_reside: "Where you currently reside. City or ZIP code",
      not_usa: "Not USA?",
      student: "ARE YOU A STUDENT?",
      city_zip: "City or ZIP code ",
      Architecture: "Architecture",
      new_york: "New York, NY",
    },
    messages: {},
    errors: {},
  },
  add_education: {
    titles: {
      add_education: "Add Education",
      edu_describe: "Describe your current or most relevant institution.",
      add_school: "You may add other schools later.",
      institution: "Institution",
      subtitle: "Optional Subtitle",
      city_state: "City/ State",
      from: "From",
      not_usa: "Not USA?",
      expected_graduation: "To (Expected Graduation)",
      program_level: "Program Level",
      degree: "Degree",
      Major: "Major",
      minor: "Minor/Concentration",
      headline: "Headline",
      make_headline: "Make this my headline",
      highlights: "Highlights and Coursework",
      now_displayed:
        "Now displayed: Student at Columbia School of Architecture, Planning ... ",
      add_highlight: "Add Highlight",
      add_major: "Add Major",
      add_minor: "Add Minor",
      candidate: "Candidate, Master’s Degree in Planning",
      select_school: "Select or type your school",
      alpha_merit: "Alpha Ro Chi - Medal for Professional Merit",
      ex_architecture: "Ex: Architecture",
      ex_urban: "Ex: Urban Planning",
      highlight_professional: "Let’s develop your professional presence",
    },
    messages: {},
    errors: {},
  },
  add_exprience: {
    titles: {
      add_exprience: "add Experience",
      add_exprience_des:
        "What recent experience most closely aligns with your career goals?",
      company: "Company/Entity",
      company_name: "Your Company",
      entity: "Short Description of Entity",
      well_known: "(if not well known)",
      location: "Office Location",
      employment: "Employment Type",
      from: "From",
      month: "Month",
      year: "Year",
      to: "To",
      title: "Title",
      add_title: "Additional Title for this Experience",
      accomplishments: "Accomplishments",
      action: "Start each point with an ACTION verb. ",
      see_List: "See List",
      add_accomplishments: "Add Accomplishment",
      headline: "Headline",
      checkbox: "Make this my Profile Headline",
      headline_des:
        "Now displayed: Student at Columbia School of Architecture, Planning ... ",
      add_Media: "Add Related Media",
      add_link: "Add Link",
      add_file: "Add File",
      current_postion: "Current Position?",
      highlight_professional: "HIGHLIGHT YOUR PROFESSIONAL PROWESS",
      join_trade: "Join the Trade Community",
      highlight_credential: "HIGHLIGHT YOUR CREDENTIALS",
    },
    messages: {},
    errors: {},
  },
  add_file: {
    titles: {
      add_link: "ADD LINK",
      add_link_des: "This could be a website or a video link",
      add_link_placeholder_URL: "Type URL address here (yoursite.com)",
      add_another_link: "Upload",
      add_link_label: "Link Title",
      add_link_label_placeholder:
        "This will autofill from site or enter your own title",
      Page_Description: "Page Description",
      textArea_placeholder: "Come from site too.",
      Btn_title: "CAPTURE",
      add_file: "ADD FILE",
      add_file_des: "This could be a jpg, png, svg.",
      step_1: "Step 1 : Find the graphic",
      step_2: "Step 2 : Describe your image",
      step_2_label: "Displayed Caption",
      step_2_placeholder: "The Salas Hacienda - Montañita, Ecuador",
      step_2_textAear: "File Description",
      step_2_textAear_placeholder:
        "This 10 acres property is located near the coastal town of Montañita, in Ecuador.  The Salas family was very clear about their desire of keeping their new beach recidence in the same local style of open areas, large windows, soft woods and earth tones. ",
      step_3: "Step 3: Optional Link (Choose one)",
      step_3_radio_1: "No Link",
      step_3_radio_2: "URL Address",
      step_3_radio_2_input: "www.SalasHacienda.com",
      step_3_radio_3: "Upload PDF or Doc",
      add_file_btn: "DISPLAY",
    },
    messages: {},
    errors: {},
  },
  add_photoOp: {
    titles: {
      add_image_title: "Let’s add some images to your portfolio",
      add_image_des: "Profiles with headshots have a 27% more visibility",
      profile_link: "Upload Profile Photo or Logo",
      cover_link: "Upload Main Cover Photo",
      profile_text: "That’s a great profile picture!",
      profile_crop: "Let’s crop it to fit your portfolio",
      drag_img: "Drag the image to desired position.",
      drag_zoom: "Drag the zoom handle to change photo size.",
      choose_image: "Choose new image",
      cover_text: "That’s a beautiful cover picture ",
      cover_right: "Let’s get the proportions right",
      back_text: "You can go back and change these at any time!",
      change_profile: "Change Profile Photo or Logo",
      change_cover: "Change Main Cover Photo",
    },
    messages: {},
    errors: {},
  },

  // c package strings

  company_wizard: {
    step1: {
      titles: {
        businessTitle: "Tell the world about your Company",
        businessTitle_des:
          "Create engagement and cultivate a pipeline of qualified talent",
        address: "Address",
        location: "Location",
        city: "City",
        state: "State",
        zip: "Zip",
        email: "Company Email",
        website: "URL",
        phone: "Phone Number",
        headquarters: "Headquarters?",
        sample_company: "Sample Company Profile",
        your_company: "Your Company Profile",
        step1: "Step 1 • Your Business Card",
        step1_2: "Step 1 • ADD COMPANY HEADQUARTERS",
        step1_des:
          "Location is key! 65% of candidates say that location is their first priority",
        step1_des2: "This will be your public company information",
        add_office: "Add Office",
        step2: "About me",
        step2_2: "Step 2 • THIS IS ME",
        step2_des: "This will be your personal information for all TradeWorks",
        step2_des_1: "This will be your image for all of TradeWorks",
        my_email: "My Email ",
        my_phone: "My Personal Phone # ",
        my_office: "My Office",
        my_profile: "My Profile pic",
        additional_info: "Additional emails may be added later",
        additional_info_2:
          "Our primary way to reach you. You may add additional emails later in settings",
      },
      labels: {
        company_dropdown_top:
          "Please review the companies below to avoid duplicates",
        company_dropdown_bottom:
          "No Existing Company Profile? Just keep on typing.",
        ping_admin:
          "There is currently an admin for this company. You may request admin/ recruiting  privileges later in the sign up process. ",
        tm_ping_admin: "Please ping the Administrator ",
        tm_ping_admin2: "if you recommend any changes to your company profile ",
      },
    },
    step2: {
      titles: {
        main_des: "ENTICE CANDIDATES WITH YOUR STRENGTHS",
        main_brand: "Build your brand. Why is it exciting to work here?",
        step_2a: "Company Images",
        step_2a_des:
          "50% of job seekers say company image plays a critical role in deciding whether or not to apply. Bring your company’s story to life!",
        Company_logo: "Company Logo",
        Cover_Image: "Cover Image",
        Featured1: "Top Featured Image 1",
        Featured2: "Top Featured Image 2",
        step_2b: "Your Company Pitch",
        step_2b_des:
          "76% of full-time employees are open to new job possibilities!",
        Company_Snapshot: "Company Snapshot",
        Company_Snapshot_Lead: "Use industry keywords for better SEO searches",
        Company_Snapshot_placeholder:
          "Neoclassical, enduring, residential architectural design.",
        Company_Pitch: "Company Pitch",
        Year_Established: "Year Established",
        Employees: "# Employees",
        Clientele: "Clientele",
        design_proclivities: "Design Proclivities ",
        More_Trades: "More Trades?",
        third_Trade: "3rd Trade",
        Company_Accolades: "Company Accolades",
        Company_Vibe: "Company Vibe",
        venue: "Venue: Company Setting",
        Venu_des: "Venue: What best describes your company organization.",
        Company_Vibe_des:
          "Limited to 8 descriptors for greater impact and thoughtfulness. Includes process, positions, meetings, setting and attire.",
        College_Allegiances: "School Allegiances",
        College_Allegiances_des:
          "Check if you would like this publicly displayed; otherwise, will just be used to help sort the most promising candidates for you",
        Social_Media: "Social Media Feeds",
        Social_Media_des:
          "Engage prospective candidates with your style and ongoing successes",
        Company_url: "Company’s TradeWorks URL",
        change_url: "To change your custom URL, please go to your  ",
        Company_setting: "Company Settings",
        errorMsg: "Please fill required fields above",
        errorMsgLayout:
          "Please fill required fields above, or uncheck all the LAYOUTS to skip",
      },
      checkbox: {
        admin_a_1: "I confirm that I am currently employed by this company",
        admin_a_2:
          "Additionally, I am an official representative authorized to administer this company profile and act as a job recruiting contact, along with the existing Company Admin.",
        admin_b: "I confirm that I am currently employed by this",
        team_mem: "I confirm that I am a team member of this company",
      },
      recommended_350: "Recommended length of 350+ characters, Min of 60; ",
      messages: {},
      errors: {},
    },
    step3: {
      titles: {
        step3_title: "SHOWCASE YOUR TEAM IN ACTION",
        step3_title_des:
          "You can add still shots or videos of your company members to help show candidates what it’s truly like to work there. Be honest & concise, not overly salesy.",
        step3: "Step 3 • OUR PEOPLE",
        step3_des: "Showcase your brand In action",
        team_Member_Name: "Team Member Name*",
        team_Title: "Title",
        Video_Link: "Paste Video Link",
        Video_Summary: "Summary",
        Add_More_Videos: "Add More People",
      },
      messages: {},
      errors: {},
    },
    step4: {
      titles: {
        step4_title: "Spotlight your unique identity",
        step4_title_des:
          "Find the most appropriate candidates with candid shots",
        Step4: "Step 4 • Offices Shots ",
        step4_des: "Help applicants find their new home",
        offices: "You may change the order of these offices later",
      },
      messages: {},
      errors: {},
    },
    step5: {
      titles: {
        step5_title: "What distinguishes your company?",
        step5_title_des:
          "Excite & engage qualified candidates to follow your story",
        Step5: "Step 5 • Company Culture",
        step5_des:
          "Describe your mission and goals to attract the most appropriate applicants",
        step_A: "STEP A - PICK A LAYOUT (You may pick one of each)",
        step_B: "STEP B - ADD CONTENT",
        Highlight_Title: "Highlight Title",
        Highlight_Title_input:
          "Pet friendly office, It takes a village, etc...",
        Body_Content: "Body Content",
        Add_Image: "Add Image",
        Paragraph1: "Paragraph 1",
        Paragraph2: "Paragraph 2",
      },
      errors: {
        image:
          "Please add image or delete this section, to preserve the beauty of your Company Profile.",
      },
    },
  },
  admin_edit: {
    titles: {
      admin_edit: "What part of your profile would you like to edit?",
      click_Edit: "Hover to view section, click to edit",
      logo_cover: "Logo and Cover Photos",
      company_pitch: "Company Pitch",
      water_cooler: "Water Cooler",
      our_offices: "Our Offices",
      Company_highlights: "Company Culture",
      Current_team: "Current Team + Alumni",
    },
    messages: {},
    errors: {},
  },
  edit_office: {
    titles: {
      edit_office: "EDIT: OUR OFFICES",
      edit_officeText: "Reorder Office Blocks",
    },
  },
  modals: {
    titles: {
      thank_you: "Thank you!",
      joining_trade: "for joining our Trade network",
      thank_you_des1: "Help us match ideal candidates to their dream job.",
      thank_you_des2: "Please check your inbox to confirm your membership.",
      email_info: "Didn't get the email?",
      email_link: "Email Link Again",
      well_done: "Well done!",
      userName: "Ames/Adams Architects",
      thankyouFinish: "Thank you for working on your Company Profile.",
      thankyouFinish_des:
        "58% of Applicants read the Company Profiles of jobs they are considering. Thus, we recomend that you finish your profile.",
      edit_profile: " edit profile",
      go_to_profile: "GO TO PROFILE",
      ping_Admin: "PING ADMINISTRATOR",
      rename_your_company: "RENAME YOUR COMPANY",
      current_company_name: "Current Company Name",
      new_company_name: "New Main Company Name",
      thank_you: "THANK YOU",
      allow_24: "Please allow 24 hours for name change",
      check_box1: "Please add or edit my office address.",
      check_box2: "This content is not correct (please add a message below)",
      check_box3: "Please add me as an Administrator",
      check_box4: "Please remove me from this company as I no longer work here",
      check_box5:
        "Please remove me from this company as I prefer no to be included",
      message: "Message",
      your_name: "Your Name",
      your_email: "Your Email",
      Send: "Send",
      Submit: "Submit",
      Great: "Great",
      message_sent: "Message Sent",
      message_sent_des:
        "Please sign up now with one of the existing addresses until your Admin flips the switch.",
      message_sent_desc_public:
        "Thank you for your input. We look forward to connecting with you",
      message_to: "Message to",
      company_already: "This Company already exists. ",
      company_already_des1: "Please go back and add from dropdown.",
      company_already_des2: "To add a new office, ping your admin.",
      ok: "ok",
    },
  },
  gamification: {
    titles: {
      contribute: "• CONTRIBUTE TO THE TRADE COMMUNITY •",
      mission: "MISSION: COMPLETE COMPANY PROFILE",
      complete: " COMPLETE",
      office_shoots: "OFFICE SHOTS",
      Company_highlights: "COMPANY HIGHLIGHTS",
      water_cooler: "WATER COOLER",
      social_media: "SOCIAL MEDIA",
      completion_goals: "COMPLETION GOALS",
      points_earned: "POINTS EARNED ON THIS MISSION",
      activity: "Activity",
      Necessary: "Necessary to Complete",
      currently: "Currently Displayed",
      Points_per: "Points Per Displayed Activity",
      points_original: "Points Originally Earned",
      current_point: "Current Points",
      main_section: "MAIN SECTIONS",
      company_pitch: "COMPANY PITCH",
      current_total: "Your current total balance:",
      points_des:
        " Your points are time sensitive. They diminish as time goes by.",
      dilution: "Dilution Over Time",
      time: "Time",
      Credit: "Credit Remaining",
      levels: "Levels",
    },
  },
  upgarde: {
    titles: {
      form: "From: ",
      please_apply: "Please check all that apply",
      level_change: "LEvel Change Request",
      request_change: "Requested Change:",
      admin: "Admin",
      admin_des: "Assigns privileges and all else",
      hr: "HR",
      hr_des1: "Post Jobs",
      hr_des2: "Review Candidates",
      marketing: "Marketing",
      marketing_des: "Edit Company Profile",
      accounting: "Accounting",
      accounting_des: "Billing/Shipping",
      team_mem: "Team Member",
      team_mem_des: "Happy Camper",
      thank: "Thank you,",
      request: "REQUEST",
    },
  },
  companyCulture: {
    description_validation: "Minimum 100 characters",
    title_validation: "Required field",
  },
  settings: {
    titles: {
      heading_title: "My PERSONAL SETTINGS",
      settings: "My Settings",
      basic_info: "Basic Info",
      first_name: "First Name: ",
      facebook: "Facebook : ",
      twitter: "Twitter :",
      last_name: "Last Name: ",
      username: "Username:",
      primary_email: "Primary Email:",
      email: "Email:",
      phone: "Phone:",
      erase: "Erase",
      save: "Save",
      main_phone: "Main Phone:",
      default_flipbook: "My Default Flipbook URL:",
      username_tip: "Not required, but helpful to submit reviews",
      phone_tip:
        "Your phone number is requested as we may need to contact you to authenticate job, review or comment details.",
      privacy_setting: "Privacy Settings:",
      auth_options: "Authentication Options",
      mobile_auth:
        "We will text you to recover your username or email. This will go to",
      privacy_pref: "privacy preferences:",
      privacy_pref_desc:
        "Identity visibility for your Franklin Report reviews. ",
      real_name: "Real Name",
      show_city: "Show City",
      hide_city: "Hide City",
      password_cond: "8 characters. 1 Upper. 1 number or symbol",
      profile_pic_normal: "Upload Photo or Personal Emblem",
      fb_login_txt: "Use Facebook Photo",
      company_basics: "Company Basics",
      company_admin: "Company Administrators",
      new_admin_contact: "New Admin contact, created with enhanced portfolio:",
      add_company_profile: "Add Company",
      office_locations: "Office Locations",
      our_portfolio: "OUR PORTFOLIOS",
      our_portfolio_settings: "Our Portfolios",
      password: "Password:",
      enhanced_portfolios: "New Enhanced Portfolios",
      add_portfolios: "Add New Portfolio",
      portfolio_history: "Portfolio Purchase History",
      tradeWorks_company: "TradeWorks Company profile",
      personal_settings: "Personal Settings",
      comments_notifications: "Comments & Notifications",
      our_company_info: "our company info",
      company_members: "Company Members",
      franklin_report_portfolios: "Franklin report Portfolios",
      my_job_journey: "My Job Journey",
      notifications: "Notifications",
      log_out: "Log Out",
      admins: "Admins:",
      admins_text: "Angeline Mathew Tim Saia ( You) Eliza Spear",
      portfolio_name: "Portfolio Name:",
      status: "Status:",
      where_service: "Where and what services do you offer?",
      addBtn: "add",
      which_office: "Which office will be listed in this portfolio?",
      view_edit: "View/Edit This Portfolio",
      save_portfolio: "Update",
      portfolio_com: "Portfolio Completion",
      business_card: "Business Card",
      Profile_Cover: "Profile & Cover Image",
      elevator_pitch: "Elevator Pitch",
      projects_summary: "Projects Summary & Images",
      products_publications: "Products & Publications",
      vignettes: "Vignettes",
      invited_clients: "Invited Clients to View",
      tally: "Tally:",
      active: "Active",
      to_bill: "To Bill",
      principal_owner: "Principal or Owner Name(s)",
      primary_trade: "Primary Trade",
      secondary_trade: "Secondary Trade",
      more_trade: "More Trades?",
      your_establishment: "Year Established",
      employees: "# Employees",
      typical_cost: "Typical Cost of Project",
      clientele: "Clientele",
      venue: "Venue",
      licensed: "Licensed?",
      whom: "If yes, by whom?",
      yes: "Yes",
      no: "No",
      purchase_history: "PORTFOLIO PURCHASE HISTORY",
      add_admin: "ADD NEW ADMIN",
      enable_des: "Please call the Franklin Report to enable this feature.",
      enable_num: "************",
      enable_fun: "This will be an automatic function soon.",
      imonit: "I’m On It",
      company_basics_label: "COMPANY SETTINGS",
      edit_personal_info: "EDIT PERSONAL INFORMATION",
      add_social: "CONNECT",
      first_name: "First Name: ",
      last_name: "Last Name: ",
      username: "Username: ",
      email: "Email: ",
      phone: "Phone: ",
      add_office: "Add Office",
      location_des:
        "Any changes here will be reflected in your Franklin Report portfolio, and your TradeWorks company profile. The email and contact name in the Business Card on the left is used in your Portfolios, for new client",
      company_name: "company name",
      Headquarters: "Headquarters?",
      billing_address: "Billing Address?",
      ofc_nickname: "Office Nickname",
      display_where: "DISPLAYED WHERE?",
      services_offered: "SERVICES OFFERED?",
      userName_primary: "Username",
      save: "save",
      plus_company: "add company profile",
      visit_tw_community: "VISIT TW COMMUNITY",
    },
    messages: {},
    errors: {},
  },
  footer: {
    copy_right: "TradeWorks® Beta 1.0",
    forJobSeekers: "For Job Seekers",
    forEmployers: "For Employers",
    myAccount: "My Account",
    contactUs: "Contact Us",
    privacyPolicy: "Privacy Policy",
    visitFranklinReport: "Visit Franklin Report",
  },
  interviewTags: {
    requested: "INTERVIEW REQUESTED",
    upcoming: "UPCOMING INTERVIEW",
    seeAll: "SEE ALL",
  },
  share_Tw: {
    titles: {
      share_Tw_title: "SHARE A TW PROFILE",
      name: "Your Name",
      email: "Your Email",
      recipients: "Recipient Email",
      message: "Message",
      recipients_receive: "Recipients will receive a link to this TW Profile",
      send: "send",
    },
  },
  recommendation: {
    titles: {
      request_recommendation: "• REQUEST RECOMMENDATION •",
      first_name: "First Name",
      last_name: "Last Name",
      email: "Email",
      message: "Message",
      send_request: "SEND REQUEST",
      thank_you_recommendation: "Thank you so much for the great assist",
      please_tw: "Please check out TradeWorks",
      meet_job: "Meet Your Dream Job ",
      best_employee: "Best Employee, ever",
      go_tw: "GO TO TW",
      recomendation_letters: "• Recommendation letter •",
      from: "From: ",
      great: "GREAT",
      recomendation_letter_heading: "• Recommendation Letter •",
      hi_username: "Hi Bob!",
      thank_info:
        "Thank you so very much for offering to write a recommendation for <name>. It will increase their chances 32% in receiving an excellent job opportunity.",
      how_know: "How I know <firstName>: ",
      i_have: "I  ",
      i_have_placeholder: "Suggestions: worked with/have known/coached",
      tim_for: " <firstName> for",
      years: "years ",
      years_placeholder: "# years",
      where_we: "where we ",
      where_we_plaeholder: "name an accomplishment",
      place_placeholder: "at/in/with",
      place_placeholder_2: "Place",
      together: "together. ",
      i_was_info: "I was Tim’s",
      i_was_info_placeholder: "manager/associate/coach (& friend)",
      bit_info: "A bit about you:",
      currently: "Currently, I am ",
      currently_placeholder: "a/the",
      currently_placeholder_1: "MY Title",
      where_i: " , where I",
      at: "at",
      where_i_placeholder: "My Responsibilities",
      special_tim: "What is special about <firstName>?",
      please_help: "PLEASE HELP US Tag <firstName>'s strengths & Skills",
      artistic_abilities: "Artistic Abilities",
      computer_skills: "Computer Skills",
      Creativity: "Creativity",
      Drive: "Drive",
      Focus: "Focus",
      Fortitude: "Fortitude",
      Grit: "Grit",
      Hand_Skills: "Freehand Drawing",
      Intelligence: "Intelligence",
      Patience: "Patience",
      Precision: "Precision",
      Persistence: "Persistence",
      add_your_wn: "+ ADD YOUR OWN",
      Thank_consideration: "Thank you again for your time and consideration!",
      book_assist: "BOOK ASSIST",
      name_palceholder: "Your name",
      company_placeholder: "Your Current Company",
      strength_placeholder:
        "Complete sentences please: details on strength of character, hard work, intelligence, focus, creativity, patience, etc.",
      story_placeholder: "A small story about tim",
      add_your_wn_placeholder: "ADD YOUR OWN",
      recomendation_delete:
        "Are you sure you want to delete this recommendation later? You cannot undo this action",
      am_sure: "I AM SURE",
      recomendation_success:
        "Your Recommendation Letter request has been sent!",
    },
  },
};

export default strings;
