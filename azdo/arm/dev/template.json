{"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"subscriptionId": {"type": "string"}, "name": {"type": "string"}, "location": {"type": "string"}, "hostingEnvironment": {"type": "string"}, "hostingPlanName": {"type": "string"}, "serverFarmResourceGroup": {"type": "string"}, "alwaysOn": {"type": "bool"}, "WEBSITES_PORT": {"type": "int"}, "linuxFxVersion": {"type": "string"}, "dockerRegistryUrl": {"type": "string"}, "dockerRegistryUsername": {"type": "string"}, "dockerRegistryPassword": {"type": "securestring"}, "dockerRegistryStartupCommand": {"type": "string"}, "REACT_APP_SERVER_URL": {"type": "string"}}, "resources": [{"apiVersion": "2018-11-01", "name": "[parameters('name')]", "type": "Microsoft.Web/sites", "location": "[parameters('location')]", "tags": {}, "dependsOn": [], "properties": {"name": "[parameters('name')]", "siteConfig": {"appSettings": [{"name": "DOCKER_REGISTRY_SERVER_URL", "value": "[parameters('dockerRegistryUrl')]"}, {"name": "DOCKER_REGISTRY_SERVER_USERNAME", "value": "[parameters('dockerRegistryUsername')]"}, {"name": "DOCKER_REGISTRY_SERVER_PASSWORD", "value": "[parameters('dockerRegistryPassword')]"}, {"name": "WEBSITES_ENABLE_APP_SERVICE_STORAGE", "value": "false"}, {"name": "WEBSITES_PORT", "value": "[parameters('WEBSITES_PORT')]"}, {"name": "REACT_APP_SERVER_URL", "value": "[parameters('REACT_APP_SERVER_URL')]"}], "linuxFxVersion": "[parameters('linuxFxVersion')]", "appCommandLine": "[parameters('dockerRegistryStartupCommand')]", "alwaysOn": "[parameters('alwaysOn')]"}, "serverFarmId": "[concat('/subscriptions/', parameters('subscriptionId'),'/resourcegroups/', parameters('serverFarmResourceGroup'), '/providers/Microsoft.Web/serverfarms/', parameters('hostingPlanName'))]", "hostingEnvironment": "[parameters('hostingEnvironment')]", "clientAffinityEnabled": false}}]}