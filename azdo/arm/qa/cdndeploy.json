{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for all resources."}}, "storageAccountName": {"type": "string", "metadata": {"description": "Name of the Storage Account"}}, "cdnEndpointName": {"type": "string", "metadata": {"description": "Name of the CDN Endpoint"}}, "cdnProfileName": {"type": "string", "metadata": {"description": "Name of the CDN Profile"}}}, "resources": [{"type": "Microsoft.Storage/storageAccounts", "name": "[parameters('storageAccountName')]", "apiVersion": "2016-01-01", "location": "[parameters('location')]", "tags": {"displayName": "[parameters('storageAccountName')]"}, "kind": "Storage", "sku": {"name": "Standard_LRS"}, "properties": {}}, {"name": "[parameters('cdnProfileName')]", "type": "Microsoft.Cdn/profiles", "location": "[parameters('location')]", "apiVersion": "2016-04-02", "tags": {"displayName": "[parameters('cdnProfileName')]"}, "sku": {"name": "Standard_Akamai"}, "properties": {}, "resources": [{"apiVersion": "2016-04-02", "name": "[parameters('cdnEndpointName')]", "type": "endpoints", "dependsOn": ["[parameters('cdnProfileName')]", "[parameters('storageAccountName')]"], "location": "[parameters('location')]", "tags": {"displayName": "[parameters('cdnEndpointName')]"}, "properties": {"originHostHeader": "[replace(replace(reference(parameters('storageAccountName')).primaryEndpoints.blob,'https://',''),'/','')]", "isHttpAllowed": true, "isHttpsAllowed": true, "queryStringCachingBehavior": "IgnoreQueryString", "contentTypesToCompress": ["text/plain", "text/html", "text/css", "application/x-javascript", "text/javascript"], "isCompressionEnabled": true, "origins": [{"name": "origin1", "properties": {"hostName": "[replace(replace(reference(parameters('storageAccountName')).primaryEndpoints.blob,'https://',''),'/','')]"}}]}}]}], "outputs": {"hostName": {"type": "string", "value": "[reference(parameters('cdnEndpointName')).hostName]"}, "originHostHeader": {"type": "string", "value": "[reference(parameters('cdnEndpointName')).originHostHeader]"}}}