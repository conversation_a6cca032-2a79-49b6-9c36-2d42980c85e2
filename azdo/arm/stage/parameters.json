{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"subscriptionId": {"value": "3fe25129-0c45-4f60-a6ee-d8cee6cc0da9"}, "name": {"value": "tradeworks-cpack-stage-fe"}, "location": {"value": "East US"}, "hostingEnvironment": {"value": ""}, "hostingPlanName": {"value": "tra-asp-use-stage"}, "serverFarmResourceGroup": {"value": "azdo-arm-svc-rg"}, "alwaysOn": {"value": false}, "WEBSITES_PORT": {"value": 80}, "linuxFxVersion": {"value": "DOCKER|tradeworks.azurecr.io/tradeworks/cpackage/web:latest"}, "dockerRegistryUrl": {"value": "https://tradeworks.azurecr.io"}, "dockerRegistryUsername": {"value": "tradeworks"}, "dockerRegistryPassword": {"reference": {"keyVault": {"id": "/subscriptions/3fe25129-0c45-4f60-a6ee-d8cee6cc0da9/resourceGroups/azdo-arm-svc-rg/providers/Microsoft.KeyVault/vaults/tradeworksakv"}, "secretName": "tradeworks-acr-admin"}}, "dockerRegistryStartupCommand": {"value": ""}, "REACT_APP_SERVER_URL": {"value": "https://tradeworks-cpack-dev.azurewebsites.net/"}}}