trigger:
  - develop

jobs:
- job: Build
  displayName: Webpack Build
  pool:
    name: 'Menlo Self Hosted'
  workspace:
    clean: all
  steps:
  - task: Npm@1
    inputs:
      command: 'install'
      workingDir: '$(Build.Repository.LocalPath)'
  - task: Npm@1
    inputs:
      command: 'custom'
      workingDir: '$(Build.Repository.LocalPath)'
      customCommand: 'run build'
  - task: CopyFiles@2
    inputs:
      SourceFolder: '$(Build.Repository.LocalPath)/build/'
      Contents: '**'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/dist/'
      OverWrite: true
    displayName: 'Copying webpack dist contents to artifact staging directory'
  - task: CopyFiles@2
    inputs:
      SourceFolder: '$(Build.Repository.LocalPath)/azdo/arm/'
      Contents: '**'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/arm/'
      OverWrite: true
    displayName: 'Copying ARM Templates to artifact staging directory'
  - task: PublishBuildArtifacts@1
    inputs:
      PathToPublish: '$(Build.ArtifactStagingDirectory)/dist/'
      ArtifactName: 'dist'
      publishLocation: 'Container'
    displayName: 'Publishing webpack dist contents'
  - task: PublishBuildArtifacts@1
    inputs:
      PathToPublish: '$(Build.ArtifactStagingDirectory)/arm/'
      ArtifactName: 'arm'
      publishLocation: 'Container'
    displayName: 'Publishing ARM Templates'
  - task: PostBuildCleanup@3