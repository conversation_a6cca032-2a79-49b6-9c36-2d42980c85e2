{"name": "cpack", "version": "0.1.0", "private": true, "homepage": "/cpack", "dependencies": {"@material-ui/core": "^4.9.7", "@material-ui/lab": "^4.0.0-alpha.46", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.4.0", "@testing-library/user-event": "^7.2.1", "autosuggest-highlight": "^3.1.1", "axios": "^0.19.2", "moment": "^2.24.0", "react": "^16.12.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "^16.12.0", "react-easy-crop": "^3.0.1", "react-player": "^1.15.3", "react-redux": "^7.1.3", "react-router-dom": "^5.1.2", "react-scripts": "3.4.0", "react-slick": "^0.26.1", "react-sortable-hoc": "^1.11.0", "redux": "^4.0.5", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0"}, "scripts": {"start": "set PORT=3002 && react-scripts start", "build:env": "env-cmd -f .env react-scripts build", "build:staging": "env-cmd -f .env.staging react-scripts build", "build:prod": "react-scripts build", "build:dev": "env-cmd -f .env.development react-scripts build", "build": "react-scripts build", "sonar": "sonar-scanner", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"env-cmd": "^10.0.1", "sonar-scanner": "^3.1.0", "sonarqube-scanner": "^2.6.0"}}