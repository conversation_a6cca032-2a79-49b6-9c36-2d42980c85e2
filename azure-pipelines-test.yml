# Node.js with React
# Build a Node.js project that uses React.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript

trigger:
  - develop
pool:
  name: 'g360-tra'


steps:
- task: NodeTool@0
  inputs:
    versionSpec: '14.16.0'
  displayName: 'Install Node.js'

- script: |
    npm install
    npm run build:env
  displayName: 'npm install and build'

- task: CopyFiles@2
  displayName: 'Copy files'
  inputs:
    sourceFolder: '$(Build.Repository.LocalPath)/build' 
    Contents: '**/*'
    TargetFolder: '$(Build.ArtifactStagingDirectory)'
    cleanTargetFolder: true
    
- task: ArchiveFiles@2
  displayName: 'Archive files'
  inputs:
    rootFolderOrFile: '$(Build.ArtifactStagingDirectory)'
    includeRootFolder: false
    archiveType: zip
    archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
    replaceExistingArchive: true
    
- task: PublishBuildArtifacts@1
  displayName: 'Publish Build Artifacts'
  inputs: 
    pathtoPublish: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
