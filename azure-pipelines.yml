trigger:
  - develop
    
jobs:
- job: Build
  displayName: Dockerize the image and publish ARM templates
  pool:
    name: 'Menlo Self Hosted'
  workspace:
    clean: all
  steps:
  - task: Docker@2
    inputs:
      containerRegistry: 'tradeworks-acr'
      repository: 'tradeworks/cpackage/web'
      command: 'buildAndPush'
      Dockerfile: '**/Dockerfile'
      tags: |
        $(Build.BuildId)
    displayName: 'Build and push Docker image'
  - task: CopyFiles@2
    inputs:
      SourceFolder: '$(Build.Repository.LocalPath)/azdo/arm/'
      Contents: '**'
      TargetFolder: '$(Build.ArtifactStagingDirectory)/arm/'
      OverWrite: true
  - task: PublishBuildArtifacts@1
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)/arm/'
      ArtifactName: 'arm'
      publishLocation: 'Container'
    displayName: 'Publishing ARM templates'
  - task: PostBuildCleanup@3
